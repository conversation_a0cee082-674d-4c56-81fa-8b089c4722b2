package com.cosfo.message.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date : 2023/2/21 17:17
 */
/**
 * 关键词返回类型
 */
@AllArgsConstructor
@Getter
public enum RuleTypeEnum {

    THING("thing","事物"),
    NUMBER("number","数字"),
    LETTER("letter","字母"),
    SYMBOL("symbol","符号"),
    CHARACTER_STRING("character_string","事物"),
    TIME("time","时间"),
    DATE("date","日期"),
    AMOUNT("amount","金额"),
    PHONE_NUMBER("phone_number","电话"),
    CAR_NUMBER("car_number","车牌"),
    NAME("name","姓名"),
    PHRASE("phrase","汉字"),
    ENUM("enum","枚举值");

    private String value;
    private String content;

    /**
     * 获取内容
     * @param value
     * @return
     */
    public static String getContent(String value) {
        for (RuleTypeEnum ruleTypeEnum : RuleTypeEnum.values()) {
            if (Objects.equals(value, ruleTypeEnum.getValue())) {
                return ruleTypeEnum.getContent();
            }
        }
        return null;
    }
}
