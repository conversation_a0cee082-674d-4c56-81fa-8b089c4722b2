package com.cosfo.message.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.enums.base.Enum2Args;

public interface MsgNoticeEnum {
    @Getter
    @AllArgsConstructor
    enum SupportSwitch implements Enum2Args {
        ERROR(-1, "错误"),
        CLOST(0, "关闭"),
        OPEN(1, "开启"),
        ;
        private Integer value;
        private String content;

        public static MsgNoticeEnum.SupportSwitch of(Integer value) {
            for (MsgNoticeEnum.SupportSwitch e : MsgNoticeEnum.SupportSwitch.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return ERROR;
        }
    }
    @Getter
    @AllArgsConstructor
    enum PushStatus implements Enum2Args {
        ERROR(-1, "错误"),
        DRAFT(0, "草稿"),
        PUSHED(1, "已发布"),
        READY(2, "待发布"),
        ;
        private Integer value;
        private String content;

        public static MsgNoticeEnum.PushStatus of(Integer value) {
            for (MsgNoticeEnum.PushStatus e : MsgNoticeEnum.PushStatus.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return ERROR;
        }
    }
    @Getter
    @AllArgsConstructor
    enum PushType implements Enum2Args {
        ERROR(-1, "错误"),
        TIMING(0, "定时发布"),
        NOW(1, "立即发布"),
        /**
         * 发布状态0=定时发布,1=立即发布
         */
        ;
        private Integer value;
        private String content;

        public static MsgNoticeEnum.PushType of(Integer value) {
            for (MsgNoticeEnum.PushType e : MsgNoticeEnum.PushType.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return ERROR;
        }
    }
}
