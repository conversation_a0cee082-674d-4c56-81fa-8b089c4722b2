package com.cosfo.message.common.util;

import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.message.common.constants.WechatTemplateConstants;
import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;

import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date : 2023/2/27 7:26
 */
@Slf4j
public class WeiXinConnectionUtil {
    public static <T> T getWechatByUrl( String accessToken, T t, String url, String params) {
        HttpResponse response = null;
        if (StringUtils.isEmpty(params)){
            response = HttpUtil.createGet(url + "?access_token="+accessToken)
                .contentType(ContentType.APPLICATION_JSON.toString()).setConnectionTimeout (10000)
                    .setReadTimeout (10000).execute();

        }else {
            response = HttpUtil.createGet(url + "?access_token="+accessToken + params)
                    .contentType(ContentType.APPLICATION_JSON.toString()).setConnectionTimeout (10000)
                    .setReadTimeout (10000).execute();
        }
        String body = response.body();
        T vo = JSONObject.parseObject(body, (Type) t.getClass());
        return vo;
    }

    public static String getWechatResponseByUrl( String accessToken, String url, String params) {
        HttpResponse response = null;
        if (StringUtils.isEmpty(params)){
            response = HttpUtil.createGet(url + "?access_token="+accessToken)
                    .contentType(ContentType.APPLICATION_JSON.toString()).setConnectionTimeout (10000)
                    .setReadTimeout (10000).execute();

        }else {
            response = HttpUtil.createGet(url + "?access_token="+accessToken + params)
                    .contentType(ContentType.APPLICATION_JSON.toString()).setConnectionTimeout (10000)
                    .setReadTimeout (10000).execute();
        }
        String body = response.body();
        return body;
    }

    public static <T> T postWechatByUrl(Object o, T t, String access_token, String url){
        String json = JSON.toJSONString(o);
        StringEntity stringEntity = new StringEntity(json, StandardCharsets.UTF_8);
        stringEntity.setContentType(ContentType.APPLICATION_JSON.toString());
        cn.hutool.http.HttpResponse response = HttpUtil.createPost(url + "?access_token="+access_token)
                .contentType(ContentType.APPLICATION_JSON.toString())
                .body(json).timeout (3000).execute();
        String body = response.body();
        T wechatCreateTemplateVO = JSONObject.parseObject(body, (Type) t.getClass());
        return wechatCreateTemplateVO;
    }

    public static <T> T postWechatByUrl(String paramJson, Class<T> responseClass, String accessToken, String url) {
        log.info("发送微信请求，URL:{},param:{},accessToken:{}", url, paramJson, accessToken);
        cn.hutool.http.HttpResponse response = HttpUtil.createPost(url + "?access_token=" + accessToken)
                .contentType(ContentType.APPLICATION_JSON.toString())
                .body(paramJson).timeout (3000).execute();
        String body = response.body();
        log.info("发送微信请求响应，param:{},body:{}", paramJson, body);
        T wechatCreateTemplateVO = JSONObject.parseObject(body, responseClass);
        return wechatCreateTemplateVO;
    }

    public static String postWechatResponseByUrl(Object o, String access_token, String url){
        String json = JSON.toJSONString(o);
        StringEntity stringEntity = new StringEntity(json, StandardCharsets.UTF_8);
        stringEntity.setContentType(ContentType.APPLICATION_JSON.toString());
        cn.hutool.http.HttpResponse response = HttpUtil.createPost(url + "?access_token="+access_token)
                .contentType(ContentType.APPLICATION_JSON.toString())
                .body(json).timeout (3000).execute();
        String body = response.body();
        return body;
    }
}
