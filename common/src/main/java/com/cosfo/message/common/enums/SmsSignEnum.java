package com.cosfo.message.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 短信签名
 * <AUTHOR>
 * @date 2025-08-14
 */
@AllArgsConstructor
@Getter
public enum SmsSignEnum {

    /**
     * 帆台
     */
    FAN_TAI("帆台"),
    
    /**
     * 鲜沐科技
     */
    SUMMERFARM("鲜沐科技");

    /**
     * 提供商名称
     */
    private String sign;

    public static String getByTenantId(Long tenantId) {
        if(1L == tenantId){
            return SUMMERFARM.getSign ();
        }
        return FAN_TAI.getSign ();
    }
}
