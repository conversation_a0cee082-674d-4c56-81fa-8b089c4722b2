package com.cosfo.message.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

public interface MerchantStoreEnum {
    /**
     * 店铺类型
     */
    @Getter
    @AllArgsConstructor
    public enum Type {
        /**
         * 0-直营店
         */
        DIRECT(0, "直营店"),
        /**
         * 1-加盟店
         */
        JOINING(1, "加盟店"),
        /**
         * 2-托管店
         */
        MANAGED(2, "托管店");

        /**
         * 店铺类型编码
         */
        private Integer code;
        /**
         * 店铺类型描述
         */
        private String desc;

        /**
         * get status
         * @param desc
         * @return
         */
        public static Type getStatus(String desc) {
            for (Type type : Type.values()) {
                if (type.desc.equals(desc)) {
                    return type;
                }
            }

            return null;
        }

        /**
         * get desc
         * @param code
         * @return
         */
        public static String getDesc(Integer code) {
            for (Type type : Type.values()) {
                if (type.code.equals(code)) {
                    return type.getDesc();
                }
            }

            return null;
        }
    }

    /**
     * 状态
     */
    @Getter
    @AllArgsConstructor
    public enum Status {
        /**
         * 0-审核中
         */
        IN_AUDIT(0, "审核中"),
        /**
         * 1-审核成功
         */
        AUDIT_SUCCESS(1, "审核成功"),
        /**
         * 2-审核失败
         */
        AUDIT_FAIL(2, "审核失败"),

        CLOSE(3, "关店");

        /**
         * 状态类型编码
         */
        private Integer code;
        /**
         * 状态类型描述
         */
        private String desc;

        /**
         * 状态描述
         * @param code
         * @return
         */
        public static String getDesc(Integer code) {
            for (MerchantStoreEnum.Status statusEnum : MerchantStoreEnum.Status.values()) {
                if (Objects.equals(code, statusEnum.getCode())) {
                    return statusEnum.getDesc();
                }
            }
            return null;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum OperateStatus {

        /**
         * 开店
         */
        OPEN(1, "开店"),

        /**
         * 关店
         */
        CLOSE(2, "关店");

        /**
         * 状态类型编码
         */
        private Integer code;
        /**
         * 状态类型描述
         */
        private String desc;
    }

    /**
     * 门店查询状态
     */
    @Getter
    @AllArgsConstructor
    public enum QueryStatus {
        /**
         * 0-待审核
         */
        IN_AUDIT(0, "待审核"),
        /**
         * 1-经营中
         */
        AUDIT_SUCCESS(1, "经营中"),
        /**
         * 2-审核不通过
         */
        AUDIT_FAIL(2, "审核不通过"),
        /**
         * 3-已关店
         */
        CLOSE(3, "已关店");

        /**
         * 状态类型编码
         */
        private Integer code;
        /**
         * 状态类型描述
         */
        private String desc;

        /**
         * 状态描述
         * @param code
         * @return
         */
        public static String getDesc(Integer code) {
            for (MerchantStoreEnum.Status statusEnum : MerchantStoreEnum.Status.values()) {
                if (Objects.equals(code, statusEnum.getCode())) {
                    return statusEnum.getDesc();
                }
            }
            return null;
        }
    }
}
