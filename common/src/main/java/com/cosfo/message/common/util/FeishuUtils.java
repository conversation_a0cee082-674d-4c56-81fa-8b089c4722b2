package com.cosfo.message.common.util;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.message.common.constants.FeishuConstants;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.i18n.exception.I18nBizException;
import org.apache.http.entity.ContentType;

/**
 * @Author: fansongsong
 * @Date: 2023-07-13
 * @Description:
 */
@Slf4j
public class FeishuUtils {

    public static final Integer SUCCESS_CODE = 0;

    public static final Integer LIMIT_CODE = 230020;

    /**
     * 机器人对当前用户不可用
     */
    public static final Integer BOT_NO_AVAILABILITY_CODE = 230013;

    /**
     * 飞书发送单聊消息
     * @param o 入参
     * @param t 出参class类型
     * @param authorization tenant_access_token
     * @return
     */
    public static <T> T sendMessage(Object o, Class<T> t, String authorization) {
        String json = JSON.toJSONString(o);
        log.info("FeishuUtils.sendMessage 请求参数 json:{}", json);
        cn.hutool.http.HttpResponse response = HttpUtil.createPost(FeishuConstants.SEND_MESSAGE_URL + "?receive_id_type=user_id")
                .header("Authorization", "Bearer " + authorization)
                .contentType(ContentType.APPLICATION_JSON.toString())
                .timeout (3000)
                .body(json).execute();
        String body = response.body();
        log.info("FeishuUtils.sendMessage 响应 body:{}，", body);
        if (!isJSONValid(body)) {
            throw new I18nBizException ("发送飞书单聊消息响应内容异常:{0}" , body);
        }
        T vo = JSONObject.parseObject(body, t);
        return vo;
    }

    /**
     * 判定是否是json
     * @param jsonStr
     * @return
     */
    public static boolean isJSONValid(String jsonStr) {
        try {
            JSONObject.parseObject(jsonStr);
        } catch (JSONException ex) {
            try {
                JSONArray.parseArray(jsonStr);
            } catch (JSONException ex1) {
                return false;
            }
        }
        return true;
    }
}
