package com.cosfo.message.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.enums.base.Enum2Args;

/**
 * <AUTHOR>
 * @date : 2023/2/13 18:37
 */
public interface MsgSceneEnum {
    /**
     * 是否关联模板
     */
    @AllArgsConstructor
    @Getter
    enum MsgSceneStatus implements Enum2Args{
        DISABLED(0,"未关联"),
        ABLED(1,"关联");

        private Integer value;
        private String content;

    }

    /**
     * 创建成功/失败
     */
    @AllArgsConstructor
    @Getter
    enum CreateStatus implements Enum2Args{
        FAIL(0,"失败"),
        SUCCESS(1,"成功");

        private Integer value;
        private String content;

    }


    /**
     * 是否删除
     */
    @AllArgsConstructor
    @Getter
    enum AvailableStatus implements Enum2Args{
        DISABLED(0,"已删除"),
        ABLED(1,"生效中");

        private Integer value;
        private String content;

    }

    /**
     * 是否可用
     */
    @AllArgsConstructor
    @Getter
    enum SceneStatus implements Enum2Args{
        DISABLED(0,"不可用"),
        ABLED(1,"可用");

        private Integer value;
        private String content;

    }

}
