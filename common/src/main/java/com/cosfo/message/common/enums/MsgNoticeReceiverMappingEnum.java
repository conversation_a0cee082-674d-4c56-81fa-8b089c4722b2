package com.cosfo.message.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.enums.base.Enum2Args;

public interface MsgNoticeReceiverMappingEnum {
    @Getter
    @AllArgsConstructor
    enum Type implements Enum2Args {
        ERROR(-1, "错误"),
        STORE(0, "店铺"),
        ;
        private Integer value;
        private String content;

        public static MsgNoticeReceiverMappingEnum.Type of(Integer value) {
            for (MsgNoticeReceiverMappingEnum.Type e : MsgNoticeReceiverMappingEnum.Type.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return ERROR;
        }
    }
}
