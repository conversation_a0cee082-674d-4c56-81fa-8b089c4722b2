package com.cosfo.message.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.enums.base.Enum2Args;

public interface MsgSendLogEnum {
    @AllArgsConstructor
    @Getter
    enum ChannleType implements Enum2Args {
        ERROR(-1, "错误"),
        WECHAT(0, "微信服务通知"),
        SELF_SYSTEM(1, "小程序内部消息通知"),
        FEISHU_SYSTEM(2, "飞书消息通知"),
        BASIC_NOTIFY(3, "帆台常规基础通知")
        ;
        private Integer value;
        private String content;

        public static MsgSendLogEnum.ChannleType of(Integer value) {
            for (MsgSendLogEnum.ChannleType e : MsgSendLogEnum.ChannleType.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return ERROR;
        }
    }
    @AllArgsConstructor
    @Getter
    enum ContentType implements Enum2Args {
        ERROR(-1, "错误"),
        NOTIC(0, "公告消息"),
        ;
        private Integer value;
        private String content;

        public static MsgSendLogEnum.ContentType of(Integer value) {
            for (MsgSendLogEnum.ContentType e : MsgSendLogEnum.ContentType.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return ERROR;
        }
    }
    @AllArgsConstructor
    @Getter
    enum SendStatus implements Enum2Args {
        ERROR(-1, "错误"),
        SUCCESS(0, "成功"),
        FAIL(1, "失败"),
        PROCESSING(2, "消息处理中"),
        ;
        private Integer value;
        private String content;

        public static MsgSendLogEnum.SendStatus of(Integer value) {
            for (MsgSendLogEnum.SendStatus e : MsgSendLogEnum.SendStatus.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return ERROR;
        }
    }
    @AllArgsConstructor
    @Getter
    enum ReadStatus implements Enum2Args {
        ERROR(-1, "错误"),
        UNREAD(0, "未读"),
        READED(1, "已读"),
        ;
        private Integer value;
        private String content;

        public static MsgSendLogEnum.ReadStatus of(Integer value) {
            for (MsgSendLogEnum.ReadStatus e : MsgSendLogEnum.ReadStatus.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return ERROR;
        }
    }
    @AllArgsConstructor
    @Getter
    enum TemplateType implements Enum2Args {
        ERROR(-1, "错误"),
        WECHAT(0, "微信"),
        ;
        private Integer value;
        private String content;

        public static MsgSendLogEnum.TemplateType of(Integer value) {
            for (MsgSendLogEnum.TemplateType e : MsgSendLogEnum.TemplateType.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return ERROR;
        }
    }
}
