package com.cosfo.message.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.enums.base.Enum2Args;

public interface MsgReadLogEnum {
    @Getter
    @AllArgsConstructor
    enum ActionType implements Enum2Args {
        ERROR(-1, "错误"),
        READ(0, "浏览"),
        SUPPORT(1, "点赞"),
        CANCEL_SUPPORT(2, "取消点赞"),
        ;
        private Integer value;
        private String content;

        public static ActionType of(Integer value) {
            for (ActionType e : ActionType.values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return ERROR;
        }
    }
}
