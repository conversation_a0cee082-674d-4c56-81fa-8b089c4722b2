//package com.cosfo.message.common.middleware;
//
//import org.apache.rocketmq.spring.core.RocketMQTemplate;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//
//@Component
//public class MQOperator {
//
//    @Resource
//    private RocketMQTemplate rocketMQTemplate;
//
//    public void sendDataToQueue(String queueKey, String data) {
//        rocketMQTemplate.convertAndSend(queueKey, data);
//    }
//
//    public void sendDataToQueue(String queueKey, Object data) {
//        rocketMQTemplate.convertAndSend(queueKey, data);
//    }
//}
