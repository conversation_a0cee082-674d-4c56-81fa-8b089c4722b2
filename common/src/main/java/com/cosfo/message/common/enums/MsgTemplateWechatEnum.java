package com.cosfo.message.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.enums.base.Enum2Args;

import java.util.Objects;

public interface MsgTemplateWechatEnum {

    /**
     * 创建成功/失败
     */
    @AllArgsConstructor
    @Getter
    public enum SuccessFlag implements Enum2Args {
        FAIL(0, "失败"),
        SUCCESS(1, "成功");

        private Integer value;
        private String content;

    }

    /**
     * 是否可用
     */
    @AllArgsConstructor
    @Getter
    public enum AvailableStatus implements Enum2Args {
        DISABLED(0, "不可用"),
        ABLED(1, "可用");

        private Integer value;
        private String content;

    }

    /**
     * 模板类型
     */
    @AllArgsConstructor
    @Getter
    public enum TemplateType implements Enum2Args{
        WECHAT(1,"微信");

        private Integer value;
        private String content;

    }


    /**
     * 是否成功响应
     */
    @AllArgsConstructor
    @Getter
    public enum WechatErrorCode implements Enum2Args{
        SUCCESS(0,"成功"),
        BUSY(-1,"系统繁忙，此时请开发者稍候再试"),
        ACCESS_TOKEN(40001, "获取 access_token 时 AppSecret 错误，或者 access_token 无效"),
        OVER_FLOW(200012,"个人模板数已达上限，上限25个"),
        TID_ERROR(200014,"模板 tid 参数错误"),
        KID_ERROR(200020,"关键词列表 kidList 参数错误"),
        SCENE_ERROR(200021,"场景描述 sceneDesc 参数错误"),
        ALREADY_ADD(200022,"模板已添加，请勿重复添加");

        private Integer value;
        private String content;

        /**
         * get WechatErrorCode
         * @param value
         * @return
         */
        public static WechatErrorCode getWechatErrorCode(Integer value) {
            for (WechatErrorCode status : WechatErrorCode.values()) {
                if (status.value.equals(value)) {
                    return status;
                }
            }
            return BUSY;
        }
    }
    /**
     * 微信模板查询条件
     */
    @AllArgsConstructor
    @Getter
    public enum WechatQueryCode implements Enum2Args{
        WECHAT_TEMPLATE_TYPE(0,"微信模板");

        private Integer value;
        private String content;

    }


}
