<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.1.RELEASE</version>
    </parent>
    <groupId>com.cosfo</groupId>
    <artifactId>message</artifactId>
    <packaging>pom</packaging>
    <version>1.0-RELEASE</version>


    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <app.version>1.0-RELEASE</app.version>
        <xianmu-rocket-mq.version>1.2.0</xianmu-rocket-mq.version>
        <authentication-client.version>1.1.13</authentication-client.version>
        <app.client.version>1.3.1-RELEASE</app.client.version>


        <xianmu-common.version>1.1.15-RELEASE</xianmu-common.version>
        <xianmu-dubbo.version>1.0.14-RELEASE</xianmu-dubbo.version>
        <xianmu.i18n.sdk.version>1.0.2-RELEASE</xianmu.i18n.sdk.version>
        <xianmu-task-support.version>1.0.5</xianmu-task-support.version>
        <oms-client.version>1.0.5-RELEASE</oms-client.version>
        <usercenter-client.version>1.0.3-RELEASE</usercenter-client.version>

        <fastjson.version>1.2.83</fastjson.version>
        <lombok.version>1.18.2</lombok.version>
        <starter.version>2.1.1</starter.version>
        <mysql-connector.version>8.0.22</mysql-connector.version>
        <druid.version>1.1.20</druid.version>
        <easyexcel.version>3.1.0</easyexcel.version>
        <dubbo-registry-nacos.version>2.7.15</dubbo-registry-nacos.version>
        <redisson.version>3.11.1</redisson.version>
        <mybatis-plus.version>3.4.0</mybatis-plus.version>
        <velocity.version>2.2</velocity.version>
        <hutool.version>5.7.22</hutool.version>
        <xianmu-log.version>1.0.14-RELEASE</xianmu-log.version>
        <xianmu-rocketmq.version>1.1.6</xianmu-rocketmq.version>
        <guava.version>19.0</guava.version>
        <caffeine.version>2.7.0</caffeine.version>
        <nacos-config.version>0.2.10</nacos-config.version>

    </properties>

    <dependencyManagement>
        <!-- ~~~~~~~~~~~~~~~~~~~~~~~自己的包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
        <dependencies>
            <dependency>
                <groupId>com.cosfo</groupId>
                <artifactId>message-common</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cosfo</groupId>
                <artifactId>message-application</artifactId>
                <version>${app.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cosfo</groupId>
                <artifactId>message-infrastructure</artifactId>
                <version>${app.version}</version>
            </dependency>

            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~自己的包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->


            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~二方包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-log-support</artifactId>
                <version>${xianmu-log.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.common</groupId>
                <artifactId>xianmu-common</artifactId>
                <version>${xianmu-common.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.i18n</groupId>
                <artifactId>xianmu-i18n-sdk</artifactId>
                <version>${xianmu.i18n.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-rocketmq-support</artifactId>
                <version>${xianmu-rocketmq.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-dubbo-support</artifactId>
                <version>${xianmu-dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-task-support</artifactId>
                <version>${xianmu-task-support.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cosfo</groupId>
                <artifactId>oms-client</artifactId>
                <version>${oms-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cosfo</groupId>
                <artifactId>message-client</artifactId>
                <version>${app.client.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-rocketmq-support</artifactId>
                <version>${xianmu-rocket-mq.version}</version>
            </dependency>
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>authentication-client</artifactId>
                <version>${authentication-client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.mybatis</groupId>
                        <artifactId>mybatis</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--    用户中心-->
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>usercenter-client</artifactId>
                <version>${usercenter-client.version}</version>
            </dependency>
            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~二方包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->



            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~三方包开始~~~~~~~~~~~~~~~~~~~~~~~~~ -->
            <!-- 数据库组件——mysql连接组件 -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector.version}</version>
                <scope>runtime</scope>
            </dependency>
            <!-- alibaba开源数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <!-- 注册中心 -->
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-registry-nacos</artifactId>
                <version>${dubbo-registry-nacos.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>nacos-config-spring-boot-starter</artifactId>
                <version>${nacos-config.version}</version>
            </dependency>
            <!-- redis依赖 -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
            <version>${velocity.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>
            <!-- rocket mq -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>2.3.1.RELEASE</version>
            </dependency>
            <!-- alibaba json -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <!--  lombok  -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>${caffeine.version}</version>
            </dependency>
            <dependency>
                <artifactId>okio</artifactId>
                <groupId>com.squareup.okio</groupId>
                <version>1.17.2</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>5.3.2</version>
            </dependency>
            <!--aliyun 短信sdk-->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dysmsapi20170525</artifactId>
                <!-- 请将 'the-latest-version' 替换为最新版本号：https://mvnrepository.com/artifact/com.aliyun/dysmsapi20170525 -->
                <version>4.1.2</version>
            </dependency>
            <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~三方包结束~~~~~~~~~~~~~~~~~~~~~~~~~ -->
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.2.1</version>
                <configuration>
                    <propertyFile>archetype.properties</propertyFile>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.6.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.0.2</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>

<modules>
    <module>application</module>
    <module>infrastructure</module>
    <module>common</module>
  </modules>
</project>
