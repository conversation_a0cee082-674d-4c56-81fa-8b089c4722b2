package com.cosfo.message.infrastructure.message.dao.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.message.common.enums.MsgSendLogEnum;
import com.cosfo.message.infrastructure.base.dto.PageQueryDTO;
import com.cosfo.message.infrastructure.message.dao.MsgSendLogDao;
import com.cosfo.message.infrastructure.message.dto.MsgBatchUpdateSendLogDTO;
import com.cosfo.message.infrastructure.message.dto.MsgSendLogQueryDTO;
import com.cosfo.message.infrastructure.message.dto.NotifyMessageDTO;
import com.cosfo.message.infrastructure.message.mapper.MsgSendLogMapper;
import com.cosfo.message.infrastructure.message.model.MsgSendLog;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <p>
 * 消息发送记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-07
 */
@Service
public class MsgSendLogDaoImpl extends ServiceImpl<MsgSendLogMapper, MsgSendLog> implements MsgSendLogDao {


    @Override
    public Long getBeforeNoticeId(Long uId, Long noticeId, Long tenantId) {
        List<MsgSendLog> msgSendLogs = list4BeforeAfter(uId, noticeId, tenantId);
        Optional<MsgSendLog> first = msgSendLogs.stream().findFirst();

        LambdaQueryWrapper<MsgSendLog> wrapper = new LambdaQueryWrapper();
        if(first.isPresent()) {
            wrapper.gt(MsgSendLog::getId, first.get().getId());
        }
        wrapper.eq(MsgSendLog::getSendStatus,MsgSendLogEnum.SendStatus.SUCCESS.getValue());
        wrapper.eq(MsgSendLog::getContentType,MsgSendLogEnum.ContentType.NOTIC.getValue());
        wrapper.eq(MsgSendLog::getChannleType,MsgSendLogEnum.ChannleType.SELF_SYSTEM.getValue());
        wrapper.eq(MsgSendLog::getReceiveAccountId,uId);
        wrapper.eq(MsgSendLog::getTenantId,tenantId);
        wrapper.last("limit 1");
        wrapper.orderByAsc(MsgSendLog::getId);
        MsgSendLog one = getOne(wrapper);
        if(ObjectUtils.isNotEmpty(one)){
            return one.getPageId();
        }
        return null;
    }

    @Override
    public Long getAfterNoticeId(Long uId, Long noticeId, Long tenantId) {
        List<MsgSendLog> msgSendLogs = list4BeforeAfter(uId, noticeId, tenantId);
        Optional<MsgSendLog> first = msgSendLogs.stream().findFirst();

        LambdaQueryWrapper<MsgSendLog> wrapper = new LambdaQueryWrapper();
        if(first.isPresent()) {
            wrapper.lt(MsgSendLog::getId, first.get().getId());
        }
        wrapper.eq(MsgSendLog::getSendStatus,MsgSendLogEnum.SendStatus.SUCCESS.getValue());
        wrapper.eq(MsgSendLog::getContentType,MsgSendLogEnum.ContentType.NOTIC.getValue());
        wrapper.eq(MsgSendLog::getChannleType,MsgSendLogEnum.ChannleType.SELF_SYSTEM.getValue());
        wrapper.eq(MsgSendLog::getReceiveAccountId,uId);
        wrapper.eq(MsgSendLog::getTenantId,tenantId);
        wrapper.last("limit 1");
        wrapper.orderByDesc(MsgSendLog::getId);
        MsgSendLog one = getOne(wrapper);
        if(ObjectUtils.isNotEmpty(one)){
            return one.getPageId();
        }
        return null;
    }

    @Override
    public IPage<MsgSendLog> pageSendLog(MsgSendLogQueryDTO dto, PageQueryDTO pageQueryDTO) {
        LambdaQueryWrapper<MsgSendLog> wrapper = builderQuerySendLog(dto);
        return page(new Page<>(pageQueryDTO.getPageIndex(),pageQueryDTO.getPageSize()),wrapper);
    }

    @Override
    public Integer countSendLog(MsgSendLogQueryDTO dto) {
        LambdaQueryWrapper<MsgSendLog> wrapper = getMsgSendLogLambdaQueryWrapper(dto);
        return count(wrapper);
    }

    private LambdaQueryWrapper<MsgSendLog> builderQuerySendLog(MsgSendLogQueryDTO dto) {
        LambdaQueryWrapper<MsgSendLog> wrapper = getMsgSendLogLambdaQueryWrapper(dto);
        wrapper.orderByDesc(MsgSendLog::getId);
        return wrapper;
    }

    private LambdaQueryWrapper<MsgSendLog> builderQuerySendLogOrderByOptimize(MsgSendLogQueryDTO dto) {
        LambdaQueryWrapper<MsgSendLog> wrapper = getMsgSendLogLambdaQueryWrapper(dto);
        wrapper.orderByDesc(MsgSendLog::getCreateTime);
        return wrapper;
    }

    private LambdaQueryWrapper<MsgSendLog> getMsgSendLogLambdaQueryWrapper(MsgSendLogQueryDTO dto) {
        LambdaQueryWrapper<MsgSendLog> wrapper = new LambdaQueryWrapper();
        if (Objects.nonNull(dto.getSendStatus())) {
            wrapper.eq(MsgSendLog::getSendStatus, dto.getSendStatus());
        }
        if (Objects.nonNull(dto.getContentType())) {
            wrapper.eq(MsgSendLog::getContentType, dto.getContentType());
        }
        if (Objects.nonNull(dto.getChannleType())) {
            wrapper.eq(MsgSendLog::getChannleType, dto.getChannleType());
        }
        wrapper.eq(MsgSendLog::getTenantId, dto.getTenantId());
        wrapper.eq(ObjectUtils.isNotEmpty(dto.getReadStatus()), MsgSendLog::getReadStatus, dto.getReadStatus());
        wrapper.gt(ObjectUtils.isNotEmpty(dto.getStartTime()), MsgSendLog::getCreateTime, dto.getStartTime());
        wrapper.lt(ObjectUtils.isNotEmpty(dto.getEndTime()), MsgSendLog::getCreateTime, dto.getEndTime());
        if (Objects.nonNull(dto.getReceiveAccountId())) {
            wrapper.eq(MsgSendLog::getReceiveAccountId, dto.getReceiveAccountId());
        }
        return wrapper;
    }

    @Override
    public void updateReadStatusByUidAndNoticeId(Long uId, Long noticeId, Long tenantId, Integer readStatus) {
        LambdaUpdateWrapper<MsgSendLog> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(MsgSendLog::getReadStatus,readStatus);
        wrapper.set(MsgSendLog::getReadTime, LocalDateTime.now());
        if (MsgSendLogEnum.ReadStatus.READED.getValue().equals(readStatus)) {
            wrapper.eq(MsgSendLog::getReadStatus, MsgSendLogEnum.ReadStatus.UNREAD.getValue());
        }
        wrapper.eq(MsgSendLog::getReceiveAccountId,uId);
        wrapper.eq(MsgSendLog::getPageId, noticeId);
        wrapper.eq(Objects.nonNull(tenantId), MsgSendLog::getTenantId, tenantId);
        update(wrapper);
    }

    @Override
    public void deleteByPageId(Long pageId,Integer contentType) {
        LambdaUpdateWrapper<MsgSendLog> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MsgSendLog::getPageId,pageId);
        wrapper.eq(MsgSendLog::getContentType,contentType);
        remove(wrapper);
    }

    @Override
    public Boolean batchUpdateReadStatus(MsgBatchUpdateSendLogDTO msgBatchUpdateSendLogDTO) {
        LambdaUpdateWrapper<MsgSendLog> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(MsgSendLog::getReadStatus, msgBatchUpdateSendLogDTO.getReadStatus());
        if (MsgSendLogEnum.ReadStatus.READED.getValue().equals(msgBatchUpdateSendLogDTO.getReadStatus())) {
            wrapper.set(MsgSendLog::getReadTime, LocalDateTime.now());
            wrapper.eq(MsgSendLog::getReadStatus, MsgSendLogEnum.ReadStatus.UNREAD.getValue());
        }
        wrapper.eq(MsgSendLog::getTenantId, msgBatchUpdateSendLogDTO.getTenantId());
        wrapper.eq(Objects.nonNull(msgBatchUpdateSendLogDTO.getContentType()), MsgSendLog::getContentType, msgBatchUpdateSendLogDTO.getContentType());
        wrapper.eq(MsgSendLog::getChannleType, msgBatchUpdateSendLogDTO.getChannleType());
        // 为空,该租户下全部已读
        wrapper.in(CollectionUtils.isNotEmpty(msgBatchUpdateSendLogDTO.getIds()), MsgSendLog::getId, msgBatchUpdateSendLogDTO.getIds());
        return update(wrapper);
    }

    @Override
    public MsgSendLog queryMessageByUniqueId(Long tenantId, Integer channleType, Integer contentType, String bizUniqueNo) {
        LambdaQueryWrapper<MsgSendLog> wrapper = new LambdaQueryWrapper();
        wrapper.eq(MsgSendLog::getTenantId, tenantId);
        wrapper.eq(MsgSendLog::getChannleType, channleType);
        wrapper.eq(MsgSendLog::getBizUniqueNo, bizUniqueNo);
        wrapper.eq(MsgSendLog::getContentType, contentType);
        wrapper.last("limit 1");
        return getOne(wrapper);
    }

    @Override
    public List<MsgSendLog> listSendLog(MsgSendLogQueryDTO dto) {
        LambdaQueryWrapper<MsgSendLog> wrapper = builderQuerySendLog(dto);
        wrapper.last("limit 20");
        return list(wrapper);
    }

    @Override
    public Boolean batchSuccessSendStatus(MsgSendLogQueryDTO msgSendLogQueryDTO, List<Long> idList) {
        if(CollectionUtils.isEmpty(idList)){
            return false;
        }
        // 更新已读状态
        LambdaUpdateWrapper<MsgSendLog> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(MsgSendLog::getSendStatus, MsgSendLogEnum.SendStatus.SUCCESS.getValue());
        wrapper.eq(MsgSendLog::getTenantId, msgSendLogQueryDTO.getTenantId());
        wrapper.eq(MsgSendLog::getChannleType, msgSendLogQueryDTO.getChannleType());
        wrapper.in(MsgSendLog::getId, idList);
        return update(wrapper);
    }

    @Override
    public IPage<MsgSendLog> pageSendLogOrderByOptimize(MsgSendLogQueryDTO dto, PageQueryDTO pageQueryDTO) {
        LambdaQueryWrapper<MsgSendLog> wrapper = builderQuerySendLogOrderByOptimize(dto);
        return page(new Page<>(pageQueryDTO.getPageIndex(),pageQueryDTO.getPageSize()),wrapper);
    }

    private List<MsgSendLog> list4BeforeAfter(Long uId,Long noticeId, Long tenantId){
        LambdaQueryWrapper<MsgSendLog> wrapper = new LambdaQueryWrapper();
        wrapper.eq(MsgSendLog::getSendStatus,MsgSendLogEnum.SendStatus.SUCCESS.getValue());
        wrapper.eq(MsgSendLog::getContentType,MsgSendLogEnum.ContentType.NOTIC.getValue());
        wrapper.eq(MsgSendLog::getChannleType,MsgSendLogEnum.ChannleType.SELF_SYSTEM.getValue());
        wrapper.eq(MsgSendLog::getReceiveAccountId,uId);
        wrapper.eq(MsgSendLog::getPageId,noticeId);
        wrapper.eq(MsgSendLog::getTenantId,tenantId);
        wrapper.orderByDesc(MsgSendLog::getId);
        return list(wrapper);
    }

    @Override
    public boolean updateMsgLogResp(MsgSendLog msgSendLog, Object resp, boolean success) {
        MsgSendLog updateMsgSendLog = new MsgSendLog();
        updateMsgSendLog.setId(msgSendLog.getId());
        updateMsgSendLog.setResp(JSON.toJSONString(resp));
        updateMsgSendLog.setSendStatus(success ? MsgSendLogEnum.SendStatus.SUCCESS.getValue() : MsgSendLogEnum.SendStatus.FAIL.getValue());
        return updateById(updateMsgSendLog);
    }
}
