package com.cosfo.message.infrastructure.notice.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cosfo.message.infrastructure.base.dto.PageQueryDTO;
import com.cosfo.message.infrastructure.notice.dto.ReadOrSupportLogQueryDTO;
import com.cosfo.message.infrastructure.notice.model.MsgReadLog;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 公告阅读点赞记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-07
 */
public interface MsgReadLogDao extends IService<MsgReadLog> {
    /**
     * 查询某用户对公告的浏览/点赞记录
     *
     * @param uId
     * @param noticId
     * @param tenantId
     * @return
     */
    List<MsgReadLog> listByUIdAndNoticId(Long uId, Long noticId, Long tenantId);

    /**
     * 查询某条公告的读取点赞情况，只做数量统计，如需详情需要进行分页查询
     *
     * @return
     */
    Map<Long, Long> countByNoticIdsAndActionType(List<Long> notidIds, Integer actionType);
    /**
     * 查询某条公告的读取点赞 详情需要进行分页查询
     *
     * @return
     */
    Map<Long, List<MsgReadLog>> listByNoticIdsAndActionType(List<Long> notidIds, Integer actionType);

    /**
     * 分页查询
     * @param queryDTO
     * @param pageQueryDTO
     * @return
     */
    IPage<MsgReadLog> pageByCondition(ReadOrSupportLogQueryDTO queryDTO, PageQueryDTO pageQueryDTO);

    /**
     * 根据公告id删除发送记录
     * @param id
     */
    void deleteByContentId(Long id);
}