package com.cosfo.message.infrastructure.template.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.message.common.enums.MsgTemplateWechatEnum;
import com.cosfo.message.infrastructure.template.dto.MsgWechatAppDTO;
import com.cosfo.message.infrastructure.template.dto.ParentMsgTemplateInfoDTO;
import com.cosfo.message.infrastructure.template.dto.WechatMsgTemplateDTO;
import com.cosfo.message.infrastructure.template.dto.WechatMsgTemplateQueryDTO;
import com.cosfo.message.infrastructure.template.model.MsgTemplateWechat;
import com.cosfo.message.infrastructure.template.mapper.MsgTemplateWechatMapper;
import com.cosfo.message.infrastructure.template.dao.MsgTemplateWechatDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.message.infrastructure.template.model.MsgTemplateWechatPublic;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 微信消息模版 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-07
 */
@Service
public class MsgTemplateWechatDaoImpl extends ServiceImpl<MsgTemplateWechatMapper, MsgTemplateWechat> implements MsgTemplateWechatDao {
    @Override
    public List<MsgTemplateWechat> getMsgTemplateWechatList(WechatMsgTemplateDTO wechatMsgTemplateDTO , Map<Long, ParentMsgTemplateInfoDTO> idMap) {
        Page<MsgTemplateWechat> pageInfo = new Page<>();
        pageInfo.setCurrent(wechatMsgTemplateDTO.getPageIndex());
        pageInfo.setSize(wechatMsgTemplateDTO.getPageSize());
        LambdaQueryWrapper<MsgTemplateWechat> queryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtils.isNotEmpty(wechatMsgTemplateDTO.getStartTime())&&ObjectUtils.isNotEmpty(wechatMsgTemplateDTO.getEndTime())){
            queryWrapper.between(MsgTemplateWechat::getCreateTime, wechatMsgTemplateDTO.getStartTime(), wechatMsgTemplateDTO.getEndTime());
        }
        if (ObjectUtils.isNotEmpty(wechatMsgTemplateDTO.getId())){
            queryWrapper.eq(MsgTemplateWechat::getId, wechatMsgTemplateDTO.getId());
        }
        // 排除个人模板
        queryWrapper.ne(MsgTemplateWechat::getTemplateWechatPublicId,0);
        if(!CollectionUtils.isEmpty(idMap)){
            // 存在标题名称作为条件的话，需要按符合条件的id去筛选结果
            Set<Long> idList = idMap.keySet();
            queryWrapper.in(MsgTemplateWechat::getTemplateWechatPublicId, idList);
        }
        Page<MsgTemplateWechat> msgTemplateWechatPublicPage = baseMapper.selectPage(pageInfo, queryWrapper);
        return msgTemplateWechatPublicPage.getRecords();
    }

    @Override
    public List<Long> getIdListByCondition(MsgWechatAppDTO msgWechatAppDTO) {
        LambdaQueryWrapper<MsgTemplateWechat> msgTemplateWechatWrapper = new LambdaQueryWrapper<>();
        msgTemplateWechatWrapper.eq(ObjectUtils.isNotEmpty(msgWechatAppDTO.getId()), MsgTemplateWechat::getTemplateWechatPublicId, msgWechatAppDTO.getId());
        msgTemplateWechatWrapper.eq(ObjectUtils.isNotEmpty(msgWechatAppDTO.getAvailableStatus()), MsgTemplateWechat::getAvailableStatus, msgWechatAppDTO.getAvailableStatus());
        msgTemplateWechatWrapper.eq(ObjectUtils.isNotEmpty(msgWechatAppDTO.getSuccessFlag()), MsgTemplateWechat::getSuccessFlag, msgWechatAppDTO.getSuccessFlag());
        List<MsgTemplateWechat> msgTemplateWechatList = baseMapper.selectList(msgTemplateWechatWrapper);
        return msgTemplateWechatList.stream().map(item -> item.getTenantId()).collect(Collectors.toList());
    }

    @Override
    public List<MsgTemplateWechat> getMsgTemplateWechatListByCondition(WechatMsgTemplateQueryDTO wechatMsgTemplateQueryDTO) {
        LambdaQueryWrapper<MsgTemplateWechat> msgTemplateWrapper = new LambdaQueryWrapper<>();
        msgTemplateWrapper.eq(ObjectUtils.isNotEmpty(wechatMsgTemplateQueryDTO.getId()), MsgTemplateWechat::getTemplateWechatPublicId, wechatMsgTemplateQueryDTO.getId());
        msgTemplateWrapper.in(!CollectionUtils.isEmpty(wechatMsgTemplateQueryDTO.getIdList()), MsgTemplateWechat::getTenantId, wechatMsgTemplateQueryDTO.getIdList());
        msgTemplateWrapper.in(!CollectionUtils.isEmpty(wechatMsgTemplateQueryDTO.getTemplateIdList()), MsgTemplateWechat::getId, wechatMsgTemplateQueryDTO.getTemplateIdList());
        msgTemplateWrapper.eq(ObjectUtils.isNotEmpty(wechatMsgTemplateQueryDTO.getSuccessFlag()), MsgTemplateWechat::getSuccessFlag, wechatMsgTemplateQueryDTO.getSuccessFlag());
        msgTemplateWrapper.eq(ObjectUtils.isNotEmpty(wechatMsgTemplateQueryDTO.getTenantId()), MsgTemplateWechat::getTenantId, wechatMsgTemplateQueryDTO.getTenantId());
        List<MsgTemplateWechat> msgTemplateWechatList = baseMapper.selectList(msgTemplateWrapper);
        return msgTemplateWechatList;
    }

    @Override
    public int countTemplate(Long id) {
        LambdaQueryWrapper<MsgTemplateWechat> templateWrapper = new LambdaQueryWrapper<>();
        templateWrapper.eq(MsgTemplateWechat::getTemplateWechatPublicId, id);
        templateWrapper.eq(MsgTemplateWechat::getSuccessFlag, MsgTemplateWechatEnum.SuccessFlag.SUCCESS.getValue());
        return baseMapper.selectCount(templateWrapper);
    }

    @Override
    public MsgTemplateWechat getMsgTemplateWechatById(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public int countDisableTemplate(Long id, Integer availableStatus) {
        LambdaQueryWrapper<MsgTemplateWechat> templateWrapper = new LambdaQueryWrapper<>();
        templateWrapper.eq(MsgTemplateWechat::getTemplateWechatPublicId, id);
        templateWrapper.eq(MsgTemplateWechat::getAvailableStatus, availableStatus);
        return baseMapper.selectCount(templateWrapper);
    }

    @Override
    public MsgTemplateWechat getByPublicTemplateIdAndTenantId(Long templateWechatPublicId, Long tenantId) {
        LambdaQueryWrapper<MsgTemplateWechat> templateWrapper = new LambdaQueryWrapper<>();
        templateWrapper.eq(MsgTemplateWechat::getTemplateWechatPublicId, templateWechatPublicId);
        templateWrapper.eq(MsgTemplateWechat::getTenantId, tenantId);
        return getOne(templateWrapper);
    }

    @Override
    public boolean saveOrUpdateByCondition(MsgTemplateWechat msgTemplateWechat) {
        LambdaQueryWrapper<MsgTemplateWechat> msgTemplateWechatWrapper = new LambdaQueryWrapper<>();
        msgTemplateWechatWrapper.eq(MsgTemplateWechat::getTemplateWechatPublicId, msgTemplateWechat.getTemplateWechatPublicId());
        msgTemplateWechatWrapper.eq(MsgTemplateWechat::getTenantId, msgTemplateWechat.getTenantId());
        msgTemplateWechatWrapper.eq(MsgTemplateWechat::getWechatAppId, msgTemplateWechat.getWechatAppId());
        if (baseMapper.selectCount(msgTemplateWechatWrapper)>0){
            return update(msgTemplateWechat, msgTemplateWechatWrapper);
        }
        // 首次创建时也初始化更新时间
        msgTemplateWechat.setUpdateTime(LocalDateTime.now());
        return save(msgTemplateWechat);
    }

    @Override
    public List<Long> getEnableTenantIdListByTenantIdList(Long fanTaiTemplateId, List<Long> tenantIdList, Integer availableStatus) {
        LambdaQueryWrapper<MsgTemplateWechat> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MsgTemplateWechat::getTemplateWechatPublicId, fanTaiTemplateId);
        queryWrapper.in(MsgTemplateWechat::getTenantId, tenantIdList);
        queryWrapper.eq(MsgTemplateWechat::getSuccessFlag, MsgTemplateWechatEnum.SuccessFlag.SUCCESS.getValue());
        if (ObjectUtils.isNotEmpty(availableStatus)){
            queryWrapper.eq(MsgTemplateWechat::getAvailableStatus, availableStatus);
        }
        List<MsgTemplateWechat> wechatList = baseMapper.selectList(queryWrapper);
        return wechatList.stream().map(item -> item.getTenantId()).collect(Collectors.toList());
    }
}
