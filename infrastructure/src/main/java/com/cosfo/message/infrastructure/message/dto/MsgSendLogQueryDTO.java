package com.cosfo.message.infrastructure.message.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class MsgSendLogQueryDTO implements Serializable {

    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 0=微信服务通知;1=小程序内部消息通知
     */
    private Integer channleType;
    /**
     * 消息类型,0=公告消息
     */
    private Integer contentType;
    /**
     * 发送状态0=成功,1=失败
     */
    private Integer sendStatus;
    /**
     * 读取状态0=未读,1=已读
     */
    private Integer readStatus;
    /**
     * 接收人
     */
    private Long receiveAccountId;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
}
