package com.cosfo.message.infrastructure.template.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.message.infrastructure.template.dto.MsgTemplateApplyLogQueryDTO;
import com.cosfo.message.infrastructure.template.model.MsgTemplateApplyLog;
import com.cosfo.message.infrastructure.template.mapper.MsgTemplateApplyLogMapper;
import com.cosfo.message.infrastructure.template.dao.MsgTemplateApplyLogDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 模版申请记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-07
 */
@Service
public class MsgTemplateApplyLogDaoImpl extends ServiceImpl<MsgTemplateApplyLogMapper, MsgTemplateApplyLog> implements MsgTemplateApplyLogDao {

    @Override
    public List<MsgTemplateApplyLog> getListByCondition(MsgTemplateApplyLogQueryDTO msgTemplateApplyLogQueryDTO) {
        LambdaQueryWrapper<MsgTemplateApplyLog> applyLogWrapper = new LambdaQueryWrapper<>();
        applyLogWrapper.eq(ObjectUtils.isNotEmpty(msgTemplateApplyLogQueryDTO.getTemplateType()), MsgTemplateApplyLog::getTemplateType, msgTemplateApplyLogQueryDTO.getTemplateType());
        applyLogWrapper.eq(MsgTemplateApplyLog::getSuccessFlag, msgTemplateApplyLogQueryDTO.getSuccessFlag());
        applyLogWrapper.select().in(!CollectionUtils.isEmpty(msgTemplateApplyLogQueryDTO.getIdList()),MsgTemplateApplyLog::getTemplateId, msgTemplateApplyLogQueryDTO.getIdList());
        // 按创建时间倒序
        applyLogWrapper.orderByDesc(MsgTemplateApplyLog::getCreateTime);
        // 按模板id去重
        List<MsgTemplateApplyLog> templateApplyLogs = baseMapper.selectList(applyLogWrapper);
        List<MsgTemplateApplyLog> list = templateApplyLogs.stream().collect(
                Collectors.collectingAndThen(Collectors.toCollection(
                        () -> new TreeSet<>(Comparator.comparing(p -> p.getTemplateId()))), ArrayList::new)
        );
        return list;
    }
}
