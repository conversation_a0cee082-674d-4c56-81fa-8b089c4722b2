package com.cosfo.message.infrastructure.template.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date : 2023/2/9 17:58
 * 帆台模板池查询入参
 */
@Data
public class WechatMsgTemplateDTO implements Serializable {


    private static final long serialVersionUID = 6011462650308271783L;
    /**
     * primary key（模板id）
     */
    private Long id;


    /**
     * 微信模版名称(标题)
     */
    private String wechatTitle;

    /**
     * 开始日期
     */
    private LocalDateTime startTime;

    /**
     * 结束日期
     */
    private LocalDateTime endTime;


    /**
     * 当前页码
     */
    private Integer pageIndex;

    /**
     * 页面大小
     */
    private Integer pageSize;


}
