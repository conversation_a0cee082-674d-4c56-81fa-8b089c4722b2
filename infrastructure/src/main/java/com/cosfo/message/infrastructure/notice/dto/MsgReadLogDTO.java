package com.cosfo.message.infrastructure.notice.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * init MsgReadLog 转换对象
 */
@Data
public class MsgReadLogDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 公告id
     */
    private Long noticeId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 用户id
     */
    private Long uId;

    /**
     * 用户角色
     */
    private Integer roleType;

    /**
     * 用户名称
     */
    private String uName;

    /**
     * 店铺id
     */
    private Long storeId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 0=浏览;1=点赞；2=取消点赞
     */
    private Integer actionType;
}
