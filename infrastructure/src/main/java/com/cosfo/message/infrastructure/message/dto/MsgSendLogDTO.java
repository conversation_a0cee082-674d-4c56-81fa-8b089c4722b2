package com.cosfo.message.infrastructure.message.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
public class MsgSendLogDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 租户id
         */
        private Long tenantId;

        /**
         * 模版id
         */
        private Long templateId;

        /**
         * 0=微信服务;1=小程序内部消息
         */
        private Integer channleType;

        /**
         * 消息类型,0=公告消息
         */
        private Integer contentType;

        /**
         * 跳转页面id
         */
        private Long pageId;

        /**
         * 模版详情跳转页
         */
        private String page;

        /**
         * 标题
         */
        private String title;

        /**
         * 微信模版id
         */
        private String thirdTemplateId;

        /**
         * 接收者,内部账户id
         */
        private Long receiveAccountId;

        /**
         * 接收者,三方用户id(微信openid)
         */
        private String receiveThridUid;

        /**
         * 发送者,内部账户id,-1未系统
         */
        private Long sendAccountId;

        /**
         * 发送者,三方用户id(微信openid),-1未系统
         */
        private String sendThridUid;
        /**
         * 模版类型0=微信
         */
        private Integer templateType;

        /**
         * 店铺id
         */
        private Long storeId;

        /**
         * 消息模版内容
         */
        private String data;

        private String accessToken;
}
