package com.cosfo.message.infrastructure.template.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 微信公众号消息模板表
 * @TableName msg_template_wechat_official_account_public
 */
@TableName(value ="msg_template_wechat_official_account_public")
@Data
public class MsgTemplateWechatOfficialAccountPublic implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 模版编码
     */
    private String templateCode;

    /**
     * 模版名称
     */
    private String templateName;

    /**
     * 公众号模版id
     */
    private String wechatTemplateId;

    /**
     * 公众号code FTGYL-帆台供应链;
     */
    private String channelCode;

    /**
     * 关键字
     */
    private String keywords;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}