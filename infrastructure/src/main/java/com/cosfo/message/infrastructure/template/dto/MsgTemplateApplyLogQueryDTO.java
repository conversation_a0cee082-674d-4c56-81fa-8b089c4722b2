package com.cosfo.message.infrastructure.template.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 模版申请记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MsgTemplateApplyLogQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 三方appid
     */
    private String appId;

    /**
     * 1=微信
     */
    private Integer templateType;

    /**
     * 模版id,msg_template_wechat.id
     */
    private Long templateId;

    /**
     * 微信创建模版失败原因
     */
    private String reason;

    /**
     * 是否创建成功0=失败;1=成功
     */
    private Integer successFlag;

    /**
     * 按id列表查询时使用
     */
    private List<Long> idList;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


}
