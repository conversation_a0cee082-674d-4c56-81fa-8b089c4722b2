package com.cosfo.message.infrastructure.message.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 消息发送记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MsgSendLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 模版id
     */
    private Long templateId;

    /**
     * 0=微信服务;1=小程序内部消息
     */
    private Integer channleType;

    /**
     * 消息类型,0=公告消息
     */
    private Integer contentType;

    /**
     * 跳转页面id
     */
    private Long pageId;

    /**
     * 模版详情跳转页
     */
    private String page;

    /**
     * 标题
     */
    private String title;

    /**
     * 微信模版id
     */
    private String thirdTemplateId;

    /**
     * 接收者,内部账户id
     */
    private Long receiveAccountId;

    /**
     * 接收者,三方用户id(微信openid)
     */
    private String receiveThridUid;

    /**
     * 发送者,内部账户id,-1未系统
     */
    private Long sendAccountId;

    /**
     * 发送者,三方用户id(微信openid),-1未系统
     */
    private String sendThridUid;

    /**
     * 内容
     */
    private String data;

    /**
     * 发送状态0=成功,1=失败
     */
    private Integer sendStatus;

    /**
     * 消息发送请求
     */
    private String req;

    /**
     * 消息返回
     */
    private String resp;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 模版类型0=微信
     */
    private Integer templateType;

    /**
     * 店铺id
     */
    private Long storeId;

    /**
     * 读取状态0=未读,1=已读
     */
    private Integer readStatus;

    /**
     * 读取时间
     */
    private LocalDateTime readTime;

    /**
     * 消息唯一标识
     */
    private String bizUniqueNo;

    /**
     * 渠道code
     */
    String channelCode;

}
