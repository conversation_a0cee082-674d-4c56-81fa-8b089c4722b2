package com.cosfo.message.infrastructure.sms.dao;

import com.cosfo.message.infrastructure.sms.dto.SmsSceneQueryDTO;
import com.cosfo.message.infrastructure.sms.model.SmsScene;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * <p>
 * 发送短信场景表 包括发送短信的模板 模板id 平台 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
public interface SmsSceneDao extends IService<SmsScene> {

    /**
     * 根据条件查询短信场景列表
     * @param queryDTO 查询条件
     * @return 短信场景列表
     */
    List<SmsScene> getListByCondition(SmsSceneQueryDTO queryDTO);

    /**
     * 分页查询短信场景列表
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageInfo<SmsScene> getPageByCondition(SmsSceneQueryDTO queryDTO);

    /**
     * 根据场景和平台查询短信场景
     * @param scene 场景
     * @param platform 平台
     * @return 短信场景
     */
    SmsScene getBySceneAndPlatform(String scene, String platform);

    /**
     * 根据模板代码和平台查询短信场景
     * @param templateCode 模板代码
     * @param platform 平台
     * @return 短信场景
     */
    SmsScene getByTemplateCodeAndPlatform(String templateCode, String platform);

    /**
     * 根据平台查询所有场景
     * @param platform 平台
     * @return 场景列表
     */
    List<SmsScene> getListByPlatform(String platform);

    /**
     * 根据场景查询所有平台的配置
     * @param scene 场景
     * @return 场景列表
     */
    List<SmsScene> getListByScene(String scene);

    /**
     * 检查场景和平台的组合是否存在
     * @param scene 场景
     * @param platform 平台
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsBySceneAndPlatform(String scene, String platform, Long excludeId);
}
