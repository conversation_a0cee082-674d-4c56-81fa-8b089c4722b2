package com.cosfo.message.infrastructure.notice.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.message.infrastructure.base.dto.PageQueryDTO;
import com.cosfo.message.infrastructure.notice.dto.ReadOrSupportLogQueryDTO;
import com.cosfo.message.infrastructure.notice.model.MsgReadLog;
import com.cosfo.message.infrastructure.notice.mapper.MsgReadLogMapper;
import com.cosfo.message.infrastructure.notice.dao.MsgReadLogDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 公告阅读点赞记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-07
 */
@Service
public class MsgReadLogDaoImpl extends ServiceImpl<MsgReadLogMapper, MsgReadLog> implements MsgReadLogDao {

    @Override
    public List<MsgReadLog> listByUIdAndNoticId(Long uId, Long noticId, Long tenantId) {
        LambdaQueryWrapper<MsgReadLog> wrapper = new LambdaQueryWrapper();
        wrapper.eq(MsgReadLog::getTenantId,tenantId);
        wrapper.eq(MsgReadLog::getUId,uId);
        wrapper.eq(MsgReadLog::getContentId,noticId);
        wrapper.orderByDesc(MsgReadLog::getId);
        return list(wrapper);
    }

    @Override
    public Map<Long,Long> countByNoticIdsAndActionType(List<Long> notidIds, Integer actionType) {
        //todo cathy 调用的地方从redis取
        LambdaQueryWrapper<MsgReadLog> wrapper = new LambdaQueryWrapper();
        wrapper.select(MsgReadLog::getId,MsgReadLog::getContentId);
        wrapper.in(MsgReadLog::getContentId,notidIds);
        wrapper.eq(MsgReadLog::getActionType,actionType);
        List<MsgReadLog> list = list(wrapper);
        return list.stream().collect(Collectors.groupingBy(MsgReadLog::getContentId, Collectors.counting()));
    }

    @Override
    public Map<Long, List<MsgReadLog>> listByNoticIdsAndActionType(List<Long> notidIds, Integer actionType) {
        //todo cathy 调用的地方从redis取
        LambdaQueryWrapper<MsgReadLog> wrapper = new LambdaQueryWrapper();
        wrapper.select(MsgReadLog::getId, MsgReadLog::getContentId, MsgReadLog::getStoreId, MsgReadLog::getCreateTime);
        wrapper.in(MsgReadLog::getContentId,notidIds);
        wrapper.eq(MsgReadLog::getActionType,actionType);
        List<MsgReadLog> list = list(wrapper);
        return list.stream().collect(Collectors.groupingBy(MsgReadLog::getContentId));
    }

    @Override
    public IPage<MsgReadLog> pageByCondition(ReadOrSupportLogQueryDTO queryDTO, PageQueryDTO pageQueryDTO) {
        LambdaQueryWrapper<MsgReadLog> wrapper = new LambdaQueryWrapper();
        wrapper.eq(MsgReadLog::getContentId,queryDTO.getNoticeId());
        wrapper.eq(MsgReadLog::getTenantId,queryDTO.getTenantId());
        wrapper.eq(!Objects.isNull(queryDTO.getStoreId()),MsgReadLog::getStoreId,queryDTO.getStoreId());
        wrapper.eq(!Objects.isNull(queryDTO.getActionType()),MsgReadLog::getActionType,queryDTO.getActionType());
        return page(new Page<>(pageQueryDTO.getPageIndex(),pageQueryDTO.getPageSize()),wrapper);
    }

    @Override
    public void deleteByContentId(Long contentId) {
        LambdaQueryWrapper<MsgReadLog> wrapper = new LambdaQueryWrapper();
        wrapper.eq(MsgReadLog::getContentId,contentId);
        remove(wrapper);
    }
}
