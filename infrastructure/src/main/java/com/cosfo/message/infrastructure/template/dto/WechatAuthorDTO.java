package com.cosfo.message.infrastructure.template.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date : 2023/2/17 11:25
 * 商户信息封装DTO
 */
@Data
public class WechatAuthorDTO implements Serializable {

    private static final long serialVersionUID = -7286652912723665830L;
    /**
     * 商户id
     */
    private Long tenantId;
    /**
     * 商户access_token
     */
    private String accessToken;

    /**
     * 商户小程序appId
     */
    private String appId;

    /**
     * 商户绑定时间
     */
    private LocalDateTime createTime;
}
