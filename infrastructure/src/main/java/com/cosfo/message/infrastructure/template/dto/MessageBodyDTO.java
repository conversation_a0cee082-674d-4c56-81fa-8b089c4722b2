package com.cosfo.message.infrastructure.template.dto;

import lombok.Data;

/**
 * @Author: fansongsong
 * @Date: 2023-07-14
 * @Description:
 */
@Data
public class MessageBodyDTO {
    /**
     * 消息类型,0=公告消息，1=普通消息
     */
    private Integer contentType;
    /**
     * 消息类型,0=文本，1=富文本，2=图片，3=消息卡片，4=视频，5=文件
     */
    private Integer msgBodyType;
    /**
     * 标题
     */
    private String title;
    /**
     * 内容
     */
    private String data;

    /**
     * 模版id(公众号消息通知时，传公众号模版ID，templateId、templateCode二选一)
     */
    private String templateId;

    /**
     * 模版编码
     */
    private String templateCode;

    /**
     * 消息 点击 跳转url
     * 小程序   ex： { "appid": "pagepath": { "value": any } }
     */
    private String jumpUrl;

    /**
     * 跳转类型 1:网页/2:小程序
     */
    private Integer jumpUrlType;

    /**
     * 微信公众号防重入id。对于同一个openid + client_msg_id, 只发送一条消息,10分钟有效,超过10分钟不保证效果。若无防重入需求，可不填
     */
    private String clientMsgId;
}
