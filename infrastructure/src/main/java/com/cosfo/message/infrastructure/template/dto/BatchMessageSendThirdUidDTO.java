package com.cosfo.message.infrastructure.template.dto;

import lombok.Data;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-07-14
 * @Description: 使用公司内部用户id 或者 三方系统用户id都可以
 * 当使用公司内部用户id发送消息时，(baseuserid,bizUserId,phone 三者选填，不可同时为空)，tenantId，systemOriginType四个参数都不可为空
 * 当使用三方系统用户id发送消息时，thridUid 不可为空，
 * 同时存在时已三方系统用户id为第一优先级
 */
@Data
public class BatchMessageSendThirdUidDTO {
    /**
     * channleType 0=微信服务通知，2=飞书
     */
    private Integer channleType;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 发送者UID
     */
    String senderUid;
    /**
     * 发送者UID列表
     */
    List<String> receiverUidList;
    /**
     * 消息内容
     */
    MessageBodyDTO mesg;
    /**
     * 渠道code
     */
    String channelCode;
    /**
     * systemOriginType 系统来源
     */
    Integer systemOriginType;
}
