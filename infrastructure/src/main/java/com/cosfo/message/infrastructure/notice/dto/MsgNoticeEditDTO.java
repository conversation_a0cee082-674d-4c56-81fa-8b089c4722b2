package com.cosfo.message.infrastructure.notice.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class MsgNoticeEditDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    private Long id;
    /**
     * 标题
     */
    private String title;
    /**
     * 公告内容
     */
    private String content;
    /**
     * 发布时间
     */
    private LocalDateTime pushTime;

    /**
     * 发布门店id
     */
    private List<Long> storeIds;
    /**
     * 发布状态0=定时发布,1=立即发布
     */
    private Integer pushType;
    /**
     * 是否开启点赞 0=关闭；1开启
     */
    private Integer supportSwitch;
    /**
     * 发布状态0=草稿,1=已发布;2=待发布
     */
    private Integer pushStatus;
    /**
     * 用户id
     */
    private Long uId;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 0=公告
     */
    private Integer type;
    /**
     * 0=店铺
     */
    private Integer receiverType;
}
