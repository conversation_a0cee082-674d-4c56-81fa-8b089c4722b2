package com.cosfo.message.infrastructure.sms.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 发送短信场景表 包括发送短信的模板 模板id 平台
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sms_scene")
public class SmsScene implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 模板code
     */
    private String templateCode;

    /**
     * 场景描述
     */
    private String description;

    /**
     * 平台:ALI,CHUANGLAN
     */
    private String platform;

    /**
     * 模板内容
     */
    private String templateContent;

    /**
     * 场景 发送验证码:SEND_VERIFICATION_CODE
     */
    private String scene;
}
