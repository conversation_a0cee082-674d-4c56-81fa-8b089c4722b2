package com.cosfo.message.infrastructure.template.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date : 2023/2/1 15:12
 * 商户创建个人消息模板入参
 */
@Data
public class PrivateMsgTemplateWechatDTO implements Serializable {
    private static final long serialVersionUID = 5266497348599954050L;
    /**
     * 模板id
     */
    private Long id;

    /**
     * 创建模板时选用的关键字序号列表
     */
    private List<Integer> kidList;

    /**
     * 商户id，为单个商户创建模板时使用
     * 不传表示未关联商户全部创建
     */
    private Long tenantId;

    /**
     * 场景说明
     */
    private String scene;

    /**
     * 模板标题id
     */
    private String tid;

    /**
     * 微信公众平台id
     */
    private String platformAppId;

    /**
     * 创建人id
     */
    private Long uId;

    /**
     * 要创建模板的商城信息
     */
    private List<WechatAuthorDTO> authorDTOList;
}
