package com.cosfo.message.infrastructure.template.dao;

import com.cosfo.message.infrastructure.template.dto.TemplateWechatOfficialAccountQueryDTO;
import com.cosfo.message.infrastructure.template.model.MsgTemplateWechatOfficialAccountPublic;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 *
 */
public interface MsgTemplateWechatOfficialAccountPublicDao extends IService<MsgTemplateWechatOfficialAccountPublic> {

    List<MsgTemplateWechatOfficialAccountPublic> queryTemplateWechatOfficialAccountList(TemplateWechatOfficialAccountQueryDTO queryDTO);
}
