package com.cosfo.message.infrastructure.template.dto;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * @Author: fansongsong
 * @Date: 2023-10-17
 * @Description:
 */
@Data
public class WechatOaSendMessageDTO {
    /**
     *  接收者（用户）的 openid
     */
    private String touser;

    /**
     * 所需下发的订阅模板id
     */
    @JSONField(name = "template_id")
    private String templateId;

    /**
     * 跳转网页时填写
     */
    private String url;

    /**
     * 跳转小程序时填写
     */
    private JSONObject miniprogram;

    /**
     * 模板内容，格式形如 { "key1": { "value": any }, "key2": { "value": any } }
     */
    private JSONObject data;

    /**
     * 微信公众号防重入id。对于同一个openid + client_msg_id, 只发送一条消息,10分钟有效,超过10分钟不保证效果。若无防重入需求，可不填
     */
    @JSONField(name = "client_msg_id")
    private String clientMsgId;

}
