package com.cosfo.message.infrastructure.notice.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.message.infrastructure.base.dto.PageQueryDTO;
import com.cosfo.message.infrastructure.notice.dto.NoticeQuery4ManagerDTO;
import com.cosfo.message.infrastructure.notice.model.MsgNotice;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 公告 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-07
 */
public interface MsgNoticeDao extends IService<MsgNotice> {

    Page<MsgNotice> page(NoticeQuery4ManagerDTO queryDTO, PageQueryDTO pageQueryDTO);

    void updatePushStatus(Long id, Integer pushStatus);
}
