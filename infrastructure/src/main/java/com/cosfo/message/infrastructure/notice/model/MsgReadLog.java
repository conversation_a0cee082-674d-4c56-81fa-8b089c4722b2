package com.cosfo.message.infrastructure.notice.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 公告阅读点赞记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MsgReadLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 公告id
     */
    private Long contentId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 用户id
     */
    private Long uId;

    /**
     * 用户角色
     */
    private Integer roleType;

    /**
     * 用户名称
     */
    private String uName;

    /**
     * 店铺id
     */
    private Long storeId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 0=浏览;1=点赞
     */
    private Integer actionType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


}
