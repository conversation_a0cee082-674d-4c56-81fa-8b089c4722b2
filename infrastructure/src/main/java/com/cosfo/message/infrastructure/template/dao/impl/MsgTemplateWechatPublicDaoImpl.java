package com.cosfo.message.infrastructure.template.dao.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.message.common.constants.WechatTemplateConstants;
import com.cosfo.message.infrastructure.template.dto.MsgTemplateWechatPublicDTO;
import com.cosfo.message.infrastructure.template.dto.WechatMsgTemplateDTO;
import com.cosfo.message.infrastructure.template.model.MsgTemplateWechatPublic;
import com.cosfo.message.infrastructure.template.mapper.MsgTemplateWechatPublicMapper;
import com.cosfo.message.infrastructure.template.dao.MsgTemplateWechatPublicDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 微信模版池 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-07
 */
@Slf4j
@Service
public class MsgTemplateWechatPublicDaoImpl extends ServiceImpl<MsgTemplateWechatPublicMapper, MsgTemplateWechatPublic> implements MsgTemplateWechatPublicDao {
    @Override
    public PageInfo<MsgTemplateWechatPublic> getWechatPublicMsgTemplateList(MsgTemplateWechatPublicDTO msgTemplateWechatPublicDTO) {
        Page<MsgTemplateWechatPublic> publicPageInfo = new Page<>();
        publicPageInfo.setCurrent(msgTemplateWechatPublicDTO.getPageIndex());
        publicPageInfo.setSize(msgTemplateWechatPublicDTO.getPageSize());
        LambdaQueryWrapper<MsgTemplateWechatPublic> publicLambdaQueryWrapper = new LambdaQueryWrapper<>();
        // 查询微信公共模板
        publicLambdaQueryWrapper.eq(MsgTemplateWechatPublic::getPId, WechatTemplateConstants.TEMPLATE_PARENT_ID);
        if (ObjectUtils.isNotEmpty(msgTemplateWechatPublicDTO.getWechatTitle())){
            publicLambdaQueryWrapper.like(MsgTemplateWechatPublic::getWechatTitle, msgTemplateWechatPublicDTO.getWechatTitle());
        }
        if (ObjectUtils.isNotEmpty(msgTemplateWechatPublicDTO.getKeyWord())){
            publicLambdaQueryWrapper.like(MsgTemplateWechatPublic::getKeywords, msgTemplateWechatPublicDTO.getKeyWord());
        }
        Page<MsgTemplateWechatPublic> msgTemplateWechatPublicPage = baseMapper.selectPage(publicPageInfo, publicLambdaQueryWrapper);
        PageInfo<MsgTemplateWechatPublic> pageInfo = new PageInfo<>();
        pageInfo.setList(msgTemplateWechatPublicPage.getRecords());
        pageInfo.setPageNum(msgTemplateWechatPublicDTO.getPageIndex());
        pageInfo.setPageSize(msgTemplateWechatPublicDTO.getPageSize());
        pageInfo.setPages((int) msgTemplateWechatPublicPage.getPages());
        pageInfo.setTotal(msgTemplateWechatPublicPage.getTotal());
        return pageInfo;
    }

    @Override
    public PageInfo<MsgTemplateWechatPublic> getMsgTemplateWechatList(WechatMsgTemplateDTO wechatMsgTemplateDTO) {
        Page<MsgTemplateWechatPublic> page = new Page<>();
        page.setCurrent(wechatMsgTemplateDTO.getPageIndex());
        page.setSize(wechatMsgTemplateDTO.getPageSize());
        LambdaQueryWrapper<MsgTemplateWechatPublic> queryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtils.isNotEmpty(wechatMsgTemplateDTO.getStartTime())&&ObjectUtils.isNotEmpty(wechatMsgTemplateDTO.getEndTime())){
            queryWrapper.between(MsgTemplateWechatPublic::getCreateTime, wechatMsgTemplateDTO.getStartTime(), wechatMsgTemplateDTO.getEndTime());
        }
        if (ObjectUtils.isNotEmpty(wechatMsgTemplateDTO.getId())){
            queryWrapper.eq(MsgTemplateWechatPublic::getId, wechatMsgTemplateDTO.getId());
        }
        // 排除微信公共模板
        queryWrapper.ne(MsgTemplateWechatPublic::getPId,WechatTemplateConstants.TEMPLATE_PARENT_ID);
        if(!StringUtils.isEmpty(wechatMsgTemplateDTO.getWechatTitle())){
            queryWrapper.like(MsgTemplateWechatPublic::getWechatTitle, wechatMsgTemplateDTO.getWechatTitle());
        }
        // 按时间倒序
        queryWrapper.orderByDesc(MsgTemplateWechatPublic::getCreateTime);
        Page<MsgTemplateWechatPublic> msgTemplateWechatPublicPage = baseMapper.selectPage(page, queryWrapper);
        PageInfo<MsgTemplateWechatPublic> pageInfo = new PageInfo<>();
        pageInfo.setList(msgTemplateWechatPublicPage.getRecords());
        pageInfo.setPageNum(wechatMsgTemplateDTO.getPageIndex());
        pageInfo.setPageSize(wechatMsgTemplateDTO.getPageSize());
        pageInfo.setPages((int) msgTemplateWechatPublicPage.getPages());
        pageInfo.setTotal(msgTemplateWechatPublicPage.getTotal());
        return pageInfo;
    }

    @Override
    public MsgTemplateWechatPublic getWechatPublicMsgTemplateById(Long id) {
        MsgTemplateWechatPublic wechatPublic = baseMapper.selectById(id);
        return wechatPublic;
    }

    @Override
    public List<MsgTemplateWechatPublic> getWechatPublicMsgTemplateByIdList(List<Long> idList) {
        List<MsgTemplateWechatPublic> publicList = baseMapper.selectBatchIds(idList);
        return publicList;
    }
}
