package com.cosfo.message.infrastructure.message.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-07-19
 * @Description:
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class MsgBatchUpdateSendLogDTO {
    /**
     * 消息类型,2=售后租户通知
     */
    private Integer contentType;

    /**
     * 发送记录ids
     */
    private List<Long> ids;

    /**
     * 标记状态0=未读,1=已读
     */
    private Integer readStatus;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 渠道类型
     */
    private Integer channleType;
}
