package com.cosfo.message.infrastructure.base.dto;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.Data;
import org.apache.commons.compress.utils.Lists;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;

import static java.util.stream.Collectors.toList;

@Data
public class PageVO<T> implements Serializable {

    private static final long serialVersionUID = -1;

    private static final int DEFAULT_PAGE_SIZE = 10;

    private Integer pageNum = 1;

    /**
     * 存放当前页中的数据
     */
    private List<T> data;

    /**
     * 总记录数
     */
    private Integer total = 0;

    /**
     * 每页显示的数据量
     */
    private Integer pageSize = DEFAULT_PAGE_SIZE;

    /**
     * 总页数
     */
    private Integer pages = 1;

    /**
     * 是否是最后一页
     */
    private Boolean isLastPage;

    public static <T, R> PageVO<R> page2VO(IPage<T> page, Function<? super T, ? extends R> mapper) {
        PageVO resp = new PageVO();
        resp.setPageNum(new Long(page.getCurrent()).intValue());
        resp.setPageSize(new Long(page.getSize()).intValue());
        resp.setTotal(new Long(page.getTotal()).intValue());
        resp.setData(Lists.newArrayList());
        resp.setIsLastPage(resp.getTotal() == 0 || page.getCurrent() == page.getPages());
        List<R> collect = page.getRecords().stream().map(mapper).collect(toList());
        resp.setData(collect);
        return resp;
    }

    /**
     * 一个结果都没
     * @param page
     * @param pageSize
     * @return
     */
    public static PageVO getNullResult(Integer page, Integer pageSize) {
        PageVO empty = new PageVO();
        empty.setPageNum(page);
        empty.setPageSize(pageSize);
        empty.setData(Collections.emptyList());
        empty.setIsLastPage(true);
        return empty;
    }
}
