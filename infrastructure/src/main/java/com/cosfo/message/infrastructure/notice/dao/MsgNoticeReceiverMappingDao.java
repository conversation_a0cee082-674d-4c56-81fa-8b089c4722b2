package com.cosfo.message.infrastructure.notice.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cosfo.message.infrastructure.base.dto.PageQueryDTO;
import com.cosfo.message.infrastructure.notice.dto.MsgNoticeReceiverQueryDTO;
import com.cosfo.message.infrastructure.notice.model.MsgNoticeReceiverMapping;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 公告接收门店表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-07
 */
public interface MsgNoticeReceiverMappingDao extends IService<MsgNoticeReceiverMapping> {

    List<MsgNoticeReceiverMapping> listByNoticeIds(List<Long> noticeIds);

    /**
     *
     * @param noticeId
     */
    void removeByNoticId(Long noticeId);

    IPage<MsgNoticeReceiverMapping> page(MsgNoticeReceiverQueryDTO queryDTO, PageQueryDTO pageQueryDTO);
}
