package com.cosfo.message.infrastructure.notice.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class NoticeQuery4ManagerDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 排序字段 id；push_time;create_time
     */
    private String sortKey;
    /**
     * 排序规则 1=升序  0=降序
     */
    private Integer sortBy;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * id
     */
    private Long id;
    /**
     * 标题
     */
    private String title;
    /**
     * 发布时间-开始
     */
    private LocalDateTime pushTimeBegin;

    /**
     * 发布时间-结束
     */
    private LocalDateTime pushTimeEnd;

    /**
     * 发布状态0=草稿,1=已发布;2=待发布
     */
    private Integer pushStatus;
}
