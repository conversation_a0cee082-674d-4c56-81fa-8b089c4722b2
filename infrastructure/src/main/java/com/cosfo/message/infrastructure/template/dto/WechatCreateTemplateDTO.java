package com.cosfo.message.infrastructure.template.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date : 2023/2/15 18:33
 * 调用微信创建模板接口参数封装类
 */
@Data
public class WechatCreateTemplateDTO implements Serializable {
    private static final long serialVersionUID = -6248880461689572975L;
    /**
     * 模板标题id
     */
    private String tid;
    /**
     * 模板关键词列表
     */
    private List<Integer> kidList;

    /**
     * 服务场景描述
     */
    private String sceneDesc;
}
