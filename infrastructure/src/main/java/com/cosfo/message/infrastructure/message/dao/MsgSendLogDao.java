package com.cosfo.message.infrastructure.message.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cosfo.message.infrastructure.base.dto.PageQueryDTO;
import com.cosfo.message.infrastructure.message.dto.MsgBatchUpdateSendLogDTO;
import com.cosfo.message.infrastructure.message.dto.MsgSendLogQueryDTO;
import com.cosfo.message.infrastructure.message.dto.NotifyMessageDTO;
import com.cosfo.message.infrastructure.message.model.MsgSendLog;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 消息发送记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-07
 */
public interface MsgSendLogDao extends IService<MsgSendLog> {
    /**
     * 根据用户查询 收到的上一个公告
     * @param uId
     * @param noticeId
     * @param tenantId
     * @return
     */
    Long getBeforeNoticeId(Long uId, Long noticeId, Long tenantId);
    /**
     * 根据用户查询 收到的下一个公告
     * @param uId
     * @param noticeId
     * @param tenantId
     * @return
     */
    Long getAfterNoticeId(Long uId, Long noticeId, Long tenantId);

    /**
     * 分页查询消息接收记录
     * @param dto
     * @return
     */
    IPage<MsgSendLog> pageSendLog(MsgSendLogQueryDTO dto, PageQueryDTO pageQueryDTO);

    /**
     * 统计查询消息总数
     * @param dto
     * @return
     */
    Integer countSendLog(MsgSendLogQueryDTO dto);

    /**
     * 跟新读取状态
     * @param uId
     * @param noticeId
     * @param tenantId
     * @param value
     */
    void updateReadStatusByUidAndNoticeId(Long uId, Long noticeId, Long tenantId, Integer value);

    /**
     * 根据公告id删除发送记录
     * @param pageId
     */
    void deleteByPageId(Long pageId,Integer contentType);

    /**
     * 更新消息读取状态
     * @param msgBatchUpdateSendLogDTO
     * @return
     */
    Boolean batchUpdateReadStatus(MsgBatchUpdateSendLogDTO msgBatchUpdateSendLogDTO);

    /**
     * 查询幂等数据
     *
     * @param tenantId 租户
     * @param channleType  渠道类型
     * @param contentType 消息类型
     * @param bizUniqueNo 业务唯一单号
     * @return
     */
    MsgSendLog queryMessageByUniqueId(Long tenantId, Integer channleType, Integer contentType, String bizUniqueNo);

    /**
     * 批量查询强提醒消息接收记录
     * @param msgSendLogQueryDTO
     * @return
     */
    List<MsgSendLog> listSendLog(MsgSendLogQueryDTO msgSendLogQueryDTO);

    /**
     * 更新消息提醒状态
     * @param msgSendLogQueryDTO
     * @return
     */
    Boolean batchSuccessSendStatus(MsgSendLogQueryDTO msgSendLogQueryDTO, List<Long> idList);

    /**
     * 分页查询优化
     * @param dto
     * @return
     */
    IPage<MsgSendLog> pageSendLogOrderByOptimize(MsgSendLogQueryDTO dto, PageQueryDTO pageQueryDTO);

    /**
     * 更新发送日志的响应内容及状态
     * @param msgSendLog
     * @param resp
     * @param success
     * @return
     */
    boolean updateMsgLogResp(MsgSendLog msgSendLog, Object resp, boolean success);
}
