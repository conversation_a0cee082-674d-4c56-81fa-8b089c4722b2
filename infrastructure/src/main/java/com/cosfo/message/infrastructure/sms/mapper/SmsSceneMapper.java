package com.cosfo.message.infrastructure.sms.mapper;

import com.cosfo.message.infrastructure.sms.model.SmsScene;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 发送短信场景表 包括发送短信的模板 模板id 平台 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Mapper
public interface SmsSceneMapper extends BaseMapper<SmsScene> {

    /**
     * 根据场景和平台查询短信场景
     * @param scene 场景
     * @param platform 平台
     * @return 短信场景
     */
    SmsScene selectBySceneAndPlatform(@Param("scene") String scene, @Param("platform") String platform);

    /**
     * 根据模板代码和平台查询短信场景
     * @param templateCode 模板代码
     * @param platform 平台
     * @return 短信场景
     */
    SmsScene selectByTemplateCodeAndPlatform(@Param("templateCode") String templateCode, @Param("platform") String platform);
}
