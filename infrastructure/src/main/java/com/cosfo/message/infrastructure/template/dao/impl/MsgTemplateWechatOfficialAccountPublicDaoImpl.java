package com.cosfo.message.infrastructure.template.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.message.infrastructure.template.dao.MsgTemplateWechatOfficialAccountPublicDao;
import com.cosfo.message.infrastructure.template.dto.TemplateWechatOfficialAccountQueryDTO;
import com.cosfo.message.infrastructure.template.mapper.MsgTemplateWechatOfficialAccountPublicMapper;
import com.cosfo.message.infrastructure.template.model.MsgTemplateWechatOfficialAccountPublic;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class MsgTemplateWechatOfficialAccountPublicDaoImpl extends ServiceImpl<MsgTemplateWechatOfficialAccountPublicMapper, MsgTemplateWechatOfficialAccountPublic>
        implements MsgTemplateWechatOfficialAccountPublicDao {

    @Override
    public List<MsgTemplateWechatOfficialAccountPublic> queryTemplateWechatOfficialAccountList(TemplateWechatOfficialAccountQueryDTO queryDTO) {
        LambdaQueryWrapper<MsgTemplateWechatOfficialAccountPublic> wrapper = new LambdaQueryWrapper();
        wrapper.eq(MsgTemplateWechatOfficialAccountPublic::getChannelCode, queryDTO.getChannelCode());
        wrapper.eq(Objects.nonNull(queryDTO.getTemplateCode()), MsgTemplateWechatOfficialAccountPublic::getTemplateCode, queryDTO.getTemplateCode());
        return list(wrapper);
    }
}




