package com.cosfo.message.infrastructure.sms.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 发送短信场景表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SmsSceneQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 主键列表
     */
    private List<Long> idList;

    /**
     * 模板code
     */
    private String templateCode;

    /**
     * 场景描述
     */
    private String description;

    /**
     * 平台:ALI,CHUANGLAN
     */
    private String platform;

    /**
     * 平台列表
     */
    private List<String> platformList;

    /**
     * 模板内容
     */
    private String templateContent;

    /**
     * 场景 发送验证码:SEND_VERIFICATION_CODE
     */
    private String scene;

    /**
     * 场景列表
     */
    private List<String> sceneList;

    /**
     * 模糊查询关键字（用于描述或场景）
     */
    private String keyword;

    /**
     * 当前页码
     */
    private Integer pageIndex = 1;

    /**
     * 页面大小
     */
    private Integer pageSize = 10;
}
