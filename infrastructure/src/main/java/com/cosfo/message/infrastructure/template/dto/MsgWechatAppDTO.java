package com.cosfo.message.infrastructure.template.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 微信消息模版相关（已关联、未关联、创建失败）小程序查询DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-31
 */
@Data
public class MsgWechatAppDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key（模板id）
     */
    private Long id;

    /**
     * 商城id
     */
    private Long mallId;

    /**
     * 商城名称
     */
    private String mallName;

    /**
     * 查询类型 0未关联、1已关联
     */
    private Integer type;

    /**
     * 0创建失败 1成功
     */
    private Integer successFlag;

    /**
     * 是否可用0=不可用;1=可用
     * 即0=已删除;1=生效中
     */
    private Integer availableStatus;

    /**
     * 当前页码
     */
    private Integer pageIndex;

    /**
     * 页面大小
     */
    private Integer pageSize;

    /**
     * 平台id，环境隔离使用
     */
    private String platformAppId;
}
