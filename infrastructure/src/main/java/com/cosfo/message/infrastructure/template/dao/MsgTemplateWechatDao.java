package com.cosfo.message.infrastructure.template.dao;

import com.cosfo.message.infrastructure.template.dto.*;
import com.cosfo.message.infrastructure.template.model.MsgTemplateWechat;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 微信消息模版 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-07
 */
public interface MsgTemplateWechatDao extends IService<MsgTemplateWechat> {
    List<MsgTemplateWechat> getMsgTemplateWechatList(WechatMsgTemplateDTO wechatMsgTemplateDTO, Map<Long, ParentMsgTemplateInfoDTO> idMap);

    /**
     * 获取该模板已关联商城id
     * @param id 模板id
     * @return
     */
    List<Long> getIdListByCondition(MsgWechatAppDTO msgWechatAppDTO);

    /**
     * 按条件获取个人模板列表
     * @param wechatMsgTemplateQueryDTO
     * @return
     */
    List<MsgTemplateWechat> getMsgTemplateWechatListByCondition(WechatMsgTemplateQueryDTO wechatMsgTemplateQueryDTO);

    /**
     * 按微信模板池id获取已关联模板数
     * @param id
     * @return
     */
    int countTemplate(Long id);

    /**
     * 按id获取模板
     * @param id
     * @return
     */
    MsgTemplateWechat getMsgTemplateWechatById(Long id);

    /**
     * 按状态获取模板数
     * @param id
     * @param availableStatus
     * @return
     */
    int countDisableTemplate(Long id, Integer availableStatus);

    /**
     * 根据模版池模版 id和 租户id查询 微信私有模版
     * @param templateWechatPublicId
     * @param tenantId
     */
    MsgTemplateWechat getByPublicTemplateIdAndTenantId(Long templateWechatPublicId, Long tenantId);
    /**
     * 根据模板池id和tenantId来判断是否更新
     */
    boolean saveOrUpdateByCondition(MsgTemplateWechat msgTemplateWechat);

    /**
     * 根据租户id列表查询已关联的租户id列表
     * @param fanTaiTemplateId 帆台模板池id
     * @param tenantIdList 租户id列表
     * @return
     */
    List<Long> getEnableTenantIdListByTenantIdList(Long fanTaiTemplateId, List<Long> tenantIdList ,Integer availableStatus);
}
