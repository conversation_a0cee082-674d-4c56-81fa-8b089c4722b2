package com.cosfo.message.infrastructure.notice.dao.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.update.UpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.message.common.enums.SortByEnum;
import com.cosfo.message.infrastructure.base.dto.PageQueryDTO;
import com.cosfo.message.infrastructure.message.model.MsgSendLog;
import com.cosfo.message.infrastructure.notice.dto.NoticeQuery4ManagerDTO;
import com.cosfo.message.infrastructure.notice.dto.NoticeQuery4ReceiverDTO;
import com.cosfo.message.infrastructure.notice.model.MsgNotice;
import com.cosfo.message.infrastructure.notice.mapper.MsgNoticeMapper;
import com.cosfo.message.infrastructure.notice.dao.MsgNoticeDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 公告 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-07
 */
@Service
public class MsgNoticeDaoImpl extends ServiceImpl<MsgNoticeMapper, MsgNotice> implements MsgNoticeDao {

    @Override
    public Page<MsgNotice> page(NoticeQuery4ManagerDTO queryDTO, PageQueryDTO pageQueryDTO) {
        LambdaQueryWrapper<MsgNotice> wrapper = new LambdaQueryWrapper();
        wrapper.like(ObjectUtils.isNotEmpty(queryDTO.getTitle()),MsgNotice::getTitle,queryDTO.getTitle());
        wrapper.eq(ObjectUtils.isNotEmpty(queryDTO.getTenantId()),MsgNotice::getTenantId,queryDTO.getTenantId());
        wrapper.eq(MsgNotice::getPushStatus,queryDTO.getPushStatus());
        if(SortByEnum.DESC.getValue().equals(queryDTO.getSortBy())){
            wrapper.orderByDesc("id".equals(queryDTO.getSortKey()),MsgNotice::getId);
            wrapper.orderByDesc("push_time".equals(queryDTO.getSortKey()),MsgNotice::getPushTime);
            wrapper.orderByDesc("create_time".equals(queryDTO.getSortKey()),MsgNotice::getCreateTime);
            wrapper.orderByDesc("push_time".equals(queryDTO.getSortKey()),MsgNotice::getPushTime);
        } else {
            wrapper.orderByAsc("id".equals(queryDTO.getSortKey()),MsgNotice::getId);
            wrapper.orderByAsc("push_time".equals(queryDTO.getSortKey()),MsgNotice::getPushTime);
            wrapper.orderByAsc("create_time".equals(queryDTO.getSortKey()),MsgNotice::getCreateTime);
            wrapper.orderByAsc("push_time".equals(queryDTO.getSortKey()),MsgNotice::getPushTime);

        }
        wrapper.ge(ObjectUtils.isNotEmpty(queryDTO.getPushTimeBegin()),MsgNotice::getPushTime,LocalDateTimeUtil.format(queryDTO.getPushTimeBegin(),"yyyy-MM-dd HH:mm:ss"));
        wrapper.le(ObjectUtils.isNotEmpty(queryDTO.getPushTimeEnd()),MsgNotice::getPushTime, LocalDateTimeUtil.format(queryDTO.getPushTimeEnd(),"yyyy-MM-dd HH:mm:ss"));

        return page(new Page<>(pageQueryDTO.getPageIndex(),pageQueryDTO.getPageSize()),wrapper);
    }

    @Override
    public void updatePushStatus(Long id, Integer pushStatus) {
        LambdaUpdateWrapper<MsgNotice> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(MsgNotice::getPushStatus,pushStatus);
        wrapper.eq(MsgNotice::getId,id);
        update(wrapper);
    }
}
