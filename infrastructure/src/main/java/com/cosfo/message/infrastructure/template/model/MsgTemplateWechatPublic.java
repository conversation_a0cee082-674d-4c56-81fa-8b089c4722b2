package com.cosfo.message.infrastructure.template.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 微信模版池
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MsgTemplateWechatPublic implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 父模版id
     */
    private Long pId;

    /**
     * 是否可用0=不可用;1=可用
     */
    private Integer availableStatus;

    /**
     * 微信模版标题 id
     */
    private Long wechatTid;

    /**
     * 微信模版所属类目 id
     */
    private Long wechatCategoryId;

    /**
     * 微信模版所属类目 名称
     */
    private String wechatCategoryName;

    /**
     * 模版类型，2 为一次性订阅，3 为长期订阅
     */
    private Integer wechatType;

    /**
     * 微信模版名称
     */
    private String wechatTitle;

    /**
     * 模版名称
     */
    private String templateName;

    /**
     * 关键字
     */
    private String keywords;

    /**
     * 创建人id
     */
    private Long creator;

    /**
     * 场景说明
     */
    private String scene;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


}
