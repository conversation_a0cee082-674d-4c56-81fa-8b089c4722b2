package com.cosfo.message.infrastructure.message.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-07-20
 * @Description:
 */
@Data
public class NotifyMessageDTO {
    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 消息类型
     */
    private Integer messageContentType;

    /**
     * 标题
     */
    private String title;

    /**
     * 详细内容
     */
    private List<NotifyTipBodyDTO> notifyTipBodyDTOList;

    /**
     * 幂等ID
     */
    private String uniqueId;

    /**
     * 业务ID
     */
    private Long pageId;

    /**
     * 副标题
     */
    private String subTitle;
}
