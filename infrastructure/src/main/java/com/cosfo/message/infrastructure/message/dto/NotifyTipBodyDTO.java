package com.cosfo.message.infrastructure.message.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-07-20
 * @Description:
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class NotifyTipBodyDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 字段编码
     * ex：O123445695
     */
    private String keyCode;

    /**
     * 字段值
     */
    private List<String> keyValue;
}
