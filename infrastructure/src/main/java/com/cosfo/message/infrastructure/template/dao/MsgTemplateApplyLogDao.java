package com.cosfo.message.infrastructure.template.dao;

import com.cosfo.message.infrastructure.template.dto.MsgTemplateApplyLogQueryDTO;
import com.cosfo.message.infrastructure.template.model.MsgTemplateApplyLog;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 模版申请记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-07
 */
public interface MsgTemplateApplyLogDao extends IService<MsgTemplateApplyLog> {
    List<MsgTemplateApplyLog> getListByCondition(MsgTemplateApplyLogQueryDTO msgTemplateApplyLogQueryDTO);
}
