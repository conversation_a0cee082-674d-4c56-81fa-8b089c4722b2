package com.cosfo.message.infrastructure.notice.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@Data
public class NoticeQuery4ReceiverDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 公告ids
     */
    private Set<Long> ids;
    /**
     * 读取状态0=未读,1=已读
     */
    private Integer readStatus;
    /**
     * 接收人
     */
    private Long recevieUid;

    private Long tenantId;

}