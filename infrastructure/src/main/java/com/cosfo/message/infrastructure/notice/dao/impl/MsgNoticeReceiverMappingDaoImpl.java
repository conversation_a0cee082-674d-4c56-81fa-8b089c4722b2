package com.cosfo.message.infrastructure.notice.dao.impl;
import java.util.Objects;
import java.util.Set;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.message.common.enums.SortByEnum;
import com.cosfo.message.infrastructure.base.dto.PageQueryDTO;
import com.cosfo.message.infrastructure.notice.dto.MsgNoticeReceiverQueryDTO;
import com.cosfo.message.infrastructure.notice.model.MsgNotice;
import com.cosfo.message.infrastructure.notice.model.MsgNoticeReceiverMapping;
import com.cosfo.message.infrastructure.notice.mapper.MsgNoticeReceiverMappingMapper;
import com.cosfo.message.infrastructure.notice.dao.MsgNoticeReceiverMappingDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <p>
 * 公告接收门店表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-07
 */
@Service
public class MsgNoticeReceiverMappingDaoImpl extends ServiceImpl<MsgNoticeReceiverMappingMapper, MsgNoticeReceiverMapping> implements MsgNoticeReceiverMappingDao {

    @Override
    public List<MsgNoticeReceiverMapping> listByNoticeIds(List<Long> noticeIds) {
        LambdaQueryWrapper<MsgNoticeReceiverMapping> wrapper = new LambdaQueryWrapper();
        wrapper.in(MsgNoticeReceiverMapping::getNoticeId,noticeIds);
        return list(wrapper);
    }

    @Override
    public void removeByNoticId(Long noticeId) {
        LambdaQueryWrapper<MsgNoticeReceiverMapping> wrapper = new LambdaQueryWrapper();
        wrapper.eq(MsgNoticeReceiverMapping::getNoticeId,noticeId);
        remove(wrapper);
    }

    @Override
    public IPage<MsgNoticeReceiverMapping> page(MsgNoticeReceiverQueryDTO queryDTO, PageQueryDTO pageQueryDTO) {
        LambdaQueryWrapper<MsgNoticeReceiverMapping> wrapper = new LambdaQueryWrapper();
        wrapper.eq(MsgNoticeReceiverMapping::getNoticeId,queryDTO.getNoticeId());
        wrapper.eq(MsgNoticeReceiverMapping::getType, Objects.isNull(queryDTO.getReceiverType())?0:queryDTO.getReceiverType());
        wrapper.ne(MsgNoticeReceiverMapping::getReceiverId, 0L);
        wrapper.in(!CollectionUtils.isEmpty(queryDTO.getReceiverIds()),MsgNoticeReceiverMapping::getReceiverId,queryDTO.getReceiverIds());
        wrapper.notIn(!CollectionUtils.isEmpty(queryDTO.getNotInReceiverIds()),MsgNoticeReceiverMapping::getReceiverId,queryDTO.getNotInReceiverIds());
        return page(new Page<>(pageQueryDTO.getPageIndex(),pageQueryDTO.getPageSize()),wrapper);
    }
}
