package com.cosfo.message.infrastructure.notice.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 公告接收门店表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MsgNoticeReceiverMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 0=店铺
     */
    private Integer type;

    /**
     * 公告id
     */
    private Long noticeId;

    /**
     * 接受者id(店铺id)
     */
    private Long receiverId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


}
