package com.cosfo.message.infrastructure.notice.dto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@Data
public class MsgNoticeReceiverQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 0=店铺
     */
    private Integer receiverType;
    /**
     * 接受者id
     */
    private List<Long> receiverIds;
    /**
     * 不包含的接受者id
     */
    private Set<Long> notInReceiverIds;
    /**
     * 公告id
     */
    private Long noticeId;
    /**
     * 是否点赞 = 已读
     */
    private Boolean supportFlag;
    /**
     * 是否已读 false=未读
     */
    private Boolean readFlag;
}
