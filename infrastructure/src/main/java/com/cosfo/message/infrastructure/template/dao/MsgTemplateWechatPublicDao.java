package com.cosfo.message.infrastructure.template.dao;

import com.cosfo.message.infrastructure.template.dto.MsgTemplateWechatPublicDTO;
import com.cosfo.message.infrastructure.template.dto.ParentMsgTemplateInfoDTO;
import com.cosfo.message.infrastructure.template.dto.WechatMsgTemplateDTO;
import com.cosfo.message.infrastructure.template.model.MsgTemplateWechatPublic;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 微信模版池 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-07
 */
public interface MsgTemplateWechatPublicDao extends IService<MsgTemplateWechatPublic> {
    PageInfo<MsgTemplateWechatPublic> getWechatPublicMsgTemplateList(MsgTemplateWechatPublicDTO msgTemplateWechatPublicDTO);

    PageInfo<MsgTemplateWechatPublic> getMsgTemplateWechatList(WechatMsgTemplateDTO wechatMsgTemplateDTO);

    MsgTemplateWechatPublic getWechatPublicMsgTemplateById(Long id);

    List<MsgTemplateWechatPublic> getWechatPublicMsgTemplateByIdList(List<Long> idList);
}
