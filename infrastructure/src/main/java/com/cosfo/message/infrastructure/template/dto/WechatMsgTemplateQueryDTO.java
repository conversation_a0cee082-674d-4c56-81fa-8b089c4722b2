package com.cosfo.message.infrastructure.template.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date : 2023/2/9 17:58
 * 帆台模板池查询入参
 */
@Data
public class WechatMsgTemplateQueryDTO implements Serializable {


    private static final long serialVersionUID = 6011462650308271783L;
    /**
     * primary key（模板id）
     */
    private Long id;

    /**
     * 租户id集合
     */
    private List<Long> idList;

    /**
     * 模板id集合（按模板id集合查询时使用）
     */
    private List<Long> templateIdList;

    /**
     * 微信模版名称(标题)
     */
    private String wechatTitle;

    /**
     * 开始日期
     */
    private LocalDateTime startTime;

    /**
     * 结束日期
     */
    private LocalDateTime endTime;

    /**
     * 模板创建状态 0失败 1成功
     */
    private Integer successFlag;

    /**
     * 商城id
     */
    private Long tenantId;
}
