package com.cosfo.message.infrastructure.notice.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 公告
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MsgNotice implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 0=公告
     */
    private Integer type;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 发布时间
     */
    private LocalDateTime pushTime;

    /**
     * 创建用户id
     */
    private Long createUid;

    /**
     * 编辑用户id
     */
    private Long editUid;

    /**
     * 发布用户id
     */
    private Long pushUid;

    /**
     * 发布类型0=定时发布,1=立即发布
     */
    private Integer pushType;

    /**
     * 发布状态0=草稿,1=已发布;2=待发布
     */
    private Integer pushStatus;

    /**
     * 是否开启点赞 0=关闭；1开启
     */
    private Integer supportSwitch;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


}
