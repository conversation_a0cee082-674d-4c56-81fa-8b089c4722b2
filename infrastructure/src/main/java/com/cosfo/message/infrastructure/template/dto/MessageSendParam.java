package com.cosfo.message.infrastructure.template.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: fansongsong
 * @Date: 2023-07-17
 * @Description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class MessageSendParam {
    /**
     * channleType 0=微信服务通知，2=飞书
     */
    private Integer channleType;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 发送者
     */
    MessageUserDTO sender;
    /**
     * 接收者
     */
    MessageUserDTO receiver;
    /**
     * 消息内容
     */
    MessageBodyDTO mesg;
    /**
     * systemOriginType 系统来源
     */
    Integer systemOriginType;
    /**
     * 渠道code
     */
    String channelCode;
}
