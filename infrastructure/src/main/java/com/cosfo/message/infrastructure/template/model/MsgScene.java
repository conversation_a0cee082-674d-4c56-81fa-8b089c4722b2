package com.cosfo.message.infrastructure.template.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 消息场景表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MsgScene implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 模版池id
     */
    private Long templateId;

    /**
     * 模版类型0=微信
     */
    private Integer templateType;

    /**
     * 模版名称
     */
    private String templateName;

    /**
     * 关键字
     */
    private String keywords;

    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 描述
     */
    private String description;

    /**
     * 场景 状态0=不可用;1=可用
     */
    private Integer sceneStatus;

    /**
     * 消息详情url
     */
    private String page;

    /**
     * 操纵人
     */
    private Long updater;

}
