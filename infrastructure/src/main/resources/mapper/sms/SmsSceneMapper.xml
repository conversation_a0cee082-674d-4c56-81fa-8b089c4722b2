<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.message.infrastructure.sms.mapper.SmsSceneMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cosfo.message.infrastructure.sms.model.SmsScene">
        <id column="id" property="id" />
        <result column="template_code" property="templateCode" />
        <result column="description" property="description" />
        <result column="platform" property="platform" />
        <result column="template_content" property="templateContent" />
        <result column="scene" property="scene" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, template_code, description, platform, template_content, scene
    </sql>

    <!-- 根据场景和平台查询短信场景 -->
    <select id="selectBySceneAndPlatform" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sms_scene
        WHERE scene = #{scene} AND platform = #{platform}
        LIMIT 1
    </select>

    <!-- 根据模板代码和平台查询短信场景 -->
    <select id="selectByTemplateCodeAndPlatform" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sms_scene
        WHERE template_code = #{templateCode} AND platform = #{platform}
        LIMIT 1
    </select>

</mapper>
