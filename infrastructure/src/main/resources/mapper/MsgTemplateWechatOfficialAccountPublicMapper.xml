<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.message.infrastructure.template.mapper.MsgTemplateWechatOfficialAccountPublicMapper">

    <resultMap id="BaseResultMap" type="com.cosfo.message.infrastructure.template.model.MsgTemplateWechatOfficialAccountPublic">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="templateCode" column="template_code" jdbcType="VARCHAR"/>
            <result property="templateName" column="template_name" jdbcType="VARCHAR"/>
            <result property="wechatTemplateId" column="wechat_template_id" jdbcType="VARCHAR"/>
            <result property="channelCode" column="channel_code" jdbcType="VARCHAR"/>
            <result property="keywords" column="keywords" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,template_code,template_name,
        wechat_template_id,channel_code,keywords,
        create_time,update_time
    </sql>
</mapper>
