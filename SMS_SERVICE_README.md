# 短信服务使用说明

## 概述

本项目实现了一个支持多种短信发送方式的工厂类 `SmsService`，具有以下特性：

1. **多提供商支持**：支持创蓝短信和阿里云短信
2. **参数选择**：可以通过参数指定使用特定的短信发送方式
3. **自动轮询**：当未指定发送方式时，使用Redis实现的轮询机制自动切换提供商
4. **易于扩展**：通过枚举和Spring容器管理，方便添加新的短信提供商

## 核心组件

### 1. SmsProviderEnum - 短信提供商枚举
定义了所有支持的短信提供商：
- `CHUANGLAN`: 创蓝短信
- `ALI`: 阿里云短信

### 2. SmsService - 短信服务工厂类
核心服务类，提供以下功能：
- `sendSms(Sms sms, String providerCode)`: 指定提供商发送短信
- `sendSms(Sms sms)`: 使用轮询机制发送短信
- `getCurrentProvider()`: 获取当前轮询使用的提供商
- `setCurrentProvider(String providerCode)`: 手动设置当前提供商
- `resetRotation()`: 重置轮询状态

### 3. 短信发送器实现
- `ChuangLanSmsSender`: 创蓝短信发送器（已存在）
- `AliSender`: 阿里云短信发送器（已重构实现SmsSender接口）

## 使用方式

### 1. 基本使用 - 轮询机制

```java
@Autowired
private SmsService smsService;

// 创建短信对象
Sms sms = new Sms();
sms.setPhone("13800138000");
sms.setContent("您的验证码是：123456");
sms.setType(SMSTypeEnum.NOTIFY);

// 使用轮询机制发送（第一次用创蓝，第二次用阿里云，第三次又用创蓝...）
boolean result = smsService.sendSms(sms);
```

### 2. 指定提供商发送

```java
// 指定使用创蓝短信
boolean result1 = smsService.sendSms(sms, "CHUANGLAN");

// 指定使用阿里云短信
boolean result2 = smsService.sendSms(sms, "ALI");
```

### 3. 模板短信发送

```java
Sms templateSms = new Sms();
templateSms.setPhone("13800138000");
templateSms.setSceneId(1L); // 短信场景ID
templateSms.setArgs(Arrays.asList("张三", "123456")); // 模板参数
templateSms.setType(SMSTypeEnum.NOTIFY);

// 使用轮询机制发送模板短信
boolean result = smsService.sendSms(templateSms);
```

### 4. 轮询状态管理

```java
// 获取当前轮询使用的提供商
SmsProviderEnum currentProvider = smsService.getCurrentProvider();

// 手动设置当前提供商
smsService.setCurrentProvider("ALI");

// 重置轮询状态（下次发送将从第一个提供商开始）
smsService.resetRotation();
```

## REST API 接口

项目提供了完整的REST API接口，可以通过HTTP请求使用短信服务：

### 1. 发送短信（轮询）
```
POST /api/sms/send
参数：phone=13800138000&content=测试短信
```

### 2. 发送短信（指定提供商）
```
POST /api/sms/send/CHUANGLAN
参数：phone=13800138000&content=测试短信
```

### 3. 发送模板短信
```
POST /api/sms/send/template
参数：phone=13800138000&sceneId=1&args=参数1&args=参数2
```

### 4. 获取当前提供商
```
GET /api/sms/current-provider
```

### 5. 设置当前提供商
```
POST /api/sms/set-provider/ALI
```

### 6. 重置轮询状态
```
POST /api/sms/reset-rotation
```

### 7. 获取所有提供商
```
GET /api/sms/providers
```

## 轮询机制说明

轮询机制基于Redis实现，具有以下特点：

1. **分布式支持**：多个应用实例共享轮询状态
2. **自动过期**：Redis键设置1小时过期时间，避免长期占用内存
3. **异常处理**：Redis异常时自动降级到默认提供商
4. **线程安全**：Redis操作保证并发安全

轮询顺序：创蓝短信 → 阿里云短信 → 创蓝短信 → ...

## 配置说明

### 1. 创蓝短信配置
在系统参数表中配置以下参数：
- `ChuangLanAccount`: 创蓝账号
- `ChuangLanPassword`: 创蓝密码
- `ChuangLanMarketAccount`: 创蓝营销账号
- `ChuangLanMarketPassword`: 创蓝营销密码

### 2. 阿里云短信配置
在系统参数表中配置以下参数：
- `AliAccessKeyId`: 阿里云AccessKeyId
- `AliAccessKeySecret`: 阿里云AccessKeySecret
- `AliSignName`: 短信签名
- `AliTemplateCode`: 短信模板代码

## 扩展新的短信提供商

1. **创建发送器类**：实现 `SmsSender` 接口
2. **添加枚举值**：在 `SmsProviderEnum` 中添加新的提供商
3. **注册Spring Bean**：使用 `@Component` 注解注册到Spring容器

示例：
```java
@Component
public class NewSmsSender implements SmsSender {
    @Override
    public boolean sendSms(Sms sms) {
        // 实现发送逻辑
        return true;
    }
}

// 在SmsProviderEnum中添加
NEW_PROVIDER("NEW", "新提供商", "com.example.NewSmsSender");
```

## 测试

项目提供了完整的单元测试和集成测试：

- `SmsServiceTest`: 单元测试，使用Mock验证逻辑
- `SmsServiceIntegrationTest`: 集成测试，需要Redis环境

运行测试：
```bash
mvn test
```

## 注意事项

1. **Redis依赖**：轮询机制依赖Redis，请确保Redis服务可用
2. **配置完整性**：请确保各提供商的配置参数完整
3. **异常处理**：发送失败时会记录详细日志，便于排查问题
4. **并发安全**：轮询机制支持并发访问，但建议合理控制并发量
5. **监控告警**：建议对短信发送成功率进行监控和告警
