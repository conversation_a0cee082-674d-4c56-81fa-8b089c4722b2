package com.cosfo.message.web.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.cosfo.message.client.enums.MessageContentTypeEnum;
import com.cosfo.message.common.constants.MqConstants;
import com.cosfo.message.common.enums.MsgSendLogEnum;
import com.cosfo.message.infrastructure.message.dao.MsgSendLogDao;
import com.cosfo.message.infrastructure.message.model.MsgSendLog;
import com.cosfo.message.infrastructure.template.dto.MessageBodyDTO;
import com.cosfo.message.web.domain.dto.SendMessageDTO;
import com.cosfo.message.web.domain.vo.WechatResponseVO;
import com.cosfo.message.web.strategy.SendLogStrategy;
import com.cosfo.message.web.strategy.SendLogStrategyHandler;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * 描述: 根据sendLog发送第三方消息
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@MqListener(topic = MqConstants.Topic.SEND_THIRD_MESSAGE,
        consumerGroup = MqConstants.ConsumeGroup.CONSUME_SEND_MESSAGE,
        maxReconsumeTimesWarnLog = 2
)
public class SendThirdMessageListener extends AbstractMqListener<SendMessageDTO> {

    @Resource
    private MsgSendLogDao msgSendLogDao;

    @Override
    public void process(SendMessageDTO sendMessageDTO) {
        if (Objects.isNull(sendMessageDTO) || Objects.isNull(sendMessageDTO.getMsgSendLogId())) {
            log.info("SendThirdMessageListener 数据异常 sendMessageDTO:{}", JSON.toJSONString(sendMessageDTO));
            return;
        }

        Long msgSendLogId = sendMessageDTO.getMsgSendLogId();
        MsgSendLog msgSendLog = msgSendLogDao.getById(msgSendLogId);
        if (Objects.isNull(msgSendLog)) {
            log.info("SendThirdMessageListener msgSendLog未查询到数据 sendMessageDTO:{}", JSON.toJSONString(sendMessageDTO));
            return;
        }
        if (MsgSendLogEnum.SendStatus.SUCCESS.getValue().equals(msgSendLog.getSendStatus())) {
            log.info("SendThirdMessageListener 该条消息已经发送成功,msgSendLog:{}", JSON.toJSONString(msgSendLog));
            return;
        }

        // 发送消息
        SendLogStrategy strategy = SendLogStrategyHandler.getStrategyByChannelType(msgSendLog.getChannleType(), msgSendLog.getContentType());
        log.info("SendThirdMessageListener 开始发送消息 sendMessageDTO:{}", JSON.toJSONString(sendMessageDTO));
        strategy.messageSend(msgSendLog);
    }

}