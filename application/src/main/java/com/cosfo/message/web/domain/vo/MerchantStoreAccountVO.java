package com.cosfo.message.web.domain.vo;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Data;

/**
 * 描述:mch_store_account表
 */
@Data
public class MerchantStoreAccountVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 账号名称
     */
    private String accountName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 账号类型：0、店长 1、店员
     */
    private Integer type;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * open id
     */
    private String openId;

    /**
     * union id
     */
    private String unionId;

    /**
     * 0、审核中 1、审核通过 2、审核拒绝
     */
    private Integer status;
}
