package com.cosfo.message.web.domain.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 模板详情VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-31
 */
@Data
public class MsgPublicTemplateDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key（帆台模板id）
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /**
     * 微信模版名称(模板标题)
     */
    private String wechatTitle;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 场景说明
     */
    private String scene;

    /**
     * 关键词
     */

    private String keywords;
}
