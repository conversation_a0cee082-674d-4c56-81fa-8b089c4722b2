package com.cosfo.message.web.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 已关联、未关联、创建失败小程序信息返回列表VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-31
 * 已关联、未关联、创建失败小程序信息返回列表VO
 */
@Data
public class MsgTemplateWechatAppListVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key（模板id）
     */
    private Long id;


    /**
     * 微信模版名称(标题、小程序模板标题)
     */
    private String wechatTitle;

    /**
     * 关键词
     */
    private String keywords;

    /**
     * 更新时间(状态获取时间、创建失败时间)
     */
    private LocalDateTime updateTime;

    /**
     * 失败原因
     */
    private String reason;

    /**
     * 租户id（商城ID）
     */
    private Long tenantId;

    /**
     * 是否可用0=不可用;1=可用
     * 即0=已删除;1=生效中
     */
    private Integer availableStatus;

    /**
     * 父模版id、微信模板池id
     */
    private Long pId;

    /**
     * 微信模版所属类目 名称
     */
    private String wechatCategoryName;

    /**
     * 微信小程序帆台绑定时间(小程序授权时间)
     */
    private LocalDateTime bindTime;

    /**
     * 创建人id
     */
    private Long creator;

    /**
     * 商城名称
     */
    private String mallName;

    /**
     * 小程序appid
     */
    private String wechatAppId;


}
