package com.cosfo.message.web.facade.converter;

import com.cosfo.message.web.domain.vo.MerchantStoreAccountVO;
import com.cosfo.message.web.domain.vo.MerchantStoreVO;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;

/**
 * 用户中心转换类
 */
public class UserCenterConverter {

    public static MerchantStoreAccountVO merchantStoreAccountResultResp2VO(MerchantStoreAccountResultResp e) {
        MerchantStoreAccountVO vo = new MerchantStoreAccountVO();
        vo.setId(e.getId());
        vo.setTenantId(e.getTenantId());
        vo.setStoreId(e.getStoreId());
        vo.setAccountName(e.getAccountName());
        vo.setPhone(e.getPhone());
        vo.setType(e.getType());
        vo.setRegisterTime(e.getRegisterTime());
        vo.setAuditTime(e.getAuditTime());
        vo.setOpenId(e.getOpenId());
        vo.setUnionId(e.getUnionId());
        vo.setStatus(e.getStatus());
        return vo;
    }

    public static MerchantStoreVO merchantStoreResp2VO(MerchantStoreResultResp e) {
        MerchantStoreVO vo = new MerchantStoreVO();
        vo.setId(e.getId());
        vo.setTenantId(e.getTenantId());
        vo.setStoreName(e.getStoreName());
        vo.setType(e.getType());
        vo.setRegisterTime(e.getRegisterTime());
        vo.setStatus(e.getStatus());
        vo.setAuditRemark(e.getAuditRemark());
        vo.setAuditTime(e.getAuditTime());
        vo.setBillSwitch(e.getBillSwitch());
        vo.setOnlinePayment(e.getOnlinePayment());
        return vo;
    }

}
