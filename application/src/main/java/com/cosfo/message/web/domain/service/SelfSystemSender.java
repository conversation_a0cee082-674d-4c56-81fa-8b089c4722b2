package com.cosfo.message.web.domain.service;

import com.cosfo.message.common.enums.MsgSendLogEnum;
import com.cosfo.message.infrastructure.message.dao.MsgSendLogDao;
import com.cosfo.message.infrastructure.message.dto.MsgSendLogDTO;
import com.cosfo.message.infrastructure.message.model.MsgSendLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cosfo.message.web.domain.converter.MessageCoverter;

/**
 * 小程序内部消息
 */
@Service
public class SelfSystemSender implements SendHandler {

    @Autowired
    private MsgSendLogDao msgSendLogDao;

    @Override
    public MsgSendLogEnum.ChannleType getChannleType() {
        return MsgSendLogEnum.ChannleType.SELF_SYSTEM;
    }

    @Override
    public void send(MsgSendLogDTO dto) {
        MsgSendLog msgSendLog = MessageCoverter.msgSendLogDTO2Entity(dto);
        msgSendLog.setSendStatus(MsgSendLogEnum.SendStatus.SUCCESS.getValue());
        msgSendLogDao.save(msgSendLog);
    }
}