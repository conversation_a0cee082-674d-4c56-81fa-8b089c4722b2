package com.cosfo.message.web.facade;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreQueryProvider;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserCenterMerchantStoreFacade {

    @DubboReference
    private MerchantStoreQueryProvider merchantStoreQueryProvider;

    /**
     * 获取门店列表
     *
     * @param ids
     * @return
     */
    public List<MerchantStoreResultResp> getByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        DubboResponse<List<MerchantStoreResultResp>> response = merchantStoreQueryProvider.getMerchantStoreByIds(ids);
        if (!response.isSuccess()) {
            throw new BizException("获取门店信息失败");
        }
        return response.getData();
    }
}
