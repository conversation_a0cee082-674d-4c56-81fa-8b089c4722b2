package com.cosfo.message.web.domain.service;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.message.common.constants.WechatTemplateConstants;
import com.cosfo.message.common.enums.MsgSendLogEnum;
import com.cosfo.message.infrastructure.message.dao.MsgSendLogDao;
import com.cosfo.message.infrastructure.message.dto.MsgSendLogDTO;
import com.cosfo.message.infrastructure.message.model.MsgSendLog;
import com.cosfo.message.web.domain.converter.MessageCoverter;
import com.cosfo.message.web.domain.vo.WechatResponseVO;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import cn.hutool.http.HttpResponse;
import com.cosfo.message.infrastructure.message.dto.WechatSendReqDTO;

import java.util.HashMap;
import java.util.Map;

/**
 * 微信服务
 */
@Service
public class WeChatSender implements SendHandler {

    @Autowired
    private MsgSendLogDao msgSendLogDao;
    @Value("${wechat.templet.sendEvn}")
    private String sendEvn;

    @Override
    public MsgSendLogEnum.ChannleType getChannleType() {
        return MsgSendLogEnum.ChannleType.WECHAT;
    }

    @Override
    public void send(MsgSendLogDTO dto) {
        MsgSendLog msgSendLog = MessageCoverter.msgSendLogDTO2Entity(dto);

        msgSendLog.setSendStatus(MsgSendLogEnum.SendStatus.FAIL.getValue());
        String req = buildReq(dto);
        msgSendLog.setReq(req);
        msgSendLogDao.save(msgSendLog);

        HttpResponse response = HttpUtil.createPost(WechatTemplateConstants.sendMsg + "?access_token=" + dto.getAccessToken())
                .contentType(ContentType.APPLICATION_JSON.toString())
                .body(req).timeout (3000).execute();
        WechatResponseVO responseVO = JSONObject.parseObject(response.body(), WechatResponseVO.class);

        msgSendLog.setResp(response.body());
        if(responseVO.getErrcode().equals(0)){
            msgSendLog.setSendStatus(MsgSendLogEnum.SendStatus.SUCCESS.getValue());
        }
        msgSendLogDao.updateById(msgSendLog);
    }

    private String buildReq(MsgSendLogDTO dto) {
        WechatSendReqDTO wechatSendReqDTO = new WechatSendReqDTO();
        wechatSendReqDTO.setTouser(dto.getReceiveThridUid());
        wechatSendReqDTO.setTemplate_id(dto.getThirdTemplateId());
        wechatSendReqDTO.setPage(dto.getPage() + "?id=" + dto.getPageId());
        wechatSendReqDTO.setMiniprogram_state(sendEvn);
        wechatSendReqDTO.setLang("zh_CN");
        wechatSendReqDTO.setData(JSONObject.parseObject(dto.getData(), Map.class));
        return JSONObject.toJSONString(wechatSendReqDTO);
    }
}