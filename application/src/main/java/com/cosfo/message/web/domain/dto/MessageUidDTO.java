package com.cosfo.message.web.domain.dto;

import com.google.common.collect.Table;
import lombok.Data;
import net.xianmu.authentication.client.resp.AuthUserAuthResp;
import net.xianmu.authentication.client.resp.UserAuthBaseResp;
import org.apache.commons.lang3.tuple.Triple;

/**
 * @Author: fansongsong
 * @Date: 2023-10-17
 * @Description:
 */
@Data
public class MessageUidDTO {
    /**
     * 飞书消息用户信息
     */
    private Triple<Table<Integer, String, UserAuthBaseResp>, Table<Integer, String, UserAuthBaseResp>, Table<Integer, String, UserAuthBaseResp>> uidTriple;

    /**
     * 微信公众号，渠道、手机号、openId映射
     */
    Table<Integer, String, AuthUserAuthResp> phoneTable;
}
