package com.cosfo.message.web.domain.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class MsgSceneVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    private Long id;

    /**
     * 模版池id
     */
    private Long templateId;

    /**
     * 模版类型0=微信
     */
    private Integer templateType;

    /**
     * 模版名称
     */
    private String templateName;

    /**
     * 关键字
     */
    private String keywords;

    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 描述
     */
    private String description;

    /**
     * 场景 状态0=不可用;1=可用
     */
    private Integer sceneStatus;
}
