package com.cosfo.message.web.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date : 2023/2/10 16:34
 * UI场景详情
 * 实际上是小程序绑定的模板的信息
 */
@Data
public class MsgTemplateToAppVO implements Serializable {
    private static final long serialVersionUID = -4949695681187648346L;

    /**
     * primary key
     * 模板ID
     */
    private Long id;

    /**
     * 租户id（商城ID）
     */
    private Long tenantId;

    /**
     * 商城名称
     */
    private String mallName;

    /**
     * 小程序appid
     */
    private String wechatAppId;

    /**
     * 微信模版名称(标题、小程序模板标题)
     */
    private String wechatTitle;

    /**
     * 关键词
     */
    private String keywords;

    /**
     * 微信模版所属类目 名称
     */
    private String wechatCategoryName;

    /**
     * 是否可用0=不可用;1=可用
     * 即0=已删除;1=生效中
     */
    private Integer availableStatus;

    /**
     * 更新时间(状态获取时间)
     */
    private LocalDateTime updateTime;
}
