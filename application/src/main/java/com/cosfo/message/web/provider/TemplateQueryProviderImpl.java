package com.cosfo.message.web.provider;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.message.client.msgtemplate.provider.MsgTemplateQueryProvider;
import com.cosfo.message.client.msgtemplate.req.*;
import com.cosfo.message.client.msgtemplate.resp.*;
import com.cosfo.message.infrastructure.template.dto.*;
import com.cosfo.message.web.domain.service.TemplateDomainService;
import com.cosfo.message.web.domain.vo.*;
import com.cosfo.message.web.provider.converter.TemplateProviderConverter;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date : 2023/2/13 11:04
 */
@Slf4j
@DubboService
public class TemplateQueryProviderImpl implements MsgTemplateQueryProvider {
    @Resource
    private TemplateDomainService templateDomainService;

    /**
     * 获取微信公共模板池列表
     * @param msgTemplateWechatPublicReq
     * @return
     */
    @Override
    public DubboResponse<PageInfo<MsgPublicTemplateWechatListResultResp>> getWechatPublicMsgTemplateListByCondition(MsgTemplateWechatPublicReq msgTemplateWechatPublicReq) {
        log.error("msgTemplateWechatPublicReq:{}", JSONObject.toJSONString(msgTemplateWechatPublicReq));
        MsgTemplateWechatPublicDTO msgTemplateWechatPublicDTO = TemplateProviderConverter.msgTemplateWechatPublicReq2DTO(msgTemplateWechatPublicReq);
        PageInfo<MsgPublicTemplateWechatListVO> wechatPublicMsgTemplatePage = templateDomainService.getWechatPublicMsgTemplateList(msgTemplateWechatPublicDTO);
        List<MsgPublicTemplateWechatListResultResp> respList = wechatPublicMsgTemplatePage.getList().stream().map(TemplateProviderConverter::msgTemplateWechatPublicVO2Resp).collect(Collectors.toList());
        PageInfo<MsgPublicTemplateWechatListResultResp> pageInfo = (PageInfo<MsgPublicTemplateWechatListResultResp>) TemplateProviderConverter.turnToPageInfo(new PageInfo<MsgPublicTemplateWechatListResultResp>(), wechatPublicMsgTemplatePage);
        pageInfo.setList(respList);
        return DubboResponse.getOK(pageInfo);
    }

    /**
     * 查询帆台模板列表
     * @param wechatMsgTemplateReq
     * @return
     */
    @Override
    public DubboResponse<PageInfo<MsgTemplateWechatListResultResp>> getWechatMsgTemplateListByCondition(WechatMsgTemplateReq wechatMsgTemplateReq) {
        WechatMsgTemplateDTO wechatMsgTemplateDTO = TemplateProviderConverter.wechatMsgTemplateReq2DTO(wechatMsgTemplateReq);
        PageInfo<MsgTemplateWechatListVO> wechatMsgTemplatePage = templateDomainService.getWechatMsgTemplateList(wechatMsgTemplateDTO);
        List<MsgTemplateWechatListResultResp> respList = wechatMsgTemplatePage.getList().stream().map(TemplateProviderConverter::msgTemplateWechatVO2Resp).collect(Collectors.toList());
        PageInfo<MsgTemplateWechatListResultResp> pageInfo = (PageInfo<MsgTemplateWechatListResultResp>) TemplateProviderConverter.turnToPageInfo(new PageInfo<MsgTemplateWechatListResultResp>(), wechatMsgTemplatePage);
        pageInfo.setList(respList);
        return DubboResponse.getOK(pageInfo);
    }


    /**
     * 按模板id获取小程序信息
     * @param id
     * @return
     */
    @Override
    public DubboResponse<MsgTemplateToAppResultResp> getWechatAppInfoByTemplateId(Long id) {
        MsgTemplateToAppVO appVO = templateDomainService.getWechatAppInfoById(id);
        MsgTemplateToAppResultResp msgTemplateToAppResultResp = TemplateProviderConverter.wechatAppVO2Resp(appVO);
        return DubboResponse.getOK(msgTemplateToAppResultResp);
    }


    /**
     * 获取模板详情
     * @param id 帆台模板id
     * @return
     */
    @Override
    public DubboResponse<MsgTemplateWechatDetailResultResp> getWechatMsgTemplateDetailByPublicTemplateId(Long id) {
        MsgPublicTemplateDetailVO wechatMsgTemplateDetailVO = templateDomainService.getWechatMsgTemplateDetailByPublicTemplateId(id);
        return DubboResponse.getOK(TemplateProviderConverter.publicDetailVO2Resp(wechatMsgTemplateDetailVO));
    }

    /**
     * 按帆台模板id和状态获取有效的个人模板id
     * @param id 按帆台模板id
     * @param availableStatus 状态
     * @return
     */
    @Override
    public DubboResponse<List<Long>> getTemplateIdListByStatus(Long id, Integer availableStatus) {
        List<Long> idList = templateDomainService.getTemplateIdListByStatus(id, availableStatus);
        return DubboResponse.getOK(idList);
    }

    /**
     * 根据模板id获取公共模板
     * @param id 模板id
     * @return
     */
    @Override
    public DubboResponse<MsgPublicTemplateWechatListResultResp> getWechatPublicMsgTemplateById(Long id) {
        MsgPublicTemplateWechatListVO templateVO = templateDomainService.getWechatPublicMsgTemplateById(id);
        MsgPublicTemplateWechatListResultResp resultResp = TemplateProviderConverter.msgTemplateWechatPublicVO2Resp(templateVO);
        return DubboResponse.getOK(resultResp);
    }

    /**
     * 根据条件获取个人列表
     * @param wechatMsgTemplateQueryReq
     * @return
     */
    @Override
    public DubboResponse<List<MsgPublicTemplateWechatListResultResp>> getWechatPublicMsgTemplateByCondition(WechatMsgTemplateQueryReq wechatMsgTemplateQueryReq) {
        List<MsgTemplateWechatDetailVO> wechatListByCondition = templateDomainService.getWechatListByCondition(TemplateProviderConverter.wechatMsgTemplateQueryReq2DTO(wechatMsgTemplateQueryReq));
        List<MsgPublicTemplateWechatListResultResp> resultRespList = wechatListByCondition.stream().map(TemplateProviderConverter::msgPublicTemplateResultVO2Resp).collect(Collectors.toList());
        return DubboResponse.getOK(resultRespList);
    }

    /**
     * 根据模板id和租户id列表获取个人模板信息
     * @param id 模板id
     * @param tenantIdList 租户id列表
     * @return 租户id列表
     */
    @Override
    public DubboResponse<List<MsgTemplateToAppResultResp>> getWechatListByTenantIdList(Long id, List<Long> tenantIdList) {
        WechatMsgTemplateQueryDTO wechatMsgTemplateQueryDTO = new WechatMsgTemplateQueryDTO();
        wechatMsgTemplateQueryDTO.setIdList(tenantIdList);
        wechatMsgTemplateQueryDTO.setId(id);
        List<MsgTemplateWechatDetailVO> wechatListByCondition = templateDomainService.getWechatListByCondition(wechatMsgTemplateQueryDTO);
        List<MsgTemplateToAppResultResp> resultRespList = wechatListByCondition.stream().map(TemplateProviderConverter::msgTemplateWechatDetailVO2Resp).collect(Collectors.toList());
        return DubboResponse.getOK(resultRespList);
    }

    /**
     * 获取创建失败的模板
     * @param msgWechatAppReq
     * @return
     */
    @Override
    public DubboResponse<List<MsgTemplateWechatAppListResultResp>> getFailTemplateListByCondition(MsgWechatAppReq msgWechatAppReq) {
        MsgWechatAppDTO msgWechatAppDTO = TemplateProviderConverter.msgWechatAppReq2DTO(msgWechatAppReq);
        List<MsgTemplateWechatAppListVO> failList = templateDomainService.getFailListByCondition(msgWechatAppDTO);
        List<MsgTemplateWechatAppListResultResp> respList = failList.stream().map(TemplateProviderConverter::msgTemplateWechatAppListVO2Resp).collect(Collectors.toList());
        return DubboResponse.getOK(respList);
    }

    @Override
    public DubboResponse<List<Long>> getEnableTenantIdListByTenantIdList(Long fanTaiTemplateId, List<Long> list ,Integer availableStatus) {
        List<Long> tenantIdList = templateDomainService.getEnableTenantIdListByTenantIdList(fanTaiTemplateId, list, availableStatus);
        return DubboResponse.getOK(tenantIdList);
    }
    /**
     * 通过模板id列表查询微信公共模板
     * @param idList
     * @return
     */
    @Override
    public DubboResponse<List<MsgPublicTemplateWechatListResultResp>> getWechatPublicMsgTemplateByIdList(List<Long> idList) {
        if(CollectionUtil.isEmpty (idList)){
            return DubboResponse.getOK(Collections.emptyList ());
        }
        List<MsgPublicTemplateWechatListVO> voList = templateDomainService.getWechatPublicMsgTemplateByIdList(idList);
        List<MsgPublicTemplateWechatListResultResp> respList = voList.stream().map(TemplateProviderConverter::msgTemplateWechatPublicVO2Resp).collect(Collectors.toList());
        return DubboResponse.getOK(respList);
    }

    /**
     * 按帆台模板id获取已关联模板列表
     * @param id
     * @return
     */
    @Override
    public DubboResponse<List<Long>> getRelateIdListByTemplateId(Long id) {
        List<Long> relateIdList = templateDomainService.getRelateIdList(id);
        return DubboResponse.getOK(relateIdList);
    }


}
