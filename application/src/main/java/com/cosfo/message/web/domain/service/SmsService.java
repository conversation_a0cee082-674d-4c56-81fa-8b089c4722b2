package com.cosfo.message.web.domain.service;

import cn.hutool.core.collection.CollectionUtil;
import com.cosfo.mall.common.sms.SmsSender;
import com.cosfo.mall.common.sms.model.Sms;
import com.cosfo.message.client.enums.SmsSceneCodeEnum;
import com.cosfo.message.client.enums.SmsPlatformCodeEnum;
import com.cosfo.message.client.req.SmsBySceneIdReq;
import com.cosfo.message.common.enums.SmsProviderEnum;
import com.cosfo.message.common.enums.SmsSignEnum;
import com.cosfo.message.infrastructure.message.dto.SmsDTO;
import com.cosfo.message.infrastructure.sms.dao.SmsSceneDao;
import com.cosfo.message.infrastructure.sms.model.SmsScene;
import com.cosfo.message.web.domain.enums.RedisKeyEnum;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 短信服务工厂类
 * 支持参数选择发送方式和Redis轮询机制
 * <AUTHOR>
 * @date 2025-08-14
 */
@Service
@Slf4j
public class SmsService {

    private static final String SMS_PROVIDER_ROTATION_KEY = "sms:provider:rotation";
    private static final long ROTATION_KEY_EXPIRE_SECONDS = 3600; // 1小时过期
    private static final Long xianmu = 1L;

    @Autowired
    private SmsSendHandlerFactory smsSendHandlerFactory;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private SmsSceneDao smsSceneDao;

    /**
     * 发送短信 - 支持指定发送方式
     * @param sms 短信对象
     * @param providerCode 发送方式代码，如果为null则使用轮询机制
     * @return 发送结果
     */
    public boolean sendSms(Sms sms, String providerCode) {
        try {
            SmsProviderEnum provider;

            if (providerCode != null && !providerCode.trim().isEmpty()) {
                // 使用指定的发送方式
                provider = SmsProviderEnum.getByCode(providerCode.trim().toUpperCase());
                if (provider == null) {
                    log.error("未找到指定的短信发送提供商: {}", providerCode);
                    return false;
                }
                log.info("使用指定的短信发送提供商: {} 发送短信到: {}", provider.getName(), sms.getPhone());
            } else {
                // 使用轮询机制
                provider = getNextProviderByRotation();
                log.info("使用轮询机制选择短信发送提供商: {} 发送短信到: {}", provider.getName(), sms.getPhone());
            }

            SmsSender sender = getSenderByProvider(provider);
            if (sender == null) {
                log.error("无法获取短信发送器实例: {}", provider.getName());
                return false;
            }

            return sender.sendSms(sms);

        } catch (Exception e) {
            log.error("发送短信异常: phone={}, providerCode={}, error={}",
                    sms.getPhone(), providerCode, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送短信 - 使用轮询机制
     * @param sms 短信对象
     * @return 发送结果
     */
    public boolean sendSms(Sms sms) {
        return sendSms(sms, null);
    }

    /**
     * 通过轮询机制获取下一个发送提供商
     * 使用Redis实现分布式轮询
     * @return 下一个要使用的提供商
     */
    private SmsProviderEnum getNextProviderByRotation() {
        try {
            /**
             * 获取下一个提供商（用于轮询）
             * @param currentProvider 当前提供商
             * @return 下一个提供商
             */
            public static SmsPlatformCodeEnum getNext(SmsPlatformCodeEnum currentProvider) {
                if (currentProvider == null) {
                    return values()[0];
                }

                SmsPlatformCodeEnum[] providers = values();
                for (int i = 0; i < providers.length; i++) {
                    if (providers[i] == currentProvider) {
                        return providers[(i + 1) % providers.length];
                    }
                }
                return providers[0];
            }



            // 更新Redis中的当前提供商，并设置过期时间
            redisTemplate.opsForValue().set(SMS_PROVIDER_ROTATION_KEY, nextProvider.getCode(),
                    ROTATION_KEY_EXPIRE_SECONDS, TimeUnit.SECONDS);

            log.debug("短信提供商轮询: {} -> {}",
                    currentProvider != null ? currentProvider.getName() : "null",
                    nextProvider.getName());

            return nextProvider;

        } catch (Exception e) {
            log.error("获取轮询短信提供商失败，使用默认提供商: {}", e.getMessage(), e);
            // 发生异常时返回默认的第一个提供商
            return SmsProviderEnum.values()[0];
        }
    }

    /**
     * 根据提供商枚举获取对应的发送器实例
     * @param provider 提供商枚举
     * @return 发送器实例
     */
    private SmsSender getSenderByProvider(SmsProviderEnum provider) {
        try {
            // 根据类名从Spring容器中获取Bean
            Class<?> senderClass = Class.forName(provider.getClassName());
            return (SmsSender) applicationContext.getBean(senderClass);
        } catch (ClassNotFoundException e) {
            log.error("未找到短信发送器类: {}", provider.getClassName(), e);
            return null;
        } catch (Exception e) {
            log.error("获取短信发送器实例失败: {}, error: {}", provider.getClassName(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取当前轮询使用的提供商
     * @return 当前提供商，如果未设置则返回null
     */
    public SmsProviderEnum getCurrentProvider() {
        try {
            String currentProviderCode = redisTemplate.opsForValue().get(SMS_PROVIDER_ROTATION_KEY);
            return SmsProviderEnum.getByCode(currentProviderCode);
        } catch (Exception e) {
            log.error("获取当前短信提供商失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 重置轮询状态
     * 清除Redis中的轮询状态，下次发送时将从第一个提供商开始
     */
    public void resetRotation() {
        try {
            redisTemplate.delete(SMS_PROVIDER_ROTATION_KEY);
            log.info("短信提供商轮询状态已重置");
        } catch (Exception e) {
            log.error("重置短信提供商轮询状态失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 手动设置当前轮询提供商
     * @param providerCode 提供商代码
     * @return 设置是否成功
     */
    public boolean setCurrentProvider(String providerCode) {
        try {
            SmsProviderEnum provider = SmsProviderEnum.getByCode(providerCode);
            if (provider == null) {
                log.error("无效的短信提供商代码: {}", providerCode);
                return false;
            }

            redisTemplate.opsForValue().set(SMS_PROVIDER_ROTATION_KEY, provider.getCode(),
                    ROTATION_KEY_EXPIRE_SECONDS, TimeUnit.SECONDS);
            log.info("手动设置当前短信提供商为: {}", provider.getName());
            return true;
        } catch (Exception e) {
            log.error("设置当前短信提供商失败: providerCode={}, error={}", providerCode, e.getMessage(), e);
            return false;
        }
    }

    public void send(SmsPlatformCodeEnum thirdCompany, SmsDTO smsDTO) {
        SmsSendHandler sender = smsSendHandlerFactory.getHandler(thirdCompany).orElseThrow(() -> new BizException ("发送渠道暂不支持"));
        sender.send (smsDTO);
    }

    public void sendBySceneId(Long tenantId, SmsBySceneIdReq req) {
        String sceneCode = req.getSceneCode ().name ();
        List<SmsScene> listByScene = smsSceneDao.getListByScene (sceneCode);
        if(CollectionUtil.isEmpty (listByScene)){
            throw new BizException ("未找到场景");
        }

        String sign = SmsSignEnum.getByTenantId (tenantId);

        List<SmsPlatformCodeEnum> platformCodeEnums = req.getPlatformCodeEnums ();
        if(CollectionUtil.isNotEmpty (platformCodeEnums)){
            listByScene = listByScene.stream().filter (item -> platformCodeEnums.contains (item.getPlatform ())).collect(Collectors.toList());
            if(CollectionUtil.isEmpty (listByScene)){
                throw new BizException ("未找到场景");
            }
        }

        //查询这个场景下上一次这个向这个手机号发送短信的供应商
        String key = RedisKeyEnum.M00003.join (sceneCode, req.getPhone ());

    }
}
