package com.cosfo.message.web.provider;

import com.alibaba.fastjson.JSON;
import com.cosfo.message.client.common.page.req.PageQueryReq;
import com.cosfo.message.client.common.page.resp.PageResp;
import com.cosfo.message.client.provider.NoticeProvider;
import com.cosfo.message.client.req.*;
import com.cosfo.message.client.resp.*;
import com.cosfo.message.infrastructure.base.dto.PageVO;
import com.cosfo.message.infrastructure.notice.dto.*;
import com.cosfo.message.web.domain.service.MessageDomainService;
import com.cosfo.message.web.domain.service.NoticeDomianService;
import com.cosfo.message.web.domain.vo.*;
import com.cosfo.message.web.provider.converter.NoticePrividerConverter;
import com.cosfo.message.web.provider.converter.PageConverter;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import javax.annotation.Resource;

@DubboService
@Slf4j
public class NoticeProviderImpl implements NoticeProvider {
    @Resource
    private NoticeDomianService noticDomianService;
    @Resource
    private MessageDomainService messageDomainService;

    @Override
    public DubboResponse<Void> supportMsgReadLog(MsgReadLogReq req) {
        noticDomianService.supportMsgReadLog(NoticePrividerConverter.msgReadLogReq2DTO(req));
        return DubboResponse.getOK();
    }

    @Override
    public DubboResponse<NoticeDetailResultResp> getNoticeById(MsgReadLogReq req, Boolean needSaveLog) {
        log.info("getNoticeById请求，req={}",JSON.toJSONString(req));
        NoticeDetailVO vo = noticDomianService.getNoticeById(NoticePrividerConverter.msgReadLogReq2DTO(req),needSaveLog);
        NoticeDetailResultResp result = NoticePrividerConverter.noticDetailVO2Resp(vo);
        log.info("getNoticeById请求，req={},resp={}",JSON.toJSONString(req),JSON.toJSONString(result));
        return DubboResponse.getOK(result);
    }

    @Override
    public DubboResponse<Integer> getUnReadNoticeAmountByUserId(Long uId,Long tenantId) {
        return DubboResponse.getOK(messageDomainService.getUnReadNoticAmountByUserId(uId, tenantId));
    }

    @Override
    public DubboResponse<PageResp<NoticeList4ManagerResultResp>> page4Manager(NoticeQuery4ManagerReq req, PageQueryReq pageQueryReq) {
        log.info("page4Manager查询，req={}", JSON.toJSONString(req));
        NoticeQuery4ManagerDTO queryDTO = NoticePrividerConverter.noticeQuery4ManagerDTO2DTO(req);
        PageVO<NoticeList4ManagerVO> pageVO = noticDomianService.page4Manager(queryDTO, PageConverter.outReq2DTO(pageQueryReq));
        PageResp<NoticeList4ManagerResultResp> resp = PageConverter.pageVO2PageResp(pageVO, NoticePrividerConverter::noticList4ManagerVO2Resp);
        log.info("page4Manager查询，req={},resp={}", JSON.toJSONString(req), JSON.toJSONString(resp));
        return DubboResponse.getOK(resp);
    }

    @Override
    public DubboResponse<PageResp<NoticeList4ReceiverResultResp>> page4Receiver(NoticeQuery4ReceiverReq req, PageQueryReq pageQueryReq) {
        NoticeQuery4ReceiverDTO queryDTO = NoticePrividerConverter.noticeQuery4ReceiverReq2DTO(req);
        PageVO<NoticList4ReceiverVO> pageVO = noticDomianService.page4Receiver(queryDTO, PageConverter.outReq2DTO(pageQueryReq));
        return DubboResponse.getOK(PageConverter.pageVO2PageResp(pageVO, NoticePrividerConverter::noticList4ReceiverVO2Resp));
    }

    @Override
    public DubboResponse<Void> deleteNotic(Long id) {
        noticDomianService.deleteNotice(id);
        return DubboResponse.getOK();
    }

    @Override
    public DubboResponse<Long> editNotic(MsgNoticeEditReq req) {
        MsgNoticeEditDTO editDTO = NoticePrividerConverter.msgNoticReq2DTO(req);
        return DubboResponse.getOK(noticDomianService.editNotice(editDTO));
    }

    @Override
    public DubboResponse<PageResp<NoticeReadLogResultResp>> pageReadOrSupportLog(ReadOrSupportLogQueryReq req, PageQueryReq pageQueryReq) {
        ReadOrSupportLogQueryDTO queryDTO = NoticePrividerConverter.readOrSupportLogQueryReq2DTO(req);
        PageVO<NoticeReadLogVO> pageVO = noticDomianService.pageReadOrSupportLog(queryDTO, PageConverter.outReq2DTO(pageQueryReq));
        return DubboResponse.getOK(PageConverter.pageVO2PageResp(pageVO, NoticePrividerConverter::noticeReadLogVO2Resp));
    }

    @Override
    public DubboResponse<PageResp<MsgNoticeReceiverResp>> pageReceiver(MsgNoticeReceiverQueryReq req, PageQueryReq pageQueryReq) {
        MsgNoticeReceiverQueryDTO queryDTO = NoticePrividerConverter.msgNoticeReceiverQueryReq2DTO(req);
        PageVO<MsgNoticeReceiverVO> pageVO = noticDomianService.pageReceiver(queryDTO, PageConverter.outReq2DTO(pageQueryReq));
        return DubboResponse.getOK(PageConverter.pageVO2PageResp(pageVO, NoticePrividerConverter::msgNoticeReceiverVO2Resp));
    }
}
