package com.cosfo.message.web.provider.converter;

import com.alibaba.fastjson.JSON;
import com.cosfo.message.client.enums.MessageContentTypeEnum;
import com.cosfo.message.client.enums.ReadStatusEnum;
import com.cosfo.message.client.req.BatchMarkMsgReq;
import com.cosfo.message.client.req.MsgSendLogQueryReq;
import com.cosfo.message.client.req.NotifyMessageReq;
import com.cosfo.message.client.req.NotifyTipBodyReq;
import com.cosfo.message.client.resp.MsgNotifySendLogResp;
import com.cosfo.message.client.resp.MsgSendLogResp;
import com.cosfo.message.infrastructure.message.dto.MsgBatchUpdateSendLogDTO;
import com.cosfo.message.infrastructure.message.dto.MsgSendLogQueryDTO;
import com.cosfo.message.infrastructure.message.dto.NotifyMessageDTO;
import com.cosfo.message.infrastructure.message.dto.NotifyTipBodyDTO;
import com.cosfo.message.web.domain.vo.MsgSendLogVO;
import org.apache.commons.collections4.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: fansongsong
 * @Date: 2023-07-19
 * @Description:
 */
public class SendLogProviderConvert {

    /**
     *
     * @param msgSendLogVO
     * @return
     */
    public static MsgNotifySendLogResp msgSendLogVO2Resp(MsgSendLogVO msgSendLogVO) {
        MsgNotifySendLogResp msgSendLogResp = new MsgNotifySendLogResp();
        msgSendLogResp.setId(msgSendLogVO.getId());
        msgSendLogResp.setTenantId(msgSendLogVO.getTenantId());
        msgSendLogResp.setTemplateId(msgSendLogVO.getTemplateId());
        msgSendLogResp.setChannleType(msgSendLogVO.getChannleType());
        msgSendLogResp.setContentType(msgSendLogVO.getContentType());
        msgSendLogResp.setPageId(msgSendLogVO.getPageId());
        msgSendLogResp.setPage(msgSendLogVO.getPage());
        msgSendLogResp.setTitle(msgSendLogVO.getTitle());
        msgSendLogResp.setThirdTemplateId(msgSendLogVO.getThirdTemplateId());
        msgSendLogResp.setReceiveAccountId(msgSendLogVO.getReceiveAccountId());
        msgSendLogResp.setReceiveThridUid(msgSendLogVO.getReceiveThridUid());
        msgSendLogResp.setSendAccountId(msgSendLogVO.getSendAccountId());
        msgSendLogResp.setSendThridUid(msgSendLogVO.getSendThridUid());
        msgSendLogResp.setData(msgSendLogVO.getData());
        msgSendLogResp.setSendStatus(msgSendLogVO.getSendStatus());
        msgSendLogResp.setCreateTime(msgSendLogVO.getCreateTime());
        msgSendLogResp.setTemplateType(msgSendLogVO.getTemplateType());
        msgSendLogResp.setStoreId(msgSendLogVO.getStoreId());
        msgSendLogResp.setReadStatus(msgSendLogVO.getReadStatus());
        msgSendLogResp.setReadTime(msgSendLogVO.getReadTime());
        return msgSendLogResp;
    }

    public static NotifyMessageDTO notifyMessageReq2DTO(NotifyMessageReq notifyMessageReq) {
        NotifyMessageDTO notifyMessageDTO = new NotifyMessageDTO();
        notifyMessageDTO.setUniqueId(notifyMessageReq.getUniqueId());
        notifyMessageDTO.setTenantId(notifyMessageReq.getTenantId());
        notifyMessageDTO.setTitle(notifyMessageReq.getTitle());
        notifyMessageDTO.setNotifyTipBodyDTOList(transferDTOList(notifyMessageReq.getDetailList()));
        notifyMessageDTO.setPageId(notifyMessageReq.getPageId());
        notifyMessageDTO.setSubTitle(notifyMessageReq.getSubTitle());
        notifyMessageDTO.setMessageContentType(notifyMessageReq.getMessageContentTypeEnum().getType());
        return notifyMessageDTO;
    }

    private static List<NotifyTipBodyDTO> transferDTOList(List<NotifyTipBodyReq> detailList) {
        if (CollectionUtils.isEmpty(detailList)) {
            return Collections.EMPTY_LIST;
        }

        return detailList.stream().map(req -> NotifyTipBodyDTO.builder().keyCode(req.getKeyCode()).keyValue(req.getKeyValue()).build()).collect(Collectors.toList());
    }

    public static MsgBatchUpdateSendLogDTO batchMarkMsgReq2DTO(BatchMarkMsgReq batchMarkMsgReq) {
        MsgBatchUpdateSendLogDTO msgBatchUpdateSendLogDTO = MsgBatchUpdateSendLogDTO.builder()
                .tenantId(batchMarkMsgReq.getTenantId())
                .ids(batchMarkMsgReq.getIds())
                .readStatus(batchMarkMsgReq.getReadStatusEnum().getStatus()).build();
        return msgBatchUpdateSendLogDTO;
    }

    public static MsgSendLogQueryDTO msgSendLogQueryReq2DTO(MsgSendLogQueryReq msgSendLogQueryReq) {
        MsgSendLogQueryDTO msgSendLogQueryDTO = new MsgSendLogQueryDTO();
        msgSendLogQueryDTO.setReadStatus(Optional.ofNullable(msgSendLogQueryReq.getReadStatusEnum()).map(ReadStatusEnum::getStatus).orElse(null));
        msgSendLogQueryDTO.setTenantId(msgSendLogQueryReq.getTenantId());
        msgSendLogQueryDTO.setStartTime(msgSendLogQueryReq.getStartTime());
        msgSendLogQueryDTO.setEndTime(msgSendLogQueryReq.getEndTime());
        return msgSendLogQueryDTO;
    }
}
