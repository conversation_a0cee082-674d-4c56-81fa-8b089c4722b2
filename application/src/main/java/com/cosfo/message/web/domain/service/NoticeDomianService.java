package com.cosfo.message.web.domain.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cosfo.message.common.constants.MqConstants;
import com.cosfo.message.common.enums.MsgNoticeEnum;
import com.cosfo.message.common.enums.MsgReadLogEnum;
import com.cosfo.message.common.enums.MsgSendLogEnum;
import com.cosfo.message.infrastructure.base.dto.PageVO;
import com.cosfo.message.infrastructure.message.dto.MsgSendLogQueryDTO;
import com.cosfo.message.infrastructure.notice.dao.MsgNoticeReceiverMappingDao;
import com.cosfo.message.infrastructure.notice.dto.*;
import com.cosfo.message.infrastructure.notice.model.MsgNoticeReceiverMapping;
import com.cosfo.message.web.domain.vo.*;
import com.cosfo.message.infrastructure.base.dto.PageQueryDTO;
import com.cosfo.message.infrastructure.notice.dao.MsgNoticeDao;
import com.cosfo.message.infrastructure.notice.dao.MsgReadLogDao;
import com.cosfo.message.infrastructure.notice.model.MsgNotice;
import com.cosfo.message.infrastructure.notice.model.MsgReadLog;
import com.cosfo.message.web.domain.converter.NoticeConverter;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class NoticeDomianService {
    @Resource
    private MsgReadLogDao readLogDao;
    @Resource
    private MsgNoticeDao noticeDao;
    @Resource
    private MsgNoticeReceiverMappingDao noticeReceiverMappingDao;
    @Resource
    private MessageDomainService messageDomainService;
    @Autowired
    private MqProducer mqProducer;

    /**
     * 公告处理记录 - 点赞；取消点赞；
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void supportMsgReadLog(MsgReadLogDTO dto) {
        Long uId = dto.getUId();
        Long tenantId = dto.getTenantId();
        Long noticeId = dto.getNoticeId();

        if (ObjectUtil.isNull(uId)){
            throw new ParamsException("用户id不能为空");
        }
        MsgNotice notice = noticeDao.getById(noticeId);
        if (ObjectUtil.isNull(notice)){
            throw new BizException("公告不存在");
        }
        if(Objects.equals(MsgNoticeEnum.SupportSwitch.CLOST.getValue(),notice.getSupportSwitch())){
            throw new BizException("公告未开启点赞功能");
        }

        //查询该用户对公告的浏览/点赞记录
        List<MsgReadLog> logs = readLogDao.listByUIdAndNoticId(uId,noticeId,tenantId);
        if(Objects.equals(MsgReadLogEnum.ActionType.CANCEL_SUPPORT.getValue(), dto.getActionType())){
            List<Long> logIds = logs.stream().filter(e->Objects.equals(MsgReadLogEnum.ActionType.SUPPORT.getValue(),e.getActionType())).map(MsgReadLog::getId).collect(Collectors.toList());
            readLogDao.removeByIds(logIds);
        }else{
            //如果已经点赞过，就不重复点赞了。
            if(!logs.stream().anyMatch(e->Objects.equals(MsgReadLogEnum.ActionType.SUPPORT.getValue(),e.getActionType()))) {
                MsgReadLog readLog = NoticeConverter.msgReadLogDTO2Entity(dto);
                readLogDao.save(readLog);
            }
        }
        messageDomainService.readed(uId,noticeId,tenantId);
    }

    @Transactional(rollbackFor = Exception.class)
    public NoticeDetailVO getNoticeById(MsgReadLogDTO dto, Boolean needSaveLog) {
        Long uId = dto.getUId();
        Long tenantId = dto.getTenantId();
        Long noticeId = dto.getNoticeId();

        if (needSaveLog && ObjectUtil.isNull(uId)){
            throw new ParamsException("用户id不能为空");
        }
        MsgNotice notice = noticeDao.getById(noticeId);
        if (ObjectUtil.isNull(notice)){
            throw new BizException("公告不存在");
        }

        //需要保存浏览记录
        if(needSaveLog){
            MsgReadLog readLog = NoticeConverter.msgReadLogDTO2Entity(dto);
            readLogDao.save(readLog);
            messageDomainService.readed(uId, noticeId, tenantId);
        }
        //查询用户收到的公告前后记录
        Long beforeNoticeId = ObjectUtil.isNotNull(uId)?messageDomainService.getBeforeNoticeId(uId, noticeId, tenantId):null;
        Long afterNoticeId = ObjectUtil.isNotNull(uId)?messageDomainService.getAfterNoticeId(uId, noticeId, tenantId):null;

        //查询该公告的阅读数和点赞数
        Map<Long, Long> supportAmountMap = readLogDao.countByNoticIdsAndActionType(Collections.singletonList(noticeId), MsgReadLogEnum.ActionType.SUPPORT.getValue());
        Integer supportAmount = CollectionUtil.isEmpty(supportAmountMap)?0:supportAmountMap.get(noticeId).intValue();
        Map<Long, Long> readAmounMap = readLogDao.countByNoticIdsAndActionType(Collections.singletonList(noticeId), MsgReadLogEnum.ActionType.READ.getValue());
        Integer readAmoun = CollectionUtil.isEmpty(readAmounMap)?0:readAmounMap.get(noticeId).intValue();

        List<Long> receiverIds = noticeReceiverMappingDao.listByNoticeIds(Collections.singletonList(noticeId)).stream().map(MsgNoticeReceiverMapping::getReceiverId).collect(Collectors.toList());
        if(receiverIds.contains(0L)){
            receiverIds = Collections.singletonList(0L);
        }

        List<MsgReadLog> logs = readLogDao.listByUIdAndNoticId(uId,noticeId,tenantId);
        boolean supportStatus = logs.stream().filter(e -> Objects.equals(MsgReadLogEnum.ActionType.SUPPORT.getValue(), e.getActionType())).findAny().isPresent();

        return NoticeConverter.buildNoticeDetailVO(notice,beforeNoticeId,afterNoticeId,supportAmount,readAmoun,receiverIds,supportStatus);
    }

    public PageVO<NoticList4ReceiverVO> page4Receiver(NoticeQuery4ReceiverDTO req, PageQueryDTO pageQueryDTO) {
        //查询 用户所有收到的小程序内部通知
        MsgSendLogQueryDTO dto = new MsgSendLogQueryDTO();
        //系统类型的消息发送记录只发送一次。
        dto.setChannleType(MsgSendLogEnum.ChannleType.SELF_SYSTEM.getValue());
        dto.setContentType(MsgSendLogEnum.ContentType.NOTIC.getValue());
        dto.setSendStatus(MsgSendLogEnum.SendStatus.SUCCESS.getValue());
        dto.setReadStatus(req.getReadStatus());
        dto.setReceiveAccountId(req.getRecevieUid());
        dto.setTenantId(req.getTenantId());

        PageVO page = messageDomainService.pageSendLog(dto, pageQueryDTO);
        List<MsgSendLogVO> logList = page.getData();
        if(CollectionUtil.isEmpty(logList)){
            return PageVO.getNullResult(pageQueryDTO.getPageIndex(),pageQueryDTO.getPageSize());
        }

        Map<Long, Integer> noticeReadMap = logList.stream().collect(Collectors.toMap(MsgSendLogVO::getPageId,MsgSendLogVO::getReadStatus));
        List<MsgSendLogVO> sorted = logList.stream().sorted(Comparator.comparing(MsgSendLogVO::getId).reversed()).collect(Collectors.toList());
        List<NoticList4ReceiverVO> list = new ArrayList<>();
        for (MsgSendLogVO vo : sorted){
            NoticList4ReceiverVO noticList4ReceiverVO = NoticeConverter.buildNoticList4ReceiverVO(vo, noticeReadMap);
            list.add(noticList4ReceiverVO);
        }
        page.setData(list);
        return page;
    }

    public PageVO<NoticeList4ManagerVO> page4Manager(NoticeQuery4ManagerDTO queryDTO, PageQueryDTO pageQueryDTO) {
        IPage<MsgNotice> page = noticeDao.page(queryDTO,pageQueryDTO);
        List<MsgNotice> records = page.getRecords();
        if(CollectionUtil.isEmpty(records)){
            return PageVO.getNullResult(pageQueryDTO.getPageIndex(),pageQueryDTO.getPageSize());
        }
        List<Long> noticeIds = records.stream().map(MsgNotice::getId).collect(Collectors.toList());
        Map<Long, Long> supportAmountMap = readLogDao.countByNoticIdsAndActionType(noticeIds, MsgReadLogEnum.ActionType.SUPPORT.getValue());
        Map<Long, Long> readAmounMap = readLogDao.countByNoticIdsAndActionType(noticeIds, MsgReadLogEnum.ActionType.READ.getValue());
        List<MsgNoticeReceiverMapping> msgNoticeReceiverMappings = noticeReceiverMappingDao.listByNoticeIds(noticeIds);
        Map<Long, List<MsgNoticeReceiverMapping>> storeAmountMap = msgNoticeReceiverMappings.stream().collect(Collectors.groupingBy(MsgNoticeReceiverMapping::getNoticeId));

        return PageVO.page2VO(page,e->NoticeConverter.buildNoticList4ManagerVO(e,supportAmountMap,readAmounMap,storeAmountMap));
    }
    @Transactional(rollbackFor = Exception.class)
    public void deleteNotice(Long id) {
        MsgNotice notice = noticeDao.getById(id);
        if (ObjectUtil.isNull(notice)){
            throw new BizException("公告不存在");
        }
        noticeDao.removeById(id);
        noticeReceiverMappingDao.removeByNoticId(id);
        readLogDao.deleteByContentId(id);
        messageDomainService.deleteSendLogByPageId(id, MsgSendLogEnum.ContentType.NOTIC.getValue());
    }
    @Transactional(rollbackFor = Exception.class)
    public Long editNotice(MsgNoticeEditDTO dto) {
        LocalDateTime now = LocalDateTime.now();
        if(Objects.equals(MsgNoticeEnum.PushType.NOW.getValue(), dto.getPushType())){
            dto.setPushStatus(MsgNoticeEnum.PushStatus.READY.getValue());
            dto.setPushTime(now);
        }
        List<Long> storeIds = dto.getStoreIds();
        if(ObjectUtil.isNotNull(dto.getPushStatus()) && MsgNoticeEnum.PushStatus.READY.getValue().equals(dto.getPushStatus())) {
            if(CollectionUtil.isEmpty(storeIds)){
                throw new BizException("请选择门店");
            }
        }
        if(ObjectUtil.isNotNull(dto.getId())) {
            MsgNotice noticeDB = noticeDao.getById(dto.getId());
            if (ObjectUtil.isNull(noticeDB)){
                throw new BizException("公告不存在");
            }
            if (MsgNoticeEnum.PushStatus.PUSHED.getValue().equals(noticeDB.getPushStatus())){
                throw new BizException("公告已发布不允许修改");
            }
            noticeReceiverMappingDao.removeByNoticId(dto.getId());
        }
        MsgNotice notice = NoticeConverter.msgNoticeDTO2Entity(dto);
        if(ObjectUtil.isNotNull(dto.getPushTime())) {
            if (dto.getPushTime().isBefore(now)){
                throw new BizException("请修改发布时间为未来时哦");
            }
        }
        noticeDao.saveOrUpdate(notice);

        if(!CollectionUtil.isEmpty(storeIds)){
            List<MsgNoticeReceiverMapping> mappingList = storeIds.stream().map(e-> NoticeConverter.msgNoticeReceiverMapping2Entity(e,dto.getReceiverType(),notice.getId())).collect(Collectors.toList());
            noticeReceiverMappingDao.saveBatch(mappingList);
        }

        LocalDateTime pushTime = notice.getPushTime ();
        if (ObjectUtil.isNotNull(pushTime) && MsgNoticeEnum.PushStatus.READY.getValue().equals(notice.getPushStatus())) {
            LocalDateTime day39 = now.plusDays(39);
            mqProducer.sendStartDeliver(MqConstants.Topic.SEND_NOTICE,null , JSON.toJSON(notice),pushTime.isAfter(day39) ? day39 : pushTime.plusSeconds(1));
        }
        return notice.getId();
    }

    public PageVO<MsgNoticeReceiverVO> pageReceiver(MsgNoticeReceiverQueryDTO queryDTO, PageQueryDTO pageQueryDTO) {
        Long noticeId = queryDTO.getNoticeId ();
        if (ObjectUtil.isNull(noticeId)){
            throw new ParamsException("公告id不能为空");
        }
        List<MsgReadLog> supportLogs = readLogDao.listByNoticIdsAndActionType(Collections.singletonList(queryDTO.getNoticeId()), MsgReadLogEnum.ActionType.SUPPORT.getValue()).get(queryDTO.getNoticeId());
        Map<Long, List<MsgReadLog>> supportStoreMap;
        if (CollectionUtil.isNotEmpty(supportLogs)) {
            supportStoreMap = supportLogs.stream().collect(Collectors.groupingBy(MsgReadLog::getStoreId));
        } else {
            supportStoreMap = Collections.emptyMap();
        }
        Boolean supportFlag = queryDTO.getSupportFlag();
        if(ObjectUtil.isNotEmpty(supportFlag)){
            Set<Long> supportStoreIds = CollectionUtil.isNotEmpty(supportLogs)?supportLogs.stream().map(MsgReadLog::getStoreId).collect(Collectors.toSet()):Collections.emptySet();
            if (supportFlag) {
                //如果查询已经点赞的,则和传入的storeid取交集
                List<Long> receiverIds = queryDTO.getReceiverIds();
                if (CollectionUtil.isEmpty(receiverIds)) {
                    queryDTO.setReceiverIds(new ArrayList<>(supportStoreIds));
                } else {
                    HashSet<Long> resSet = new HashSet<>();
                    resSet.addAll(receiverIds);
                    resSet.retainAll(supportStoreIds);
                    queryDTO.setReceiverIds(new ArrayList<>(resSet));
                }
                if (CollectionUtil.isEmpty(queryDTO.getReceiverIds())) {
                    return PageVO.getNullResult(pageQueryDTO.getPageIndex(), pageQueryDTO.getPageSize());
                }
            } else {
                queryDTO.setNotInReceiverIds(supportStoreIds);
            }
        }


        List<MsgReadLog> readLogs  = readLogDao.listByNoticIdsAndActionType(Collections.singletonList(queryDTO.getNoticeId()), MsgReadLogEnum.ActionType.READ.getValue()).get(queryDTO.getNoticeId());
        Map<Long, List<MsgReadLog>> readStoreMap;
        if (CollectionUtil.isNotEmpty(readLogs)) {
            readStoreMap = readLogs.stream().collect(Collectors.groupingBy(MsgReadLog::getStoreId));
        } else {
            readStoreMap = Collections.emptyMap();
        }
        Boolean readFlag = queryDTO.getReadFlag();
        if(ObjectUtil.isNotEmpty(readFlag)){
            Set<Long> readStoreIds = CollectionUtil.isNotEmpty(readLogs)?readLogs.stream().map(MsgReadLog::getStoreId).collect(Collectors.toSet()):Collections.emptySet();
            if (readFlag) {
                //如果查询已经点赞的,则和传入的storeid取交集
                List<Long> receiverIds = queryDTO.getReceiverIds();
                if (CollectionUtil.isEmpty(receiverIds)) {
                    queryDTO.setReceiverIds(new ArrayList<>(readStoreIds));
                } else {
                    HashSet<Long> resSet = new HashSet<>();
                    resSet.addAll(receiverIds);
                    resSet.retainAll(readStoreIds);
                    queryDTO.setReceiverIds(new ArrayList<>(resSet));
                }
                if (CollectionUtil.isEmpty(queryDTO.getReceiverIds())) {
                    return PageVO.getNullResult(pageQueryDTO.getPageIndex(), pageQueryDTO.getPageSize());
                }
            } else {
                queryDTO.setNotInReceiverIds(readStoreIds);
            }
        }
        IPage<MsgNoticeReceiverMapping> page = noticeReceiverMappingDao.page(queryDTO,pageQueryDTO);
        List<MsgNoticeReceiverMapping> records = page.getRecords();
        if(CollectionUtil.isEmpty(records)){
            return PageVO.getNullResult(pageQueryDTO.getPageIndex(),pageQueryDTO.getPageSize());
        }

        return PageVO.page2VO(page, e->NoticeConverter.buildMsgNoticeReceiverVO(e, readStoreMap.get(e.getReceiverId()),supportStoreMap.get(e.getReceiverId())));
    }

    public PageVO<NoticeReadLogVO> pageReadOrSupportLog(ReadOrSupportLogQueryDTO queryDTO, PageQueryDTO pageQueryDTO) {
        Long noticeId = queryDTO.getNoticeId ();
        if (ObjectUtil.isNull(noticeId)){
            throw new ParamsException("公告id不能为空");
        }
        IPage<MsgReadLog> page = readLogDao.pageByCondition(queryDTO,pageQueryDTO);
        List<MsgReadLog> records = page.getRecords();
        if(CollectionUtil.isEmpty(records)){
            return PageVO.getNullResult(pageQueryDTO.getPageIndex(),pageQueryDTO.getPageSize());
        }
        return PageVO.page2VO(page, NoticeConverter::buildMsgReadLog);
    }
}
