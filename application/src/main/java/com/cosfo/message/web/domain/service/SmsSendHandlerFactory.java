package com.cosfo.message.web.domain.service;

import com.cosfo.message.client.enums.SmsPlatformCodeEnum;
import com.cosfo.message.common.enums.MsgSendLogEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Component
@Slf4j
public class SmsSendHandlerFactory implements ApplicationContextAware {

    private Map<SmsPlatformCodeEnum, SmsSendHandler> handlerMap = new HashMap<>();


    private ApplicationContext applicationContext;
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @PostConstruct
    private void init() {
        applicationContext.getBeansOfType(SmsSendHandler.class).forEach((a, b) -> handlerMap.put(b.getSmsThirdCompanyCode (), b));
    }


    public Optional<SmsSendHandler> getHandler(SmsPlatformCodeEnum codeEnum) {
        return Optional.ofNullable(handlerMap.get(codeEnum));
    }
}
