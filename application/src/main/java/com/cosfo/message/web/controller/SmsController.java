package com.cosfo.message.web.controller;

import com.cosfo.mall.common.context.SMSTypeEnum;
import com.cosfo.mall.common.sms.model.Sms;
import com.cosfo.message.common.enums.SmsProviderEnum;
import com.cosfo.message.web.domain.service.SmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 短信发送控制器 - 使用示例
 * <AUTHOR>
 * @date 2025-08-14
 */
@RestController
@RequestMapping("/api/sms")
@Slf4j
public class SmsController {

    @Autowired
    private SmsService smsService;

    /**
     * 发送短信 - 使用轮询机制
     * @param phone 手机号
     * @param content 短信内容
     * @return 发送结果
     */
    @PostMapping("/send")
    public Map<String, Object> sendSms(@RequestParam String phone, 
                                       @RequestParam String content) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Sms sms = new Sms();
            sms.setPhone(phone);
            sms.setContent(content);
            sms.setType(SMSTypeEnum.NOTIFY);
            
            boolean success = smsService.sendSms(sms);
            
            result.put("success", success);
            result.put("message", success ? "短信发送成功" : "短信发送失败");
            result.put("provider", smsService.getCurrentProvider() != null ? 
                    smsService.getCurrentProvider().getName() : "未知");
            
        } catch (Exception e) {
            log.error("发送短信异常: phone={}, error={}", phone, e.getMessage(), e);
            result.put("success", false);
            result.put("message", "发送异常: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 发送短信 - 指定发送方式
     * @param phone 手机号
     * @param content 短信内容
     * @param provider 发送方式 (CHUANGLAN/ALI)
     * @return 发送结果
     */
    @PostMapping("/send/{provider}")
    public Map<String, Object> sendSmsWithProvider(@RequestParam String phone,
                                                   @RequestParam String content,
                                                   @PathVariable String provider) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Sms sms = new Sms();
            sms.setPhone(phone);
            sms.setContent(content);
            sms.setType(SMSTypeEnum.NOTIFY);
            
            boolean success = smsService.sendSms(sms, provider);
            
            result.put("success", success);
            result.put("message", success ? "短信发送成功" : "短信发送失败");
            result.put("provider", provider);
            
        } catch (Exception e) {
            log.error("发送短信异常: phone={}, provider={}, error={}", phone, provider, e.getMessage(), e);
            result.put("success", false);
            result.put("message", "发送异常: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 发送模板短信 - 使用轮询机制
     * @param phone 手机号
     * @param sceneId 场景ID
     * @param args 模板参数
     * @return 发送结果
     */
    @PostMapping("/send/template")
    public Map<String, Object> sendTemplateSms(@RequestParam String phone,
                                               @RequestParam Long sceneId,
                                               @RequestParam String[] args) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Sms sms = new Sms();
            sms.setPhone(phone);
            sms.setSceneId(sceneId);
            sms.setArgs(Arrays.asList(args));
            sms.setType(SMSTypeEnum.NOTIFY);
            
            boolean success = smsService.sendSms(sms);
            
            result.put("success", success);
            result.put("message", success ? "模板短信发送成功" : "模板短信发送失败");
            result.put("provider", smsService.getCurrentProvider() != null ? 
                    smsService.getCurrentProvider().getName() : "未知");
            
        } catch (Exception e) {
            log.error("发送模板短信异常: phone={}, sceneId={}, error={}", phone, sceneId, e.getMessage(), e);
            result.put("success", false);
            result.put("message", "发送异常: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取当前轮询使用的提供商
     * @return 当前提供商信息
     */
    @GetMapping("/current-provider")
    public Map<String, Object> getCurrentProvider() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            SmsProviderEnum currentProvider = smsService.getCurrentProvider();
            
            if (currentProvider != null) {
                result.put("success", true);
                result.put("provider", currentProvider.getCode());
                result.put("providerName", currentProvider.getName());
            } else {
                result.put("success", true);
                result.put("provider", null);
                result.put("providerName", "未设置");
                result.put("message", "轮询尚未开始，将从第一个提供商开始");
            }
            
        } catch (Exception e) {
            log.error("获取当前提供商异常: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "获取异常: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 设置当前轮询提供商
     * @param provider 提供商代码
     * @return 设置结果
     */
    @PostMapping("/set-provider/{provider}")
    public Map<String, Object> setCurrentProvider(@PathVariable String provider) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = smsService.setCurrentProvider(provider);
            
            result.put("success", success);
            result.put("message", success ? "设置成功" : "设置失败，无效的提供商代码");
            result.put("provider", provider);
            
        } catch (Exception e) {
            log.error("设置当前提供商异常: provider={}, error={}", provider, e.getMessage(), e);
            result.put("success", false);
            result.put("message", "设置异常: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 重置轮询状态
     * @return 重置结果
     */
    @PostMapping("/reset-rotation")
    public Map<String, Object> resetRotation() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            smsService.resetRotation();
            
            result.put("success", true);
            result.put("message", "轮询状态已重置");
            
        } catch (Exception e) {
            log.error("重置轮询状态异常: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "重置异常: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取所有可用的短信提供商
     * @return 提供商列表
     */
    @GetMapping("/providers")
    public Map<String, Object> getProviders() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            SmsProviderEnum[] providers = SmsProviderEnum.values();
            Map<String, String> providerMap = new HashMap<>();
            
            for (SmsProviderEnum provider : providers) {
                providerMap.put(provider.getCode(), provider.getName());
            }
            
            result.put("success", true);
            result.put("providers", providerMap);
            result.put("count", providers.length);
            
        } catch (Exception e) {
            log.error("获取提供商列表异常: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "获取异常: " + e.getMessage());
        }
        
        return result;
    }
}
