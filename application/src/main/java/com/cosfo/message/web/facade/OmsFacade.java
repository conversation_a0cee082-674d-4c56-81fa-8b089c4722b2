package com.cosfo.message.web.facade;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cosfo.message.common.enums.MsgSceneEnum;
import com.cosfo.message.web.domain.vo.MsgSceneTenantVO;
import com.cosfo.message.web.domain.vo.MsgSceneVO;
import com.cosfo.message.web.facade.converter.OmsConverter;
import com.cosfo.oms.client.common.MsgSceneTenantMappingEnum;
import com.cosfo.oms.client.provider.msgscene.MsgSceneQueryProvider;
import com.cosfo.oms.client.provider.tenant.TenantQueryProvider;
import com.cosfo.oms.client.req.MsgSceneQueryReq;
import com.cosfo.oms.client.resp.MsgSceneResultResp;
import com.cosfo.oms.client.resp.MsgSceneTenantResultResp;
import com.cosfo.oms.client.resp.WechatAuthorizerResp;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class OmsFacade {
    @DubboReference
    private TenantQueryProvider tenantQueryProvider;
    @DubboReference
    private MsgSceneQueryProvider msgSceneQueryProvider;

    public Map<Long,String> getTokenByTenantIds(List<Long> tenantIds){
        // 获取租户小程序授权信息
        DubboResponse<List<WechatAuthorizerResp>> response = tenantQueryProvider.getAuthorizerByTenantIds(tenantIds);
        if (!response.isSuccess()){
            throw new BizException(response.getMsg());
        }
        if(CollectionUtil.isEmpty(response.getData())){
            return Collections.emptyMap();
        }
        return response.getData().stream().filter(e-> ObjectUtil.isNotNull(e.getAccessToken())).collect(Collectors.toMap(WechatAuthorizerResp::getTenantId,WechatAuthorizerResp::getAccessToken));
    }

    public MsgSceneVO getMsgSceneById(Long sceneId){
        DubboResponse<MsgSceneResultResp> response = msgSceneQueryProvider.getScene(sceneId);
        if (!response.isSuccess()){
            throw new BizException(response.getMsg());
        }
        MsgSceneResultResp data = response.getData();
        return OmsConverter.msgSceneResp2VO(data);
    }

    public List<MsgSceneTenantVO> listAvailableSceneTenantByTenantId(Long sceneId, Long tenantId, MsgSceneTenantMappingEnum.TemplateType templateType){
        MsgSceneQueryReq req = new MsgSceneQueryReq();
        req.setTemplateType(templateType.getValue());
        req.setSceneId(sceneId);
        req.setAvailableStatus(MsgSceneEnum.AvailableStatus.ABLED.getValue());
        DubboResponse<List<MsgSceneTenantResultResp>> response = msgSceneQueryProvider.listSceneTenantByTenantId(req, tenantId);
        if (!response.isSuccess()){
            throw new BizException(response.getMsg());
        }
        List<MsgSceneTenantResultResp> data = response.getData();
        return OmsConverter.msgSceneTenantResp2VO(data);
    }
}
