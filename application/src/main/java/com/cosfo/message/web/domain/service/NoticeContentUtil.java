package com.cosfo.message.web.domain.service;

import ch.qos.logback.core.joran.conditional.PropertyWrapperForScripts;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.message.common.enums.NoticeContentJsonObjEnum;
import com.cosfo.message.infrastructure.notice.model.MsgNotice;
import com.cosfo.message.web.domain.dto.NoticeContentJsonObj;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

public class NoticeContentUtil {

    public static String getContentString(MsgNotice notice){
        if(ObjectUtil.isNull(notice)){
            return null;
        }
        String content = notice.getContent();
        if(ObjectUtil.isNull(content)){
            return null;
        }
        List<NoticeContentJsonObj> noticeContentJsonObjs = JSONObject.parseArray(content, NoticeContentJsonObj.class);
        Optional<NoticeContentJsonObj> first = noticeContentJsonObjs.stream().filter(e -> Objects.equals(NoticeContentJsonObjEnum.TypeEnum.WORD.getValue(), e.getType())).findFirst();
        if(!first.isPresent()){
            return null;
        }
        String result = (String) first.get().getContent();
        if(result.length()>10) {
            return result.substring(0,10);
        }else{
            return result;
        }
    }
}
