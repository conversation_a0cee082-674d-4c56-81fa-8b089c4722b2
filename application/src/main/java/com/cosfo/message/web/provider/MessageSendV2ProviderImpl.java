package com.cosfo.message.web.provider;

import com.cosfo.message.client.enums.ChannelTypeEnum;
import com.cosfo.message.client.provider.MessageSendProviderV2;
import com.cosfo.message.client.req.MessageBodyReq;
import com.cosfo.message.client.req.MessageUserReq;
import com.cosfo.message.client.resp.MsgSendLogResp;
import com.cosfo.message.infrastructure.template.dto.BatchMessageSendDTO;
import com.cosfo.message.infrastructure.template.dto.BatchMessageSendThirdUidDTO;
import com.cosfo.message.web.domain.service.MessageDomainService;
import com.cosfo.message.web.domain.service.MessageSendService;
import com.cosfo.message.web.domain.vo.MsgSendLogVO;
import com.cosfo.message.web.provider.converter.MessageSendLogProviderConverter;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-10-16
 * @Description:新接口兼容旧功能，改为策略模式去做
 */
@DubboService
@Slf4j
public class MessageSendV2ProviderImpl extends MessageSendService implements MessageSendProviderV2 {

    @Resource
    private MessageDomainService messageDomainService;

    @Override
    public DubboResponse<List<MsgSendLogResp>> batchSendMessage(Long tenantId, ChannelTypeEnum channelTypeEnum, String channelCode, MessageUserReq sender, List<MessageUserReq> receiverList, MessageBodyReq mesg) {
        BatchMessageSendDTO messageSendDTO = MessageSendLogProviderConverter.msgReadLogReq2BatchDTOV2(tenantId, channelTypeEnum, sender, receiverList, mesg, channelCode);
        List<MsgSendLogVO> msgSendLogVOList = messageDomainService.batchCreateSendLog(messageSendDTO);
        return sendLogToMq(msgSendLogVOList);
    }

    @Override
    public DubboResponse<List<MsgSendLogResp>> batchSendMessageByThridUid(Long tenantId, ChannelTypeEnum channelTypeEnum, String channelCode, String sender, List<String> receiverList, MessageBodyReq mesg, SystemOriginEnum systemOriginEnum) {
        BatchMessageSendThirdUidDTO messageSendDTO = MessageSendLogProviderConverter.msgReadLogReq2BatchUidDTOV2(tenantId, channelTypeEnum, sender, receiverList, mesg, systemOriginEnum, channelCode);
        List<MsgSendLogVO> msgSendLogVOList = messageDomainService.batchCreateSendLogByUid(messageSendDTO);
        return sendLogToMq(msgSendLogVOList);
    }
}
