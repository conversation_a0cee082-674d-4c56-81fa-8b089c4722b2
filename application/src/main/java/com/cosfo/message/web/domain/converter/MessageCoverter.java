package com.cosfo.message.web.domain.converter;

import com.alibaba.fastjson.JSON;
import com.cosfo.message.client.enums.ChannelTypeEnum;
import com.cosfo.message.common.constants.NumberConstants;
import com.cosfo.message.common.enums.MsgSendLogEnum;
import com.cosfo.message.infrastructure.message.dto.MsgSendLogDTO;
import com.cosfo.message.infrastructure.message.dto.NotifyMessageDTO;
import com.cosfo.message.infrastructure.message.model.MsgSendLog;
import com.cosfo.message.web.domain.dto.MsgTipDataDTO;

import java.util.Optional;

public class MessageCoverter {
    public static MsgSendLog msgSendLogDTO2Entity(MsgSendLogDTO dto) {
        MsgSendLog msgSendLog = new MsgSendLog();
        msgSendLog.setTenantId(dto.getTenantId());
        msgSendLog.setTemplateId(dto.getTemplateId());
        msgSendLog.setChannleType(dto.getChannleType());
        msgSendLog.setContentType(dto.getContentType());
        msgSendLog.setPageId(dto.getPageId());
        msgSendLog.setPage(dto.getPage());
        msgSendLog.setTitle(dto.getTitle());
        msgSendLog.setThirdTemplateId(dto.getThirdTemplateId());
        msgSendLog.setReceiveAccountId(dto.getReceiveAccountId());
        msgSendLog.setReceiveThridUid(dto.getReceiveThridUid());
        msgSendLog.setSendAccountId(dto.getSendAccountId());
        msgSendLog.setSendThridUid(dto.getSendThridUid());
        msgSendLog.setData(dto.getData());
        msgSendLog.setTemplateType(dto.getTemplateType());
        msgSendLog.setStoreId(dto.getStoreId());
        return msgSendLog;
    }

    public static MsgSendLog NotifyMessageDTO2Entity(NotifyMessageDTO dto) {
        MsgSendLog msgSendLog = new MsgSendLog();
        msgSendLog.setTenantId(dto.getTenantId());
        msgSendLog.setTitle(dto.getTitle());
        msgSendLog.setContentType(dto.getMessageContentType());
        MsgTipDataDTO msgTipDataDTO = MsgTipDataDTO.builder().subTitle(dto.getSubTitle()).inputData(dto.getNotifyTipBodyDTOList()).build();
        msgSendLog.setData(JSON.toJSONString(msgTipDataDTO));
        msgSendLog.setBizUniqueNo(dto.getUniqueId());
        msgSendLog.setPageId(dto.getPageId());

        msgSendLog.setChannleType(ChannelTypeEnum.BASIC_NOTIFY.getValue());
        msgSendLog.setReceiveAccountId(NumberConstants.MINUS_ONE);
        msgSendLog.setReceiveThridUid(NumberConstants.MINUS_ONE.toString());
        msgSendLog.setSendThridUid(NumberConstants.MINUS_ONE.toString());
        msgSendLog.setSendAccountId(NumberConstants.MINUS_ONE);
        msgSendLog.setStoreId(NumberConstants.MINUS_ONE);
        msgSendLog.setSendStatus(MsgSendLogEnum.SendStatus.PROCESSING.getValue());

        return msgSendLog;
    }
}
