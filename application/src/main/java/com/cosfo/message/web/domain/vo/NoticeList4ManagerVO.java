package com.cosfo.message.web.domain.vo;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class NoticeList4ManagerVO {
    /**
     * 公告id
     */
    private Long id;
    /**
     * 发布时间
     */
    private LocalDateTime pushTime;
    /**
     * 标题
     */
    private String title;
    /**
     * 内容简略
     */
    private String content;
    /**
     * 消息类型,0=公告消息
     */
    private Integer contentType;

    /**
     * 接收对象 ex：所有门店（120）
     */
    private String receiveStore;
    /**
     * 创建用户id
     */
    private Long createUid;

    /**
     * 编辑用户id
     */
    private Long editUid;

    /**
     * 发布用户id
     */
    private Long pushUid;

    /**
     * 发布类型0=定时发布,1=立即发布
     */
    private Integer pushType;
    /**
     * 阅读数
     */
    private Integer readAmount;

    /**
     * 点赞数
     */
    private Integer supportAmount;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
