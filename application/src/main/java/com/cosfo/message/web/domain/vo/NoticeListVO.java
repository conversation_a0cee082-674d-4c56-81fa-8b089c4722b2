package com.cosfo.message.web.domain.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class NoticeListVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 接收对象 ex：所有门店（120）
     */
    private String receiveStore;

    /**
     * 创建人
     */
    private String createUserName;
    /**
     * 最后编辑人
     */
    private String editUserName;
    /**
     * 发布人
     */
    private String pushUserName;

    /**
     * 发布状态0=定时发布,1=立即发布
     */
    private Integer pushType;
    /**
     * 阅读数
     */
    private Integer readAmount;

    /**
     * 点赞数
     */
    private Integer supportAmount;
}
