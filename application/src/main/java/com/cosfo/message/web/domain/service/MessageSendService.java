package com.cosfo.message.web.domain.service;

import com.cosfo.message.client.resp.MsgSendLogResp;
import com.cosfo.message.common.constants.MqConstants;
import com.cosfo.message.web.domain.dto.SendMessageDTO;
import com.cosfo.message.web.domain.vo.MsgSendLogVO;
import com.cosfo.message.web.provider.converter.MessageSendLogProviderConverter;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.rocketmq.support.producer.MqProducer;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: fansongsong
 * @Date: 2023-10-20
 * @Description:
 */
public abstract class MessageSendService {


    @Resource
    private MqProducer mqProducer;

    public DubboResponse<List<MsgSendLogResp>> sendLogToMq(List<MsgSendLogVO> msgSendLogVOList) {
        List<MsgSendLogResp> respList = msgSendLogVOList.stream().map(MessageSendLogProviderConverter::msgSendLogVOListVO2Resp).collect(Collectors.toList());

        // 循环发送消息
        for (MsgSendLogVO msgSendLogVO : msgSendLogVOList) {
            SendMessageDTO sendMessageDTO = SendMessageDTO.builder().msgSendLogId(msgSendLogVO.getId()).build();
            mqProducer.send(MqConstants.Topic.SEND_THIRD_MESSAGE, null, sendMessageDTO);
        }

        return DubboResponse.getOK(respList);
    }
}
