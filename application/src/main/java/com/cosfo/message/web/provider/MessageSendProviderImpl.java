package com.cosfo.message.web.provider;

import com.cosfo.message.client.enums.ChannelTypeEnum;
import com.cosfo.message.client.provider.MessageSendProvider;
import com.cosfo.message.client.req.MessageBodyReq;
import com.cosfo.message.client.req.MessageUserReq;
import com.cosfo.message.client.resp.MsgSendLogResp;
import com.cosfo.message.common.constants.NumberConstants;
import com.cosfo.message.infrastructure.template.dto.BatchMessageSendDTO;
import com.cosfo.message.infrastructure.template.dto.BatchMessageSendThirdUidDTO;
import com.cosfo.message.web.domain.service.MessageDomainService;
import com.cosfo.message.web.domain.service.MessageSendService;
import com.cosfo.message.web.domain.vo.MsgSendLogVO;
import com.cosfo.message.web.provider.converter.MessageSendLogProviderConverter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.i18n.result.util.I18nDubboResponseUtil;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-07-14
 * @Description:
 */
@DubboService
@Slf4j
public class MessageSendProviderImpl extends MessageSendService implements MessageSendProvider {

    @Resource
    private MessageDomainService messageDomainService;
    @Resource
    private MqProducer mqProducer;

    @Override
    public DubboResponse<List<MsgSendLogResp>> batchSendMessageByThridUid(Long tenantId, ChannelTypeEnum channelTypeEnum, String sender, List<String> receiverList, MessageBodyReq mesg, SystemOriginEnum systemOriginEnum) {
        BatchMessageSendThirdUidDTO messageSendDTO = MessageSendLogProviderConverter.msgReadLogReq2BatchUidDTO(tenantId, channelTypeEnum, sender, receiverList, mesg, systemOriginEnum);

        List<MsgSendLogVO> msgSendLogVOList = messageDomainService.batchCreateSendLogByUid(messageSendDTO);
        return sendLogToMq(msgSendLogVOList);
    }

    @Override
    public DubboResponse<MsgSendLogResp> sendMessageByThridUid(Long tenantId, ChannelTypeEnum channelTypeEnum, String sender, String receiver, MessageBodyReq mesg, SystemOriginEnum systemOriginEnum) {
        List<MsgSendLogResp> sendLogRespList = batchSendMessageByThridUid(tenantId, channelTypeEnum, sender, Lists.newArrayList(receiver), mesg, systemOriginEnum).getData();
        if (!CollectionUtils.isEmpty(sendLogRespList) && sendLogRespList.size() == NumberConstants.ONE) {
            return DubboResponse.getOK(sendLogRespList.get(NumberConstants.ZERO));
        }

        return I18nDubboResponseUtil.getDefaultError("请求异常");
    }

    @Override
    public DubboResponse<List<MsgSendLogResp>> batchSendMessage(Long tenantId, ChannelTypeEnum channelTypeEnum, MessageUserReq sender, List<MessageUserReq> receiverList, MessageBodyReq mesg) {
        BatchMessageSendDTO messageSendDTO = MessageSendLogProviderConverter.msgReadLogReq2BatchDTO(tenantId, channelTypeEnum, sender, receiverList, mesg);

        List<MsgSendLogVO> msgSendLogVOList = messageDomainService.batchCreateSendLog(messageSendDTO);
        return sendLogToMq(msgSendLogVOList);
    }

    @Override
    public DubboResponse<MsgSendLogResp> sendMessage(Long tenantId, ChannelTypeEnum channelTypeEnum, MessageUserReq sender, MessageUserReq receiver, MessageBodyReq mesg) {
        List<MsgSendLogResp> sendLogRespList = batchSendMessage(tenantId, channelTypeEnum, sender, Lists.newArrayList(receiver), mesg).getData();
        if (!CollectionUtils.isEmpty(sendLogRespList) && sendLogRespList.size() == NumberConstants.ONE) {
            return DubboResponse.getOK(sendLogRespList.get(NumberConstants.ZERO));
        }

        return I18nDubboResponseUtil.getDefaultError("请求异常");
    }
}
