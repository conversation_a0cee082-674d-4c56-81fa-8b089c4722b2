package com.cosfo.message.web.provider;

import com.cosfo.message.client.enums.SMSTypeEnum;
import com.cosfo.message.client.enums.SmsPlatformCodeEnum;
import com.cosfo.message.client.enums.SmsPlatformCodeEnum;
import com.cosfo.message.client.provider.SmsSendProvider;
import com.cosfo.message.client.req.SmsBySceneIdReq;
import com.cosfo.message.client.req.SmsByTemplateReq;
import com.cosfo.message.infrastructure.message.dto.SmsDTO;
import com.cosfo.message.web.domain.service.SmsService;
import net.xianmu.common.result.DubboResponse;
import org.springframework.beans.factory.annotation.Autowired;

public class SmsSendProviderImpl implements SmsSendProvider {
    @Autowired
    private SmsService smsService;
    @Override
    public DubboResponse<Void> sendBySceneId(Long tenantId, SmsBySceneIdReq req) {
        smsService.sendBySceneId (tenantId,req);
        return DubboResponse.getOK ();
    }

    @Override
    public DubboResponse<Void> sendByTemplate(Long tenantId, SmsByTemplateReq req) {
        smsService.sendByTemplate (tenantId,req);
        return DubboResponse.getOK ();
    }

    @Override
    public DubboResponse<Void> send(SmsPlatformCodeEnum platformCodeEnum, SMSTypeEnum smsType, String sign, String templateCode, String phone, String content) {
        SmsDTO smsDTO = new SmsDTO ();
        smsDTO.setPhone(phone);
        smsDTO.setTemplateCode(templateCode);
        smsDTO.setType(smsType.toString ());??
        smsDTO.setSign(sign);
        smsDTO.setContent(content);
        smsService.send (platformCodeEnum,smsDTO);
        return DubboResponse.getOK ();
    }
}
