package com.cosfo.message.web.domain.dto;

import com.cosfo.message.infrastructure.template.dto.MessageSendParam;
import com.google.common.collect.Table;
import lombok.Data;
import net.xianmu.authentication.client.resp.AuthUserAuthResp;
import net.xianmu.authentication.client.resp.UserAuthBaseResp;
import org.apache.commons.lang3.tuple.Triple;

import java.util.Map;

/**
 * @Author: fansongsong
 * @Date: 2023-07-19
 * @Description:
 */
@Data
public class BuilderSendLogDTO {

    /**
     * 消息参数
     */
    MessageSendParam messageSendParam;
    /**
     * 发送者uid
     */
    String senderUid;
    /**
     * 接收者
     */
    String receiverUid;
    /**
     * 三方uid信息dto
     */
    MessageUidDTO messageUidDTO;
}
