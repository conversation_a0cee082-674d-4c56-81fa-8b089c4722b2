package com.cosfo.message.web.provider;

import com.cosfo.message.client.msgtemplate.provider.MsgTemplateCommandProvider;
import com.cosfo.message.client.msgtemplate.req.PrivateMsgTemplateWechatReq;
import com.cosfo.message.client.msgtemplate.resp.WechatCreateAllTemplateResp;
import com.cosfo.message.client.msgtemplate.resp.WechatCreateTemplateResp;
import com.cosfo.message.infrastructure.template.dto.PrivateMsgTemplateWechatDTO;
import com.cosfo.message.web.domain.service.TemplateDomainService;
import com.cosfo.message.web.domain.vo.WechatCreateAllTemplateVO;
import com.cosfo.message.web.provider.converter.TemplateProviderConverter;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date : 2023/2/13 11:07
 */
@DubboService
public class TemplateCommandProviderImpl implements MsgTemplateCommandProvider {
    @Resource
    private TemplateDomainService templateDomainService;


    /**
     * 根据微信模板创建帆台模板
     * @param privateMsgTemplateWechatReq
     * @return
     */
    @Override
    public DubboResponse<Long> createTemplate(PrivateMsgTemplateWechatReq privateMsgTemplateWechatReq) {
        PrivateMsgTemplateWechatDTO privateMsgTemplateWechatDTO = TemplateProviderConverter.privateMsgTemplateWechatReq2DTO(privateMsgTemplateWechatReq);
        Long templateId = templateDomainService.createTemplate(privateMsgTemplateWechatDTO);
        return DubboResponse.getOK(templateId);
    }

    /**
     * 实际调用微信接口创建个人模板
     * @param privateMsgTemplateWechatReq
     * @return
     */
    @Override
    public DubboResponse<WechatCreateTemplateResp> createWeiXinTemplate(PrivateMsgTemplateWechatReq privateMsgTemplateWechatReq) {
        PrivateMsgTemplateWechatDTO privateMsgTemplateWechatDTO = TemplateProviderConverter.privateMsgTemplateWechatReq2DTO(privateMsgTemplateWechatReq);
        WechatCreateTemplateResp wechatCreateTemplateResp = TemplateProviderConverter.wechatCreateTemplateVO2Resp(templateDomainService.createWeiXinTemplate(privateMsgTemplateWechatDTO));
        return DubboResponse.getOK(wechatCreateTemplateResp);
    }

    /**
     * 按帆台模板id创建所有模板
     * @param privateMsgTemplateWechatReq
     * @return
     */
    @Override
    public DubboResponse<WechatCreateAllTemplateResp> createAllTemplateList(PrivateMsgTemplateWechatReq privateMsgTemplateWechatReq) {
        PrivateMsgTemplateWechatDTO privateMsgTemplateWechatDTO = TemplateProviderConverter.privateMsgTemplateWechatReq2DTO(privateMsgTemplateWechatReq);
        WechatCreateAllTemplateVO templateVO = templateDomainService.createAllTemplate(privateMsgTemplateWechatDTO);
        WechatCreateAllTemplateResp allTemplateResp = TemplateProviderConverter.wechatCreateAllTemplateVO2Resp(templateVO);
        return DubboResponse.getOK(allTemplateResp);
    }

    /**
     * 重试创建模板
     * @param id
     * @param accessToken
     * @return
     */
    @Override
    public DubboResponse<WechatCreateTemplateResp> createTemplateRetry(Long id, String accessToken) {
        WechatCreateTemplateResp wechatCreateTemplateResp = TemplateProviderConverter.wechatCreateTemplateVO2Resp(templateDomainService.createTemplateRetry(id, accessToken));
        return DubboResponse.getOK(wechatCreateTemplateResp);
    }

    /**
     * 按个人模板id刷新个人模板状态
     * @param id 个人模板id
     * @param accessToken
     * @return
     */
    @Override
    public DubboResponse refreshApp(Long id, String accessToken) {
        templateDomainService.refreshApp(id, accessToken);
        return DubboResponse.getOK();
    }
}
