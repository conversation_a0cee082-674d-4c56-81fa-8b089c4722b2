package com.cosfo.message.web.facade;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreAccountQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserCenterMerchantStoreAccountFacade {

    @DubboReference
    private MerchantStoreAccountQueryProvider merchantStoreAccountQueryProvider;

    /**
     * 获取租户Id的门店账号信息
     *
     * @param tenantId
     * @return
     */
    public List<MerchantStoreAccountResultResp> getByTenantId(Long tenantId) {
        MerchantStoreAccountQueryReq merchantStoreAccountQueryReq = new MerchantStoreAccountQueryReq();
        merchantStoreAccountQueryReq.setTenantId(tenantId);
        return getMerchantStoreList(merchantStoreAccountQueryReq);
    }

    public List<MerchantStoreAccountResultResp> getByStoreId(Long storeId) {
        MerchantStoreAccountQueryReq merchantStoreAccountQueryReq = new MerchantStoreAccountQueryReq();
        merchantStoreAccountQueryReq.setStoreId(storeId);
        return getMerchantStoreList(merchantStoreAccountQueryReq);
    }

    /**
     * 获取门店账号信息
     *
     * @param merchantStoreAccountQueryReq
     * @return
     */
    public List<MerchantStoreAccountResultResp> getMerchantStoreList(MerchantStoreAccountQueryReq merchantStoreAccountQueryReq) {
        DubboResponse<List<MerchantStoreAccountResultResp>> response = merchantStoreAccountQueryProvider.getMerchantStoreAccounts(merchantStoreAccountQueryReq);
        if (!response.isSuccess()) {
            throw new BizException("获取门店账号信息失败");
        }
        return response.getData();
    }
}
