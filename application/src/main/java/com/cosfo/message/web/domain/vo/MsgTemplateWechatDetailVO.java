package com.cosfo.message.web.domain.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 微信消息模板表VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MsgTemplateWechatDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 微信模版池id
     */
    private Long templateWechatPublicId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 是否创建成功0=失败;1=成功
     */
    private Integer successFlag;

    /**
     * 是否可用0=不可用;1=可用
     */
    private Integer availableStatus;

    /**
     * 微信appid
     */
    private String wechatAppId;

    /**
     * 微信模版标题 id
     */
    private Long wechatTid;

    /**
     * 微信模版id
     */
    private String wechatTemplateId;

    /**
     * 关键字
     */
    private String keywords;

    /**
     * 微信小程序绑定时间(小程序授权时间)
     */
    private LocalDateTime bindTime;

    /**
     * 创建人id
     */
    private Long creator;


}
