package com.cosfo.message.web.domain.service;

import com.cosfo.message.infrastructure.template.dao.MsgTemplateWechatOfficialAccountPublicDao;
import com.cosfo.message.infrastructure.template.dto.TemplateWechatOfficialAccountQueryDTO;
import com.cosfo.message.infrastructure.template.model.MsgTemplateWechatOfficialAccountPublic;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-10-17
 * @Description:
 */
@Component
public class TemplateWechatOfficialAccountDomainService {

    @Resource
    private MsgTemplateWechatOfficialAccountPublicDao msgTemplateWechatOfficialAccountPublicDao;

    public List<MsgTemplateWechatOfficialAccountPublic> queryTemplateWechatOfficialAccountList(TemplateWechatOfficialAccountQueryDTO queryDTO) {
        return msgTemplateWechatOfficialAccountPublicDao.queryTemplateWechatOfficialAccountList(queryDTO);
    }
}
