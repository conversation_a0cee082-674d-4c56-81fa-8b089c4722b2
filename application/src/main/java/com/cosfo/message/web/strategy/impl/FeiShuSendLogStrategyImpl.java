package com.cosfo.message.web.strategy.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.message.client.enums.ChannelTypeEnum;
import com.cosfo.message.client.enums.MessageContentTypeEnum;
import com.cosfo.message.client.enums.MsgBodyTypeEnum;
import com.cosfo.message.common.constants.NumberConstants;
import com.cosfo.message.common.util.FeishuUtils;
import com.cosfo.message.infrastructure.message.dao.MsgSendLogDao;
import com.cosfo.message.infrastructure.message.model.MsgSendLog;
import com.cosfo.message.infrastructure.template.dto.BatchMessageSendDTO;
import com.cosfo.message.infrastructure.template.dto.FeishuSendMessageDTO;
import com.cosfo.message.infrastructure.template.dto.MessageBodyDTO;
import com.cosfo.message.infrastructure.template.dto.MessageSendParam;
import com.cosfo.message.infrastructure.template.dto.MessageUserDTO;
import com.cosfo.message.web.domain.dto.BuilderSendLogDTO;
import com.cosfo.message.web.domain.dto.FeishuSendLogReqDTO;
import com.cosfo.message.web.domain.dto.MessageUidDTO;
import com.cosfo.message.web.domain.vo.FeishuSendMessageVO;
import com.cosfo.message.web.facade.AuthUserAuthFacade;
import com.cosfo.message.web.strategy.SendLogStrategy;
import com.cosfo.message.web.strategy.SendLogStrategyHandler;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.Sets;
import com.google.common.collect.Table;
import com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.enums.FeiShuTokenTypeEnum;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.resp.UserAuthBaseResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.i18n.exception.I18nBizException;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * @Author: fansongsong
 * @Date: 2023-07-19
 * @Description:飞书发送消息策略
 */
@Slf4j
@Component
public class FeiShuSendLogStrategyImpl implements SendLogStrategy, InitializingBean {

    @Resource
    private MsgSendLogDao msgSendLogDao;

    @Resource
    private AuthUserAuthFacade authUserAuthFacade;

    /**
     * 飞书发送消息流控
     */
    private static final RateLimiter FEISHU_SEND_MESSAGE_LIMITER = RateLimiter.create(20);


    /**
     * 飞书支持的消息类型
     */
    public static final Set<MsgBodyTypeEnum> FEISHU_SUPPORT_MSG_TYPE_LIST = Sets.newHashSet(
            MsgBodyTypeEnum.TEXT, MsgBodyTypeEnum.POST, MsgBodyTypeEnum.IMAGE, MsgBodyTypeEnum.INTERACTIVE, MsgBodyTypeEnum.MEDIA, MsgBodyTypeEnum.FILE
    );

    /**
     * 发送用户id到限流缓存映射（同一用户发送消息的限频为 5QPS）
     */
    LoadingCache<String, RateLimiter> USER_LIMIT_CACHE = Caffeine.newBuilder()
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .maximumSize(50)
            .build(key -> RateLimiter.create(2));

    /**
     * 需忽略的接收用户id-后续考虑接入 NACOS 配置中心配置
     */
    private Set<String> ignoreUidSet = Sets.newHashSet("XM00271", "XM01026");

    @Override
    public MsgSendLog builderCustomMsgSendLog(BuilderSendLogDTO builderSendLogDTO, MsgSendLog msgSendLog) {
        MessageSendParam messageSendParam = builderSendLogDTO.getMessageSendParam();
        String receiverUid = builderSendLogDTO.getReceiverUid();

        Triple<Table<Integer, String, UserAuthBaseResp>, Table<Integer, String, UserAuthBaseResp>, Table<Integer, String, UserAuthBaseResp>> uidTriple =
                Optional.ofNullable(builderSendLogDTO.getMessageUidDTO()).map(MessageUidDTO::getUidTriple).orElse(null);

        MessageBodyDTO mesg = messageSendParam.getMesg();
        MessageUserDTO receiver = messageSendParam.getReceiver();

        msgSendLog.setReceiveThridUid(getThirdUid(receiverUid, receiver, uidTriple));

        // 把请求参数拼接好存储
        FeishuSendLogReqDTO feishuSendLogReqDTO = new FeishuSendLogReqDTO();
        if (Objects.nonNull(receiver)) {
            feishuSendLogReqDTO.setBaseUserId(receiver.getBaseUserId());
            feishuSendLogReqDTO.setBizUserId(receiver.getBizUserId());
            feishuSendLogReqDTO.setPhone(receiver.getPhone());
        }
        feishuSendLogReqDTO.setSystemOriginType(messageSendParam.getSystemOriginType());
        FeishuSendMessageDTO feishuSendMessageDTO = new FeishuSendMessageDTO();
        feishuSendMessageDTO.setReceiveId(msgSendLog.getReceiveThridUid());
        Integer msgBodyType = mesg.getMsgBodyType();
        MsgBodyTypeEnum msgBodyTypeEnum = MsgBodyTypeEnum.getByType(msgBodyType);
        feishuSendMessageDTO.setMsgType(msgBodyTypeEnum.getMsgType());
        feishuSendMessageDTO.setContent(mesg.getData());
        feishuSendLogReqDTO.setFeishuSendMessageDTO(feishuSendMessageDTO);
        msgSendLog.setReq(JSON.toJSONString(feishuSendLogReqDTO));
        return msgSendLog;
    }


    @Override
    public void messageSend(MsgSendLog msgSendLog) {
        boolean acquire = FEISHU_SEND_MESSAGE_LIMITER.tryAcquire(NumberConstants.TEN, TimeUnit.SECONDS);
        log.info("1、messageSend 开始发送飞书消息 msgSendLog:{}", JSON.toJSONString(msgSendLog));
        if (!acquire) {
            throw new BizException("消息流控限制(每秒50QPS)，十秒内无法未得到令牌,等待消费重试时进行处理,ID:" + msgSendLog.getId());
        }
        log.info("2、messageSend 开始发送飞书消息 msgSendLog.getReceiveThridUid()={}", msgSendLog.getReceiveThridUid());
        if (ignoreUidSet.contains(msgSendLog.getReceiveThridUid())) {
            log.info("SendThirdMessageListener 该条消息发送者是需忽略用户,不进行处理,msgSendLog:{}", JSON.toJSONString(msgSendLog));
            return;
        }
        acquire = USER_LIMIT_CACHE.get(msgSendLog.getReceiveThridUid()).tryAcquire(NumberConstants.THREE,TimeUnit.SECONDS);
        if (!acquire) {
            throw new I18nBizException ("消息流控限制(单用户每秒5QPS)，三秒内未得到令牌,等待消费重试时进行处理,ID:{0}",msgSendLog.getId());
        }
        log.info("3、messageSend 开始查询飞书token msgSendLog:{}", JSON.toJSONString(msgSendLog));
        FeishuSendLogReqDTO feishuSendLogReqDTO = JSON.parseObject(msgSendLog.getReq(), FeishuSendLogReqDTO.class);
        FeishuSendMessageDTO feishuSendMessageDTO = feishuSendLogReqDTO.getFeishuSendMessageDTO();
        // 查询token
        String authorizationToken = authUserAuthFacade.queryFeiShuToken(SystemOriginEnum.getSystemOriginByType(feishuSendLogReqDTO.getSystemOriginType()),
                msgSendLog.getTenantId(), FeiShuTokenTypeEnum.TENANT_ACCESS_TOKEN);

        log.info("4、messageSend 开始发送飞书消息 authorizationToken={}", authorizationToken);
        FeishuSendMessageVO feishuSendMessageVO = FeishuUtils.sendMessage(feishuSendMessageDTO, FeishuSendMessageVO.class, authorizationToken);

        Integer respCode = Optional.ofNullable(feishuSendMessageVO).map(FeishuSendMessageVO::getCode).orElseThrow(() -> new BizException("发送消息异常"));
        boolean success = FeishuUtils.SUCCESS_CODE.equals(respCode);
        msgSendLogDao.updateMsgLogResp(msgSendLog, feishuSendMessageVO, success);
        boolean limit = FeishuUtils.LIMIT_CODE.equals(respCode);
        // 机器人对用户是否具有可用性
        boolean userNoAvailability = FeishuUtils.BOT_NO_AVAILABILITY_CODE.equals(respCode);
        if (userNoAvailability) {
            log.info("机器人对用户没有可用性,消息ID:{}", msgSendLog.getId());
            return;
        }
        if (!success) {
            throw new I18nBizException("飞书消息发送异常,ID:{0},第三方响应码:{1},是否触发限频:{2}" , msgSendLog.getId(),respCode,limit);
        }
    }

    @Override
    public MessageUidDTO queryUidInfo(List<MessageUserDTO> queryList, BatchMessageSendDTO batchMessageSendDTO) {
        Long tenantId = batchMessageSendDTO.getTenantId();
        MessageUidDTO messageUidDTO = new MessageUidDTO();
        Triple<Table<Integer, String, UserAuthBaseResp>, Table<Integer, String, UserAuthBaseResp>, Table<Integer, String, UserAuthBaseResp>> uidTriple = authUserAuthFacade.queryThirdUidMap(queryList, tenantId);
        messageUidDTO.setUidTriple(uidTriple);
        return messageUidDTO;
    }

    @Override
    public void validParam(MessageBodyDTO mesg, Integer channleType, String channelCode) {
        MsgBodyTypeEnum msgBodyTypeEnum = MsgBodyTypeEnum.getByType(mesg.getMsgBodyType());

        if (Objects.isNull(msgBodyTypeEnum)) {
            throw new ParamsException("MessageBody消息参数异常");
        }
        if (!FEISHU_SUPPORT_MSG_TYPE_LIST.contains(msgBodyTypeEnum)) {
            throw new ParamsException("MessageBody消息类型异常");
        }
    }

    /**
     * 构建第三方uid
     * @param senderUid
     * @param messageUserDTO
     * @param uidTriple
     * @return
     */
    private String getThirdUid(String senderUid, MessageUserDTO messageUserDTO, Triple<Table<Integer, String, UserAuthBaseResp>, Table<Integer, String, UserAuthBaseResp>, Table<Integer, String, UserAuthBaseResp>> uidTriple) {
        if (StringUtils.isNotEmpty(senderUid)) {
            return senderUid;
        }
        if (Objects.isNull(messageUserDTO)) {
            throw new ParamsException("messageUser 不能为空");
        }
        Long baseUserId = messageUserDTO.getBaseUserId();
        Long bizUserId = messageUserDTO.getBizUserId();
        String phone = messageUserDTO.getPhone();
        Integer systemOriginType = messageUserDTO.getSystemOriginType();
        if (Objects.nonNull(baseUserId) && Objects.nonNull(uidTriple)) {
            Table<Integer, String, UserAuthBaseResp> baseUserIdMap = uidTriple.getLeft();
            return Optional.ofNullable(baseUserIdMap).map(table -> table.get(systemOriginType, baseUserId.toString())).map(UserAuthBaseResp::getThirdPartyId).orElseThrow(() -> new BizException("baseUserId=" + baseUserId + "第三方用户id为空"));
        }
        if (Objects.nonNull(bizUserId) && Objects.nonNull(uidTriple)) {
            Table<Integer, String, UserAuthBaseResp> bizUserIdMap = uidTriple.getMiddle();
            return Optional.ofNullable(bizUserIdMap).map(table -> table.get(systemOriginType, bizUserId.toString())).map(UserAuthBaseResp::getThirdPartyId).orElseThrow(() -> new BizException("bizUserId=" + bizUserId + "第三方用户id为空"));
        }

        if (StringUtils.isNotEmpty(phone) && Objects.nonNull(uidTriple)) {
            Table<Integer, String, UserAuthBaseResp> phoneMap = uidTriple.getRight();
            return Optional.ofNullable(phoneMap).map(table -> table.get(systemOriginType, phone)).map(UserAuthBaseResp::getThirdPartyId).orElseThrow(() -> new BizException("phone=" + phone + "第三方用户id为空"));
        }
        throw new BizException("sender第三方用户id不能为空");
    }

    @Override
    public void afterPropertiesSet() {
        SendLogStrategyHandler.registryStrategy(ChannelTypeEnum.FEISHU_SYSTEM, MessageContentTypeEnum.NORMAL, this);
    }
}
