package com.cosfo.message.web.domain.dto;

import com.cosfo.message.infrastructure.template.dto.WechatOaSendMessageDTO;
import lombok.Data;
import net.xianmu.authentication.client.input.SystemOriginEnum;

/**
 * @Author: fansongsong
 * @Date: 2023-10-17
 * @Description:
 */
@Data
public class WechatOaSendLogReqDTO {
    /**
     * 接收者，手机号码
     */
    private String phone;
    /**
     * systemOriginEnum {@link SystemOriginEnum}
     */
    private Integer systemOriginType;
    /**
     * 请求参数
     */
    private WechatOaSendMessageDTO wechatOaSendMessageDTO;
}
