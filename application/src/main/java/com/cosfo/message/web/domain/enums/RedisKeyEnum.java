package com.cosfo.message.web.domain.enums;

import lombok.Getter;

/**
 * @Author: fansongsong
 * @Date: 2023-03-30
 * @Description: 定义redis的key枚举
 */
@Getter
public enum RedisKeyEnum {
    M00001("创建sendLog幂等分布式锁"),
    M00002("租户强提醒分布式锁"),
    M00003("短信场景phone"),
    ;

    /**
     * 锁系统前缀
     */
    private static final String SPACE = "message-";

    /**
     * 连接符
     */
    public static final String SEPARATOR = "_";

    public String join(Object... args) {
        StringBuilder key = new StringBuilder(SPACE).append(SEPARATOR).append(super.toString());
        for (Object arg : args) {
            key.append(SEPARATOR).append(arg);
        }
        return key.toString();
    }

    RedisKeyEnum(String desc) {

    }

}
