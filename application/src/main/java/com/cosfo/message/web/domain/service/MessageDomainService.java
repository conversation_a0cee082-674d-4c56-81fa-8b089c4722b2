package com.cosfo.message.web.domain.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cosfo.message.client.enums.ChannelTypeEnum;
import com.cosfo.message.client.enums.KeyCodeEnum;
import com.cosfo.message.client.enums.MessageContentTypeEnum;
import com.cosfo.message.client.enums.MsgBodyTypeEnum;
import com.cosfo.message.client.enums.ReadStatusEnum;
import com.cosfo.message.common.constants.NumberConstants;
import com.cosfo.message.common.enums.MsgSendLogEnum;
import com.cosfo.message.common.util.FeishuUtils;
import com.cosfo.message.infrastructure.base.dto.PageQueryDTO;
import com.cosfo.message.infrastructure.base.dto.PageVO;
import com.cosfo.message.infrastructure.message.dao.MsgSendLogDao;
import com.cosfo.message.infrastructure.message.dto.MsgBatchUpdateSendLogDTO;
import com.cosfo.message.infrastructure.message.dto.MsgSendLogQueryDTO;
import com.cosfo.message.infrastructure.message.dto.NotifyMessageDTO;
import com.cosfo.message.infrastructure.message.dto.NotifyTipBodyDTO;
import com.cosfo.message.infrastructure.message.model.MsgSendLog;
import com.cosfo.message.infrastructure.template.dto.BatchMessageSendDTO;
import com.cosfo.message.infrastructure.template.dto.BatchMessageSendThirdUidDTO;
import com.cosfo.message.infrastructure.template.dto.MessageBodyDTO;
import com.cosfo.message.infrastructure.template.dto.MessageSendParam;
import com.cosfo.message.infrastructure.template.dto.MessageUserDTO;
import com.cosfo.message.web.domain.converter.MessageCoverter;
import com.cosfo.message.web.domain.converter.NoticeConverter;
import com.cosfo.message.web.domain.dto.BuilderSendLogDTO;
import com.cosfo.message.web.domain.dto.MessageUidDTO;
import com.cosfo.message.web.domain.enums.RedisKeyEnum;
import com.cosfo.message.web.domain.vo.MsgSendLogVO;
import com.cosfo.message.web.facade.AuthUserAuthFacade;
import com.cosfo.message.web.strategy.SendLogStrategy;
import com.cosfo.message.web.strategy.SendLogStrategyHandler;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.collect.Table;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.resp.UserAuthBaseResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.i18n.exception.I18nBizException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class MessageDomainService {
    @Resource
    private MsgSendLogDao msgSendLogDao;
    @Resource
    private AuthUserAuthFacade authUserAuthFacade;
    @Resource
    private RedissonClient redissonClient;

    public static final Set<Integer> SUPPORT_CHANNEL_TYPE = Sets.newHashSet(ChannelTypeEnum.FEISHU_SYSTEM.getValue(), ChannelTypeEnum.WECHAT_OFFICIAL_ACCOUNT.getValue());


    public Long getBeforeNoticeId(Long uId, Long noticeId, Long tenantId) {
        return msgSendLogDao.getBeforeNoticeId(uId, noticeId, tenantId);
    }

    public Long getAfterNoticeId(Long uId, Long noticeId, Long tenantId) {
        return msgSendLogDao.getAfterNoticeId(uId, noticeId, tenantId);
    }

    public PageVO<MsgSendLogVO> pageSendLog(MsgSendLogQueryDTO dto, PageQueryDTO pageQueryDTO) {
        IPage<MsgSendLog> logList = msgSendLogDao.pageSendLog(dto, pageQueryDTO);
        return PageVO.page2VO(logList, NoticeConverter::buildMsgSendLogVO);
    }

    public Integer countSendLog(MsgSendLogQueryDTO dto) {
        Integer count = msgSendLogDao.countSendLog(dto);
        return count;
    }

    public void readed(Long uId, Long noticeId, Long tenantId) {
        msgSendLogDao.updateReadStatusByUidAndNoticeId(uId, noticeId, tenantId, MsgSendLogEnum.ReadStatus.READED.getValue());
    }

    public Integer getUnReadNoticAmountByUserId(Long uId, Long tenantId) {
        //查询 用户所有收到的小程序内部通知
        MsgSendLogQueryDTO dto = new MsgSendLogQueryDTO();
        dto.setTenantId(tenantId);
        //系统类型的消息发送记录只发送一次。
        dto.setChannleType(MsgSendLogEnum.ChannleType.SELF_SYSTEM.getValue());
        dto.setContentType(MsgSendLogEnum.ContentType.NOTIC.getValue());
        dto.setSendStatus(MsgSendLogEnum.SendStatus.SUCCESS.getValue());
        dto.setReadStatus(MsgSendLogEnum.ReadStatus.UNREAD.getValue());
        dto.setReceiveAccountId(uId);

        PageQueryDTO pageQueryDTO = new PageQueryDTO();
        pageQueryDTO.setPageIndex(1);
        pageQueryDTO.setPageSize(1);
        return pageSendLog(dto, pageQueryDTO).getTotal();
    }

    public void deleteSendLogByPageId(Long noticeId, Integer contentType) {
        msgSendLogDao.deleteByPageId(noticeId, contentType);
    }

    public List<MsgSendLogVO> querySendLog(List<Long> logIds) {
        if (CollectionUtils.isEmpty(logIds)) {
            return Collections.EMPTY_LIST;
        }
        if (logIds.size() > NumberConstants.ONE_HUNDRED) {
            throw new I18nBizException("logIds大小超过最大值{0}" , NumberConstants.ONE_HUNDRED);
        }

        List<MsgSendLog> logList = msgSendLogDao.listByIds(logIds);
        return logList.stream().map(NoticeConverter::buildMsgSendLogVO).collect(Collectors.toList());
    }



    public List<MsgSendLogVO> batchCreateSendLog(BatchMessageSendDTO batchMessageSendDTO) {
        validBatchParam(batchMessageSendDTO);

        List<MessageUserDTO> receiverList = batchMessageSendDTO.getReceiverList();

        List<MessageUserDTO> queryList = Lists.newArrayList(receiverList);
        Integer messageContentType = Optional.ofNullable(batchMessageSendDTO.getMesg()).map(MessageBodyDTO::getContentType).orElse(MessageContentTypeEnum.NORMAL.getType());
        SendLogStrategy sendLogStrategy = SendLogStrategyHandler.getStrategyByChannelType(batchMessageSendDTO.getChannleType(), messageContentType);
        if (Objects.isNull(sendLogStrategy)) {
            throw new BizException("当前渠道类型不支持");
        }
        // 查询第三方用户信息
        MessageUidDTO messageUidDTO = sendLogStrategy.queryUidInfo(queryList, batchMessageSendDTO);

        List<MsgSendLog> msgSendLogList = Lists.newArrayList();
        for (MessageUserDTO receiver : receiverList) {
            BuilderSendLogDTO builderSendLogDTO = new BuilderSendLogDTO();
            MessageSendParam messageSendParam = MessageSendParam.builder().tenantId(batchMessageSendDTO.getTenantId()).channleType(batchMessageSendDTO.getChannleType())
                    .sender(batchMessageSendDTO.getSender()).receiver(receiver).mesg(batchMessageSendDTO.getMesg()).systemOriginType(receiver.getSystemOriginType())
                    .channelCode(batchMessageSendDTO.getChannelCode()).build();
            builderSendLogDTO.setMessageSendParam(messageSendParam);
            builderSendLogDTO.setMessageUidDTO(messageUidDTO);
            MsgSendLog msgSendLog = sendLogStrategy.builderMsgSendLog(builderSendLogDTO);
            msgSendLogList.add(msgSendLog);
        }
        boolean success = msgSendLogDao.saveBatch(msgSendLogList);
        if (!success) {
            throw new BizException("消息创建异常");
        }
        List<MsgSendLogVO> msgSendLogVOList = msgSendLogList.stream().map(NoticeConverter::buildMsgSendLogVO).collect(Collectors.toList());
        return msgSendLogVOList;
    }

    /**
     * 校验消息内容
     * @param mesg
     */
    private void validMessageBody(MessageBodyDTO mesg) {
        if (Objects.isNull(mesg.getData())) {
            throw new ParamsException("MessageBody消息参数异常");
        }
        if (StringUtils.isNotEmpty(mesg.getTitle()) && mesg.getTitle().length() > NumberConstants.SIXTY_FOUR) {
            throw new I18nBizException("MessageBody title长度不可大于{0}" ,NumberConstants.SIXTY_FOUR);
        }
    }

    /**
     * 校验渠道
     * @param channleType
     */
    public void validChannle(Integer channleType, MessageBodyDTO mesg, String channelCode) {
        if (!SUPPORT_CHANNEL_TYPE.contains(channleType)) {
            throw new ParamsException("channleType当前仅支持飞书、公众号");
        }

        SendLogStrategy sendLogStrategy = SendLogStrategyHandler.getStrategyByChannelType(channleType, mesg.getContentType());
        if (Objects.isNull(sendLogStrategy)) {
            throw new BizException("当前渠道类型不支持");
        }
        sendLogStrategy.validParam(mesg, channleType, channelCode);

        // 校验渠道消息
        validMessageBody(mesg);
    }

    /**
     * 校验消息用户
     * @param messageUserDTO
     */
    private void validMessageUserDTO(MessageUserDTO messageUserDTO) {
        if (Objects.isNull(messageUserDTO.getSystemOriginType())) {
            throw new ParamsException("messageUser缺少必填参数");
        }
        if (Objects.isNull(messageUserDTO.getBaseUserId()) && Objects.isNull(messageUserDTO.getBizUserId()) &&
                Objects.isNull(messageUserDTO.getPhone())) {
            throw new ParamsException("messageUser用户信息缺少必填参数");
        }
    }

    /**
     * 校验消息用户
     * @param receiverList
     */
    private void validMessageUserDTOList(List<MessageUserDTO> receiverList) {
        boolean validError = receiverList.stream().anyMatch(messageUserDTO -> Objects.isNull(messageUserDTO.getSystemOriginType()));
        if (validError) {
            log.info("validMessageUserDTOBatch校验失败,receiverList:{}", JSON.toJSONString(receiverList));
            throw new ParamsException("receiverList缺少必填参数");
        }

        validError = receiverList.stream().anyMatch(messageUserDTO -> Objects.isNull(messageUserDTO.getBaseUserId()) && Objects.isNull(messageUserDTO.getBizUserId()) &&
                Objects.isNull(messageUserDTO.getPhone()));
        if (validError) {
            log.info("validMessageUserDTOBatch校验失败,receiverList:{}", JSON.toJSONString(receiverList));
            throw new ParamsException("receiverList用户信息缺少必填参数");
        }
    }

    private void validBatchParam(BatchMessageSendDTO batchMessageSendDTO) {
        validParamNull(batchMessageSendDTO.getTenantId(),"tenantId");
        validParamNull(batchMessageSendDTO.getReceiverList(),"receiverList");
        validParamNull(batchMessageSendDTO.getMesg(),"MessageBodyReq");
        List<MessageUserDTO> receiverList = batchMessageSendDTO.getReceiverList();
        if (receiverList.size() > NumberConstants.FIFTY) {
            throw new I18nBizException("receiverList大小不能超过{0}",NumberConstants.FIFTY);
        }

        validChannle(batchMessageSendDTO.getChannleType(), batchMessageSendDTO.getMesg(),batchMessageSendDTO.getChannelCode());
        validMessageUserDTOList(receiverList);
    }

    public void validParamNull(Object object, String propertyName) {
        if (ObjectUtil.isNull(object)) {
            throw new I18nBizException("{0}不能为空",propertyName);
        }
    }

    private void validBatchParamByUid(BatchMessageSendThirdUidDTO batchMessageSendDTO) {
        validParamNull(batchMessageSendDTO.getTenantId(),"tenantId");
        validParamNull(batchMessageSendDTO.getReceiverUidList(),"receiverUid");
        validParamNull(batchMessageSendDTO.getMesg(),"MessageBodyReq");
        List<String> receiverUidList = batchMessageSendDTO.getReceiverUidList();
        if (receiverUidList.size() > NumberConstants.FIFTY) {
            throw new I18nBizException("receiverList大小不能超过{0}",NumberConstants.FIFTY);
        }

        validChannle(batchMessageSendDTO.getChannleType(), batchMessageSendDTO.getMesg(), batchMessageSendDTO.getChannelCode());
    }

    public List<MsgSendLogVO> batchCreateSendLogByUid(BatchMessageSendThirdUidDTO messageSendDTO) {
        validBatchParamByUid(messageSendDTO);
        Integer messageContentType = Optional.ofNullable(messageSendDTO.getMesg()).map(MessageBodyDTO::getContentType).orElse(MessageContentTypeEnum.NORMAL.getType());
        SendLogStrategy sendLogStrategy = SendLogStrategyHandler.getStrategyByChannelType(messageSendDTO.getChannleType(), messageContentType);
        if (Objects.isNull(sendLogStrategy)) {
            throw new BizException("当前渠道类型不支持");
        }
        List<MsgSendLog> msgSendLogList = Lists.newArrayList();
        MessageSendParam messageSendParam = MessageSendParam.builder().tenantId(messageSendDTO.getTenantId()).channleType(messageSendDTO.getChannleType())
                .mesg(messageSendDTO.getMesg()).systemOriginType(messageSendDTO.getSystemOriginType()).channelCode(messageSendDTO.getChannelCode()).build();
        for (String receiverUid : messageSendDTO.getReceiverUidList()) {
            BuilderSendLogDTO builderSendLogDTO = new BuilderSendLogDTO();
            builderSendLogDTO.setMessageSendParam(messageSendParam);
            builderSendLogDTO.setSenderUid(messageSendDTO.getSenderUid());
            builderSendLogDTO.setReceiverUid(receiverUid);
            MsgSendLog msgSendLog = sendLogStrategy.builderMsgSendLog(builderSendLogDTO);
            msgSendLogList.add(msgSendLog);
        }
        boolean success = msgSendLogDao.saveBatch(msgSendLogList);
        if (!success) {
            throw new BizException("消息创建异常");
        }
        List<MsgSendLogVO> msgSendLogVOList = msgSendLogList.stream().map(NoticeConverter::buildMsgSendLogVO).collect(Collectors.toList());
        return msgSendLogVOList;
    }

    public Boolean batchUpdateReadStatus(MsgBatchUpdateSendLogDTO msgBatchUpdateSendLogDTO) {
        return msgSendLogDao.batchUpdateReadStatus(msgBatchUpdateSendLogDTO);
    }

    public Boolean createNotifyMessage(NotifyMessageDTO notifyMessageDTO) {
        validUniqueId(notifyMessageDTO.getUniqueId());
//        validMessageData(notifyMessageDTO);
        if (StringUtils.isEmpty(notifyMessageDTO.getUniqueId())) {
            return save(notifyMessageDTO);
        }

        RLock lock = redissonClient.getLock(RedisKeyEnum.M00001.join(notifyMessageDTO.getTenantId(), notifyMessageDTO.getUniqueId()));
        // 未获取到锁，退出
        if (!lock.tryLock()) {
            throw new BizException("当前幂等ID正在创建中");
        }
        try {
            MsgSendLog msgSendLog = msgSendLogDao.queryMessageByUniqueId(notifyMessageDTO.getTenantId(), ChannelTypeEnum.BASIC_NOTIFY.getValue(), notifyMessageDTO.getMessageContentType(), notifyMessageDTO.getUniqueId());
            if (Objects.nonNull(msgSendLog)) {
                log.info("createNotifyMessage 命中幂等ID，不进行通知创建 notifyMessageDTO:{}", JSON.toJSONString(notifyMessageDTO));
                return Boolean.FALSE;
            }
            return save(notifyMessageDTO);
        } finally {
            lock.unlock();
        }
    }

    private void validUniqueId(String uniqueId) {
        if (StringUtils.isEmpty(uniqueId)) {
            return;
        }
        if (uniqueId.length() > NumberConstants.SIXTY_FOUR) {
            throw new I18nBizException ("uniqueId长度不可大于{0}",NumberConstants.SIXTY_FOUR);
        }
    }

    private Boolean save(NotifyMessageDTO notifyMessageDTO) {
        MsgSendLog msgSendLog = MessageCoverter.NotifyMessageDTO2Entity(notifyMessageDTO);
        return msgSendLogDao.save(msgSendLog);
    }

    private void validMessageData(NotifyMessageDTO notifyMessageDTO) {
        if (!MessageContentTypeEnum.AFTER_TENANT_NOTIFY.getType().equals(notifyMessageDTO.getMessageContentType())) {
            return;
        }
        List<NotifyTipBodyDTO> notifyTipBodyDTOList = notifyMessageDTO.getNotifyTipBodyDTOList();
        if (CollectionUtils.isEmpty(notifyTipBodyDTOList)) {
            return;
        }
        boolean codeError = notifyTipBodyDTOList.stream().anyMatch(notifyTipBodyDTO -> Objects.isNull(KeyCodeEnum.getByCode(notifyTipBodyDTO.getKeyCode())));
        if (codeError) {
            throw new ParamsException("detailList keyCode参数异常");
        }
    }

    public List<MsgSendLogVO> queryAlertNotifyMessage(Long tenantId) {
        RLock lock = redissonClient.getLock(RedisKeyEnum.M00002.join(tenantId));
        try {
            // 未获取到锁，退出
            if (!lock.tryLock(NumberConstants.TWO_HUNDRED, TimeUnit.MILLISECONDS)) {
                return Collections.EMPTY_LIST;
            }
            MsgSendLogQueryDTO msgSendLogQueryDTO = new MsgSendLogQueryDTO();
            msgSendLogQueryDTO.setTenantId(tenantId);
            msgSendLogQueryDTO.setChannleType(ChannelTypeEnum.BASIC_NOTIFY.getValue());
            msgSendLogQueryDTO.setReadStatus(ReadStatusEnum.UN_READ.getStatus());
            msgSendLogQueryDTO.setSendStatus(MsgSendLogEnum.SendStatus.PROCESSING.getValue());
            List<MsgSendLog> msgSendLogList = msgSendLogDao.listSendLog(msgSendLogQueryDTO);
            if (CollectionUtils.isEmpty(msgSendLogList)) {
                return Collections.EMPTY_LIST;
            }
            List<Long> idList = msgSendLogList.stream().map(MsgSendLog::getId).collect(Collectors.toList());
            // 更新发送状态
            msgSendLogDao.batchSuccessSendStatus(msgSendLogQueryDTO, idList);
            return msgSendLogList.stream().map(NoticeConverter::buildMsgSendLogVO).collect(Collectors.toList());
        } catch (Exception e) {
            log.info("queryAlertNotifyMessage tenantId:{},message:{}", tenantId, e.getMessage());
            return Collections.EMPTY_LIST;
        } finally {
            lock.unlock();
        }
    }

    public PageVO<MsgSendLogVO> pageSendLogOptimize(MsgSendLogQueryDTO dto, PageQueryDTO pageQueryDTO) {
        IPage<MsgSendLog> logList = msgSendLogDao.pageSendLogOrderByOptimize(dto, pageQueryDTO);
        return PageVO.page2VO(logList, NoticeConverter::buildMsgSendLogVO);
    }
}
