package com.cosfo.message.web.facade;

import com.cosfo.message.infrastructure.template.dto.BatchMessageSendDTO;
import com.cosfo.message.infrastructure.template.dto.MessageUserDTO;
import com.cosfo.message.web.domain.dto.MessageUidDTO;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.authentication.client.enums.FeiShuTokenTypeEnum;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.binlog.AuthThirdPartyInput;
import net.xianmu.authentication.client.input.user.AuthUserAuthQueryInput;
import net.xianmu.authentication.client.provider.AuthUserAuthProvider;
import net.xianmu.authentication.client.provider.FeiShuProvider;
import net.xianmu.authentication.client.provider.WechatProvider;
import net.xianmu.authentication.client.resp.AuthUserAuthResp;
import net.xianmu.authentication.client.resp.UserAuthBaseResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: fansongsong
 * @Date: 2023-07-17
 * @Description:
 */
@Slf4j
@Component
public class AuthUserAuthFacade {

    @DubboReference
    private AuthUserAuthProvider authUserAuthProvider;

    @DubboReference
    private FeiShuProvider feiShuProvider;

    @DubboReference
    private WechatProvider wechatProvider;

    /**
     * 批量查询三方id
     *
     * @return AuthThirdPartyInput
     */
    public List<UserAuthBaseResp> queryThirdPartyBatch(SystemOriginEnum systemOriginEnum, AuthThirdPartyInput authThirdPartyInput) {
        DubboResponse<List<UserAuthBaseResp>> response = authUserAuthProvider.queryThirdPartyBatch(systemOriginEnum, AuthTypeEnum.FEI_SHU, authThirdPartyInput);
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
        return Optional.ofNullable(response.getData()).orElse(Collections.EMPTY_LIST);
    }

    /**
     * 查询uid列表
     *
     * @param queryList
     * @param tenantId
     * @return
     */
    public Triple<Table<Integer, String, UserAuthBaseResp>, Table<Integer, String, UserAuthBaseResp>, Table<Integer, String, UserAuthBaseResp>> queryThirdUidMap(List<MessageUserDTO> queryList, Long tenantId) {
        if (CollectionUtils.isEmpty(queryList)) {
            return Triple.of(HashBasedTable.create(), HashBasedTable.create(), HashBasedTable.create());
        }

        List<MessageUserDTO> baseUserList = queryList.stream().filter(messageUserDTO -> Objects.nonNull(messageUserDTO.getBaseUserId())).collect(Collectors.toList());
        List<MessageUserDTO> bizUserList = queryList.stream().filter(messageUserDTO -> Objects.nonNull(messageUserDTO.getBizUserId())).collect(Collectors.toList());
        List<MessageUserDTO> phoneUserList = queryList.stream().filter(messageUserDTO -> Objects.nonNull(messageUserDTO.getPhone())).collect(Collectors.toList());

        AuthThirdPartyInput authThirdPartyInput = new AuthThirdPartyInput();
        authThirdPartyInput.setTenantId(tenantId);
        Table<Integer, String, UserAuthBaseResp> baseUserIdTable = HashBasedTable.create();
        Table<Integer, String, UserAuthBaseResp> bizUserIdTable = HashBasedTable.create();
        Table<Integer, String, UserAuthBaseResp> phoneTable = HashBasedTable.create();
        if (CollectionUtils.isNotEmpty(baseUserList)) {
            Map<Integer, List<MessageUserDTO>> baseUserMap = baseUserList.stream().collect(Collectors.groupingBy(MessageUserDTO::getSystemOriginType));
            for (Map.Entry<Integer, List<MessageUserDTO>> entry : baseUserMap.entrySet()) {
                List<String> baseUserIdList = entry.getValue().stream().map(dto -> dto.getBaseUserId().toString()).distinct().collect(Collectors.toList());
                authThirdPartyInput.setAccountType(AuthThirdPartyInput.AccountTypeEnum.BASE_USER_ID);
                authThirdPartyInput.setAccountList(baseUserIdList);
                List<UserAuthBaseResp> authUserAuthResps = queryThirdPartyBatch(SystemOriginEnum.getSystemOriginByType(entry.getKey()), authThirdPartyInput);

                authUserAuthResps.forEach(userAuthBaseResp -> baseUserIdTable.put(entry.getKey(), userAuthBaseResp.getQueryAccount(), userAuthBaseResp));
            }
        }
        if (CollectionUtils.isNotEmpty(bizUserList)) {
            Map<Integer, List<MessageUserDTO>> bizUserMap = bizUserList.stream().collect(Collectors.groupingBy(MessageUserDTO::getSystemOriginType));
            for (Map.Entry<Integer, List<MessageUserDTO>> bizUserEntry : bizUserMap.entrySet()) {
                List<String> bizUserIdList = bizUserEntry.getValue().stream().map(dto -> dto.getBizUserId().toString()).distinct().collect(Collectors.toList());
                authThirdPartyInput.setAccountType(AuthThirdPartyInput.AccountTypeEnum.BIZ_USER_ID);
                authThirdPartyInput.setAccountList(bizUserIdList);
                List<UserAuthBaseResp> authUserAuthResps = queryThirdPartyBatch(SystemOriginEnum.getSystemOriginByType(bizUserEntry.getKey()), authThirdPartyInput);

                authUserAuthResps.forEach(userAuthBaseResp -> bizUserIdTable.put(bizUserEntry.getKey(), userAuthBaseResp.getQueryAccount(), userAuthBaseResp));
            }
        }

        if (CollectionUtils.isNotEmpty(phoneUserList)) {
            Map<Integer, List<MessageUserDTO>> phoneMap = phoneUserList.stream().collect(Collectors.groupingBy(MessageUserDTO::getSystemOriginType));
            for (Map.Entry<Integer, List<MessageUserDTO>> phoneEntry : phoneMap.entrySet()) {
                List<String> phoneList = phoneEntry.getValue().stream().map(dto -> dto.getPhone()).distinct().collect(Collectors.toList());
                authThirdPartyInput.setAccountType(AuthThirdPartyInput.AccountTypeEnum.PHONE);
                authThirdPartyInput.setAccountList(phoneList);
                List<UserAuthBaseResp> authUserAuthResps = queryThirdPartyBatch(SystemOriginEnum.getSystemOriginByType(phoneEntry.getKey()), authThirdPartyInput);

                authUserAuthResps.forEach(userAuthBaseResp -> phoneTable.put(phoneEntry.getKey(), userAuthBaseResp.getQueryAccount(), userAuthBaseResp));
            }
        }
        return Triple.of(baseUserIdTable, bizUserIdTable, phoneTable);
    }

    /**
     * 查询飞书token
     *
     * @param systemOriginEnum
     * @param tenantId         租户id
     * @param tokenTypeEnum
     * @return 飞书token
     */
    public String queryFeiShuToken(SystemOriginEnum systemOriginEnum, Long tenantId, FeiShuTokenTypeEnum tokenTypeEnum) {
        DubboResponse<String> tokenResponse = feiShuProvider.queryFeiShuToken(systemOriginEnum, tenantId, tokenTypeEnum);
        if (!tokenResponse.isSuccess()) {
            throw new BizException(tokenResponse.getMsg());
        }
        return tokenResponse.getData();
    }

    /**
     * 查询用户openId信息
     * @param authUserAuthQueryInput
     * @return
     */
    public List<AuthUserAuthResp> queryUserRespByPhones(AuthUserAuthQueryInput authUserAuthQueryInput) {
        DubboResponse<List<AuthUserAuthResp>> response = wechatProvider.queryUserRespByPhones(authUserAuthQueryInput);
        if (!response.isSuccess()) {
            throw new BizException(response.getMsg());
        }
        return response.getData();
    }
    /**
     * 查询token
     * @param channel
     * @return
     */
    public String queryWeChatToken(String channel) {
        DubboResponse<String> response = wechatProvider.queryWeChatToken(channel);
        if (!response.isSuccess()) {
            throw new BizException(response.getMsg());
        }
        return response.getData();
    }

    public MessageUidDTO getWechatOaThirdUid(List<MessageUserDTO> queryList, BatchMessageSendDTO batchMessageSendDTO) {
        boolean anyMatch = queryList.stream().anyMatch(messageUserDTO -> Objects.isNull(messageUserDTO.getPhone()));
        if (anyMatch) {
            throw new ParamsException("phone不能为空");
        }

        Table<Integer, String, AuthUserAuthResp> phoneTable = HashBasedTable.create();

        if (CollectionUtils.isNotEmpty(queryList)) {
            Map<Integer, List<MessageUserDTO>> phoneMap = queryList.stream().collect(Collectors.groupingBy(MessageUserDTO::getSystemOriginType));
            for (Map.Entry<Integer, List<MessageUserDTO>> phoneEntry : phoneMap.entrySet()) {
                List<String> phoneList = phoneEntry.getValue().stream().map(dto -> dto.getPhone()).distinct().collect(Collectors.toList());

                AuthUserAuthQueryInput authUserAuthQueryInput = new AuthUserAuthQueryInput();
                authUserAuthQueryInput.setSystemOriginEnum(SystemOriginEnum.getSystemOriginByType(phoneEntry.getKey()));
                authUserAuthQueryInput.setPhones(phoneList);
                authUserAuthQueryInput.setPageNum(1);
                authUserAuthQueryInput.setAuthType(AuthTypeEnum.OFFICIAL_WE_CHAT);
                authUserAuthQueryInput.setTenantId(batchMessageSendDTO.getTenantId());
                authUserAuthQueryInput.setPageSize(phoneList.size());
                authUserAuthQueryInput.setChannelCode(batchMessageSendDTO.getChannelCode());
                List<AuthUserAuthResp> authUserAuthResps = queryUserRespByPhones(authUserAuthQueryInput);
                authUserAuthResps.forEach(userAuthBaseResp -> phoneTable.put(phoneEntry.getKey(), userAuthBaseResp.getPhone(), userAuthBaseResp));
            }
        }

        MessageUidDTO messageUidDTO = new MessageUidDTO();
        messageUidDTO.setPhoneTable(phoneTable);
        return messageUidDTO;
    }
}
