package com.cosfo.message.web.strategy;

import com.cosfo.message.client.enums.ChannelTypeEnum;
import com.cosfo.message.client.enums.MessageContentTypeEnum;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;

import java.util.Objects;

/**
 * @Author: fansongsong
 * @Date: 2023-07-19
 * @Description:
 */
public class SendLogStrategyHandler {

    private static final Table<Integer, Integer, SendLogStrategy> StrategyHandlerTable = HashBasedTable.create();

    public static void registryStrategy(ChannelTypeEnum channelTypeEnum, MessageContentTypeEnum messageContentTypeEnum, SendLogStrategy sendLogStrategy) {
        if (Objects.isNull(channelTypeEnum)) {
            return;
        }
        StrategyHandlerTable.put(channelTypeEnum.getValue(), messageContentTypeEnum.getType(), sendLogStrategy);
    }

    public static SendLogStrategy getStrategyByChannelType(Integer channelType, Integer messageContentType) {
        if (Objects.isNull(channelType)) {
            return null;
        }
        return StrategyHandlerTable.get(channelType, messageContentType);
    }

}
