package com.cosfo.message.web.strategy;

import com.cosfo.message.common.constants.NumberConstants;
import com.cosfo.message.common.enums.MsgSendLogEnum;
import com.cosfo.message.infrastructure.message.model.MsgSendLog;
import com.cosfo.message.infrastructure.template.dto.BatchMessageSendDTO;
import com.cosfo.message.infrastructure.template.dto.MessageBodyDTO;
import com.cosfo.message.infrastructure.template.dto.MessageSendParam;
import com.cosfo.message.infrastructure.template.dto.MessageUserDTO;
import com.cosfo.message.web.domain.dto.BuilderSendLogDTO;
import com.cosfo.message.web.domain.dto.MessageUidDTO;
import com.google.common.collect.Table;
import net.xianmu.authentication.client.resp.AuthUserAuthResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @Author: fansongsong
 * @Date: 2023-07-19
 * @Description:
 */
public interface SendLogStrategy {


    /**
     * 获取微信公众号三方uid
     * @param receiverUid
     * @param messageUserDTO
     * @param phoneTable
     * @return
     */
    default String getWechatOaThirdUid(String receiverUid, MessageUserDTO messageUserDTO, Table<Integer, String, AuthUserAuthResp> phoneTable) {
        if (StringUtils.isNotEmpty(receiverUid)) {
            return receiverUid;
        }
        if (Objects.isNull(messageUserDTO)) {
            throw new ParamsException("messageUser 不能为空");
        }
        String phone = messageUserDTO.getPhone();
        Integer systemOriginType = messageUserDTO.getSystemOriginType();

        if (StringUtils.isNotEmpty(phone) && Objects.nonNull(phoneTable)) {
            return Optional.ofNullable(phoneTable).map(table -> table.get(systemOriginType, phone)).map(AuthUserAuthResp::getAuthId).orElseThrow(() -> new BizException("phone=" + phone + "微信公众号获取第三方用户id为空"));
        }
        throw new BizException("sender第三方用户id不能为空");
    }


    /**
     * 构建消息保存数据
     * @param builderSendLogDTO
     * @return
     */
    default MsgSendLog builderMsgSendLog(BuilderSendLogDTO builderSendLogDTO) {
        MessageSendParam messageSendParam = builderSendLogDTO.getMessageSendParam();
        String senderUid = builderSendLogDTO.getSenderUid();
        MessageBodyDTO mesg = messageSendParam.getMesg();
        MsgSendLog msgSendLog = new MsgSendLog();
        msgSendLog.setTenantId(messageSendParam.getTenantId());

        msgSendLog.setChannleType(messageSendParam.getChannleType());
        msgSendLog.setContentType(mesg.getContentType());
        msgSendLog.setTitle(mesg.getTitle());
        // 组装第三方uid
        if (StringUtils.isNotEmpty(senderUid)) {
            msgSendLog.setSendThridUid(senderUid);
        } else {
            msgSendLog.setSendThridUid(NumberConstants.MINUS_ONE.toString());
        }
        // 发送者第三方uid
        msgSendLog.setSendAccountId(NumberConstants.MINUS_ONE);
        msgSendLog.setReceiveAccountId(NumberConstants.MINUS_ONE);
        msgSendLog.setStoreId(NumberConstants.MINUS_ONE);
        msgSendLog.setTemplateType(NumberConstants.MINUS_ONE.intValue());
        msgSendLog.setData(mesg.getData());
        msgSendLog.setSendStatus(MsgSendLogEnum.SendStatus.PROCESSING.getValue());
        // 不同渠道、消息类型的定制处理
        builderCustomMsgSendLog(builderSendLogDTO, msgSendLog);
        return msgSendLog;
    }

    /**
     * 构建策略定制的消息记录数据
     * @param builderSendLogDTO
     * @return
     */
    MsgSendLog builderCustomMsgSendLog(BuilderSendLogDTO builderSendLogDTO, MsgSendLog msgSendLog);

    /**
     * 发送第三方消息
     * @param msgSendLog
     * @return
     */
    void messageSend(MsgSendLog msgSendLog);

    /**
     * 查询第三方uid信息
     * @param queryList
     * @param batchMessageSendDTO
     * @return
     */
    MessageUidDTO queryUidInfo(List<MessageUserDTO> queryList, BatchMessageSendDTO batchMessageSendDTO);

    /**
     * 参数校验
     * @param mesg
     * @param channleType
     * @param channelCode
     */
    void validParam(MessageBodyDTO mesg, Integer channleType, String channelCode);
}
