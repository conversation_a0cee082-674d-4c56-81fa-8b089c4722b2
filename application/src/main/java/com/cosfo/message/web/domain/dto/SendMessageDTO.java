package com.cosfo.message.web.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: fansongsong
 * @Date: 2023-07-15
 * @Description:
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class SendMessageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 消息日志ID
     */
    Long msgSendLogId;
}
