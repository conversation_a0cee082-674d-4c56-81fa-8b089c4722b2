package com.cosfo.message.web.provider.converter;

import com.cosfo.message.client.common.page.req.PageQueryReq;
import com.cosfo.message.client.common.page.resp.PageResp;
import com.cosfo.message.infrastructure.base.dto.PageVO;
import com.cosfo.message.infrastructure.base.dto.PageQueryDTO;
import org.apache.commons.compress.utils.Lists;

import java.util.List;
import java.util.function.Function;

import static java.util.stream.Collectors.toList;

public class PageConverter {

    public static PageQueryDTO outReq2DTO(PageQueryReq page) {
        PageQueryDTO pageQueryDTO = new PageQueryDTO();
        pageQueryDTO.setPageSize(page.getPageSize());
        pageQueryDTO.setPageIndex(page.getPageIndex());
        return pageQueryDTO;
    }

    public static <T, R> PageResp<R> pageVO2PageResp(PageVO<T> page, Function<? super T, ? extends R> mapper) {
        PageResp resp = new PageResp();
        resp.setPageNum(new Long(page.getPageNum()).intValue());
        resp.setPageSize(new Long(page.getPageSize()).intValue());
        resp.setTotal(new Long(page.getTotal()).intValue());
        resp.setPages(page.getPages());
        resp.setData(Lists.newArrayList());
        resp.setIsLastPage(page.getIsLastPage());

        List<R> collect = page.getData().stream().map(mapper).collect(toList());
        resp.setData(collect);
        return resp;
    }
}
