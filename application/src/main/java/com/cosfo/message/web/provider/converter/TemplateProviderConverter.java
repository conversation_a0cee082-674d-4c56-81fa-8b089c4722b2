package com.cosfo.message.web.provider.converter;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.message.client.msgtemplate.req.*;
import com.cosfo.message.client.msgtemplate.resp.*;
import com.cosfo.message.common.enums.RuleTypeEnum;
import com.cosfo.message.infrastructure.template.dto.*;
import com.cosfo.message.infrastructure.template.model.*;
import com.cosfo.message.web.domain.vo.*;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date : 2023/2/13 11:58
 */
public class TemplateProviderConverter {

    public static MsgTemplateWechatPublicDTO msgTemplateWechatPublicReq2DTO(MsgTemplateWechatPublicReq msgTemplateWechatPublicReq){

        if (msgTemplateWechatPublicReq == null) {
            return null;
        }
        MsgTemplateWechatPublicDTO msgTemplateWechatPublicDTO = new MsgTemplateWechatPublicDTO();
        msgTemplateWechatPublicDTO.setPageIndex(msgTemplateWechatPublicReq.getPageIndex());
        msgTemplateWechatPublicDTO.setPageSize(msgTemplateWechatPublicReq.getPageSize());
        msgTemplateWechatPublicDTO.setWechatTitle(msgTemplateWechatPublicReq.getWechatTitle());
        msgTemplateWechatPublicDTO.setKeyWord(msgTemplateWechatPublicReq.getKeyWord());
        return msgTemplateWechatPublicDTO;
    }


    public static List<KeyWordResultResp> getKeywordList(String keywords){
        if (StringUtils.isEmpty(keywords)){
            return null;
        }
        List<KeyWordResultResp> keyWordResultRespList = JSONObject.parseArray(keywords, KeyWordResultResp.class);
        List<KeyWordResultResp> respKeyWordList = new LinkedList<>();
        if (!CollectionUtils.isEmpty(keyWordResultRespList)){
            respKeyWordList = keyWordResultRespList.stream().map(item -> {
                item.setRule(RuleTypeEnum.getContent(item.getRule()));
                return item;
            }).collect(Collectors.toList());
        }
        return respKeyWordList;
    }
    public static List<KeyWordResultResp> getKeywordListNoTrans(String keywords){
        if (StringUtils.isEmpty(keywords)){
            return null;
        }
        List<KeyWordResultResp> keyWordResultRespList = JSONObject.parseArray(keywords, KeyWordResultResp.class);
        return keyWordResultRespList;
    }

    public static WechatMsgTemplateDTO wechatMsgTemplateReq2DTO(WechatMsgTemplateReq wechatMsgTemplateReq){

        if (wechatMsgTemplateReq == null) {
            return null;
        }
        WechatMsgTemplateDTO wechatMsgTemplateDTO = new WechatMsgTemplateDTO();
        wechatMsgTemplateDTO.setId(wechatMsgTemplateReq.getId());
        wechatMsgTemplateDTO.setWechatTitle(wechatMsgTemplateReq.getWechatTitle());
        wechatMsgTemplateDTO.setStartTime(wechatMsgTemplateReq.getStartTime());
        wechatMsgTemplateDTO.setEndTime(wechatMsgTemplateReq.getEndTime());
        wechatMsgTemplateDTO.setPageIndex(wechatMsgTemplateReq.getPageIndex());
        wechatMsgTemplateDTO.setPageSize(wechatMsgTemplateReq.getPageSize());
        return wechatMsgTemplateDTO;
    }

    public static MsgPublicTemplateWechatListResultResp msgTemplateWechatPublicVO2Resp(MsgPublicTemplateWechatListVO msgPublicTemplateWechatListVO){

        if (msgPublicTemplateWechatListVO == null) {
            return null;
        }
        MsgPublicTemplateWechatListResultResp msgPublicTemplateWechatListResultResp = new MsgPublicTemplateWechatListResultResp();
        msgPublicTemplateWechatListResultResp.setId(msgPublicTemplateWechatListVO.getId());
        msgPublicTemplateWechatListResultResp.setWechatTitle(msgPublicTemplateWechatListVO.getWechatTitle());
        msgPublicTemplateWechatListResultResp.setPId(msgPublicTemplateWechatListVO.getPId());
        msgPublicTemplateWechatListResultResp.setWechatCategoryName(msgPublicTemplateWechatListVO.getWechatCategoryName());
        msgPublicTemplateWechatListResultResp.setKeywords(getKeywordList(msgPublicTemplateWechatListVO.getKeywords()));
        msgPublicTemplateWechatListResultResp.setScene(msgPublicTemplateWechatListVO.getScene());
        msgPublicTemplateWechatListResultResp.setCreateTime(msgPublicTemplateWechatListVO.getCreateTime());
        return msgPublicTemplateWechatListResultResp;
    }

    public static MsgPublicTemplateWechatListVO msgTemplateWechatPublic2VO(MsgTemplateWechatPublic msgTemplateWechatPublic){
        MsgPublicTemplateWechatListVO msgPublicTemplateWechatListVO = new MsgPublicTemplateWechatListVO();
        msgPublicTemplateWechatListVO.setWechatTitle(msgTemplateWechatPublic.getWechatTitle());
        msgPublicTemplateWechatListVO.setId(msgTemplateWechatPublic.getId());
        msgPublicTemplateWechatListVO.setPId(msgTemplateWechatPublic.getPId());
        msgPublicTemplateWechatListVO.setWechatCategoryName(msgTemplateWechatPublic.getWechatCategoryName());
        msgPublicTemplateWechatListVO.setKeywords(msgTemplateWechatPublic.getKeywords());
        msgPublicTemplateWechatListVO.setScene(msgTemplateWechatPublic.getScene());
        msgPublicTemplateWechatListVO.setCreateTime(msgTemplateWechatPublic.getCreateTime());
        return msgPublicTemplateWechatListVO;
    }
    public static MsgTemplateWechatListVO msgTemplateWechat2VO(MsgTemplateWechatPublic msgTemplateWechat, int countTemplate, int countDisableTemplate){

        if (msgTemplateWechat == null) {
            return null;
        }
        MsgTemplateWechatListVO msgTemplateWechatListVO = new MsgTemplateWechatListVO();
        msgTemplateWechatListVO.setId(msgTemplateWechat.getId());
        msgTemplateWechatListVO.setWechatTitle(msgTemplateWechat.getWechatTitle());
        msgTemplateWechatListVO.setKeywords(msgTemplateWechat.getKeywords());
        msgTemplateWechatListVO.setCreateTime(msgTemplateWechat.getCreateTime());
        msgTemplateWechatListVO.setPId(msgTemplateWechat.getPId());
        msgTemplateWechatListVO.setCreator(msgTemplateWechat.getCreator());
        msgTemplateWechatListVO.setLinkTemplateCount(countTemplate);
        msgTemplateWechatListVO.setFailTemplateCount(countDisableTemplate);
        return msgTemplateWechatListVO;
    }

    public static MsgTemplateWechatListResultResp msgTemplateWechatVO2Resp(MsgTemplateWechatListVO msgTemplateWechatListVO){

        if (msgTemplateWechatListVO == null) {
            return null;
        }
        MsgTemplateWechatListResultResp msgTemplateWechatListResultResp = new MsgTemplateWechatListResultResp();
        msgTemplateWechatListResultResp.setId(msgTemplateWechatListVO.getId());
        msgTemplateWechatListResultResp.setWechatTitle(msgTemplateWechatListVO.getWechatTitle());
        msgTemplateWechatListResultResp.setCreateTime(msgTemplateWechatListVO.getCreateTime());
        msgTemplateWechatListResultResp.setSceneCount(msgTemplateWechatListVO.getSceneCount());
        msgTemplateWechatListResultResp.setLinkTemplateCount(msgTemplateWechatListVO.getLinkTemplateCount());
        msgTemplateWechatListResultResp.setFailTemplateCount(msgTemplateWechatListVO.getFailTemplateCount());
        msgTemplateWechatListResultResp.setPId(msgTemplateWechatListVO.getPId());
        msgTemplateWechatListResultResp.setCreator(msgTemplateWechatListVO.getCreator());
        msgTemplateWechatListResultResp.setKeywords(getKeywordList(msgTemplateWechatListVO.getKeywords()));
        return msgTemplateWechatListResultResp;
    }



    public static MsgWechatAppDTO msgWechatAppReq2DTO(MsgWechatAppReq msgWechatAppReq){

        if (msgWechatAppReq == null) {
            return null;
        }
        MsgWechatAppDTO msgWechatAppDTO = new MsgWechatAppDTO();
        msgWechatAppDTO.setId(msgWechatAppReq.getId());
        msgWechatAppDTO.setMallId(msgWechatAppReq.getMallId());
        msgWechatAppDTO.setMallName(msgWechatAppReq.getMallName());
        msgWechatAppDTO.setType(msgWechatAppReq.getType());
        msgWechatAppDTO.setSuccessFlag(msgWechatAppReq.getSuccessFlag());
        msgWechatAppDTO.setAvailableStatus(msgWechatAppReq.getAvailableStatus());
        msgWechatAppDTO.setPageIndex(msgWechatAppReq.getPageIndex());
        msgWechatAppDTO.setPageSize(msgWechatAppReq.getPageSize());
        msgWechatAppDTO.setPlatformAppId(msgWechatAppReq.getPlatformAppId());
        return msgWechatAppDTO;
    }


    public static MsgTemplateWechatAppListVO applyLog2VO(MsgTemplateApplyLog msgTemplateApplyLog){

        if (msgTemplateApplyLog == null) {
            return null;
        }
        MsgTemplateWechatAppListVO msgTemplateWechatAppListVO = new MsgTemplateWechatAppListVO();
        msgTemplateWechatAppListVO.setId(msgTemplateApplyLog.getTemplateId());
        msgTemplateWechatAppListVO.setTenantId(msgTemplateApplyLog.getTenantId());
        msgTemplateWechatAppListVO.setWechatAppId(msgTemplateApplyLog.getAppId());
        msgTemplateWechatAppListVO.setUpdateTime(msgTemplateApplyLog.getCreateTime());
        msgTemplateWechatAppListVO.setReason(msgTemplateApplyLog.getReason());
        return msgTemplateWechatAppListVO;
    }


    public static MsgTemplateToAppVO msgTemplateWechat2AppVO(MsgTemplateWechat msgTemplateWechat, MsgTemplateWechatPublic wechatPublic){
        if (msgTemplateWechat == null) {
            return null;
        }
        if (wechatPublic == null) {
            return null;
        }
        MsgTemplateToAppVO msgTemplateToAppVO = new MsgTemplateToAppVO();
        msgTemplateToAppVO.setId(msgTemplateWechat.getId());
        msgTemplateToAppVO.setTenantId(msgTemplateWechat.getTenantId());
        msgTemplateToAppVO.setWechatAppId(msgTemplateWechat.getWechatAppId());
        msgTemplateToAppVO.setWechatTitle(wechatPublic.getWechatTitle());
        msgTemplateToAppVO.setKeywords(msgTemplateWechat.getKeywords());
        msgTemplateToAppVO.setWechatCategoryName(wechatPublic.getWechatCategoryName());
        msgTemplateToAppVO.setAvailableStatus(msgTemplateWechat.getAvailableStatus());
        msgTemplateToAppVO.setUpdateTime(msgTemplateWechat.getUpdateTime());
        return msgTemplateToAppVO;
    }

    public static MsgTemplateWechatAppListResultResp msgTemplateWechatAppListVO2Resp(MsgTemplateWechatAppListVO msgTemplateWechatAppListVO){
        if (msgTemplateWechatAppListVO == null){
            return null;
        }
        MsgTemplateWechatAppListResultResp msgTemplateWechatAppListResultResp = new MsgTemplateWechatAppListResultResp();
        msgTemplateWechatAppListResultResp.setId(msgTemplateWechatAppListVO.getId());
        msgTemplateWechatAppListResultResp.setTenantId(msgTemplateWechatAppListVO.getTenantId());
        msgTemplateWechatAppListResultResp.setAvailableStatus(msgTemplateWechatAppListVO.getAvailableStatus());
        msgTemplateWechatAppListResultResp.setWechatAppId(msgTemplateWechatAppListVO.getWechatAppId());
        msgTemplateWechatAppListResultResp.setWechatTitle(msgTemplateWechatAppListVO.getWechatTitle());
        msgTemplateWechatAppListResultResp.setKeywords(getKeywordList(msgTemplateWechatAppListVO.getKeywords()));
        msgTemplateWechatAppListResultResp.setBindTime(msgTemplateWechatAppListVO.getBindTime());
        msgTemplateWechatAppListResultResp.setCreator(msgTemplateWechatAppListVO.getCreator());
        msgTemplateWechatAppListResultResp.setMallName(msgTemplateWechatAppListVO.getMallName());
        msgTemplateWechatAppListResultResp.setPId(msgTemplateWechatAppListVO.getPId());
        msgTemplateWechatAppListResultResp.setReason(msgTemplateWechatAppListVO.getReason());
        msgTemplateWechatAppListResultResp.setUpdateTime(msgTemplateWechatAppListVO.getUpdateTime());
        msgTemplateWechatAppListResultResp.setWechatCategoryName(msgTemplateWechatAppListVO.getWechatCategoryName());
        return msgTemplateWechatAppListResultResp;
    }

    public static MsgTemplateToAppResultResp wechatAppVO2Resp(MsgTemplateToAppVO appVO){
        if (appVO == null){
            return null;
        }
        MsgTemplateToAppResultResp msgTemplateToAppResultResp = new MsgTemplateToAppResultResp();
        msgTemplateToAppResultResp.setId(appVO.getId());
        msgTemplateToAppResultResp.setTenantId(appVO.getTenantId());
        msgTemplateToAppResultResp.setAvailableStatus(appVO.getAvailableStatus());
        msgTemplateToAppResultResp.setWechatAppId(appVO.getWechatAppId());
        msgTemplateToAppResultResp.setWechatTitle(appVO.getWechatTitle());
        msgTemplateToAppResultResp.setKeywords(getKeywordList(appVO.getKeywords()));
        msgTemplateToAppResultResp.setMallName(appVO.getMallName());
        msgTemplateToAppResultResp.setUpdateTime(appVO.getUpdateTime());
        msgTemplateToAppResultResp.setWechatCategoryName(appVO.getWechatCategoryName());
        msgTemplateToAppResultResp.setKeywords(getKeywordList(appVO.getKeywords()));
        return msgTemplateToAppResultResp;
    }



    public static MsgPublicTemplateDetailVO publicTemplate2DetailVO(MsgTemplateWechatPublic wechatPublic){
        if (wechatPublic == null) {
            return null;
        }
        MsgPublicTemplateDetailVO msgPublicTemplateDetailVO = new MsgPublicTemplateDetailVO();
        msgPublicTemplateDetailVO.setId(wechatPublic.getId());
        msgPublicTemplateDetailVO.setWechatTitle(wechatPublic.getWechatTitle());
        msgPublicTemplateDetailVO.setCreateTime(wechatPublic.getCreateTime());
        msgPublicTemplateDetailVO.setScene(wechatPublic.getScene());
        msgPublicTemplateDetailVO.setKeywords(wechatPublic.getKeywords());
        return msgPublicTemplateDetailVO;
    }

    public static MsgTemplateWechatDetailResultResp publicDetailVO2Resp(MsgPublicTemplateDetailVO msgPublicTemplateDetailVO){

        if (msgPublicTemplateDetailVO == null) {
            return null;
        }
        MsgTemplateWechatDetailResultResp msgTemplateWechatDetailResultResp = new MsgTemplateWechatDetailResultResp();
        msgTemplateWechatDetailResultResp.setId(msgPublicTemplateDetailVO.getId());
        msgTemplateWechatDetailResultResp.setWechatTitle(msgPublicTemplateDetailVO.getWechatTitle());
        msgTemplateWechatDetailResultResp.setScene(msgPublicTemplateDetailVO.getScene());
        msgTemplateWechatDetailResultResp.setCreateTime(msgPublicTemplateDetailVO.getCreateTime());
        msgTemplateWechatDetailResultResp.setKeywords(getKeywordList(msgPublicTemplateDetailVO.getKeywords()));
        return msgTemplateWechatDetailResultResp;
    }

    public static PrivateMsgTemplateWechatDTO privateMsgTemplateWechatReq2DTO(PrivateMsgTemplateWechatReq privateMsgTemplateWechatReq){

        if (privateMsgTemplateWechatReq == null) {
            return null;
        }
        PrivateMsgTemplateWechatDTO privateMsgTemplateWechatDTO = new PrivateMsgTemplateWechatDTO();
        privateMsgTemplateWechatDTO.setId(privateMsgTemplateWechatReq.getId());
        privateMsgTemplateWechatDTO.setKidList(privateMsgTemplateWechatReq.getKidList());
        privateMsgTemplateWechatDTO.setTenantId(privateMsgTemplateWechatReq.getTenantId());
        privateMsgTemplateWechatDTO.setScene(privateMsgTemplateWechatReq.getScene());
        privateMsgTemplateWechatDTO.setUId(privateMsgTemplateWechatReq.getUId());
        if (!CollectionUtils.isEmpty(privateMsgTemplateWechatReq.getWechatAuthorReqList())){
            privateMsgTemplateWechatDTO.setAuthorDTOList(privateMsgTemplateWechatReq.getWechatAuthorReqList().stream().map(TemplateProviderConverter::wechatAuthorReq2DTO).collect(Collectors.toList()));
        }
        return privateMsgTemplateWechatDTO;
    }

    public static WechatCreateTemplateDTO packageWechatTemplate(PrivateMsgTemplateWechatDTO privateMsgTemplateWechatDTO){

        if (privateMsgTemplateWechatDTO == null) {
            return null;
        }
        WechatCreateTemplateDTO wechatCreateTemplateDTO = new WechatCreateTemplateDTO();
        wechatCreateTemplateDTO.setKidList(privateMsgTemplateWechatDTO.getKidList());
        wechatCreateTemplateDTO.setSceneDesc(privateMsgTemplateWechatDTO.getScene());
        wechatCreateTemplateDTO.setTid(privateMsgTemplateWechatDTO.getTid());
        return wechatCreateTemplateDTO;
    }

    public static MsgTemplateWechatDetailVO msgTemplateWechat2DetailVO(MsgTemplateWechat msgTemplateWechat){

        if (msgTemplateWechat == null) {
            return null;
        }
        MsgTemplateWechatDetailVO msgTemplateWechatDetailVO = new MsgTemplateWechatDetailVO();
        msgTemplateWechatDetailVO.setId(msgTemplateWechat.getId());
        msgTemplateWechatDetailVO.setCreateTime(msgTemplateWechat.getCreateTime());
        msgTemplateWechatDetailVO.setUpdateTime(msgTemplateWechat.getUpdateTime());
        msgTemplateWechatDetailVO.setTemplateWechatPublicId(msgTemplateWechat.getTemplateWechatPublicId());
        msgTemplateWechatDetailVO.setTenantId(msgTemplateWechat.getTenantId());
        msgTemplateWechatDetailVO.setSuccessFlag(msgTemplateWechat.getSuccessFlag());
        msgTemplateWechatDetailVO.setAvailableStatus(msgTemplateWechat.getAvailableStatus());
        msgTemplateWechatDetailVO.setWechatAppId(msgTemplateWechat.getWechatAppId());
        msgTemplateWechatDetailVO.setWechatTid(msgTemplateWechat.getWechatTid());
        msgTemplateWechatDetailVO.setWechatTemplateId(msgTemplateWechat.getWechatTemplateId());
        msgTemplateWechatDetailVO.setKeywords(msgTemplateWechat.getKeywords());
        msgTemplateWechatDetailVO.setBindTime(msgTemplateWechat.getBindTime());
        msgTemplateWechatDetailVO.setCreator(msgTemplateWechat.getCreator());
        return msgTemplateWechatDetailVO;
    }

    public static MsgTemplateToAppResultResp msgTemplateWechatDetailVO2Resp(MsgTemplateWechatDetailVO msgTemplateWechatDetailVO){
        MsgTemplateToAppResultResp msgTemplateToAppResultResp = new MsgTemplateToAppResultResp();
        msgTemplateToAppResultResp.setTenantId(msgTemplateWechatDetailVO.getTenantId());
        msgTemplateToAppResultResp.setKeywords(getKeywordList(msgTemplateWechatDetailVO.getKeywords()));
        msgTemplateToAppResultResp.setAvailableStatus(msgTemplateWechatDetailVO.getAvailableStatus());
        msgTemplateToAppResultResp.setWechatAppId(msgTemplateWechatDetailVO.getWechatAppId());
        msgTemplateToAppResultResp.setId(msgTemplateWechatDetailVO.getId());
        msgTemplateToAppResultResp.setUpdateTime(msgTemplateWechatDetailVO.getUpdateTime());
        return msgTemplateToAppResultResp;
    }

    public static WechatAuthorDTO wechatAuthorReq2DTO(WechatAuthorReq wechatAuthorReq){

        if (wechatAuthorReq == null) {
            return null;
        }
        WechatAuthorDTO wechatAuthorDTO = new WechatAuthorDTO();
        wechatAuthorDTO.setAccessToken(wechatAuthorReq.getAccessToken());
        wechatAuthorDTO.setAppId(wechatAuthorReq.getAppId());
        wechatAuthorDTO.setCreateTime(wechatAuthorReq.getCreateTime());
        wechatAuthorDTO.setTenantId(wechatAuthorReq.getTenantId());
        return wechatAuthorDTO;
    }

    public static PageInfo<?> turnToPageInfo(PageInfo<? extends Object> pageTarget, PageInfo pageSource){
        pageTarget.setPageNum(pageSource.getPageNum());
        pageTarget.setPageSize(pageSource.getPageSize());
        pageTarget.setPages(pageSource.getPages());
        pageTarget.setTotal(pageSource.getTotal());
        return pageTarget;
    }

    public static WechatMsgTemplateQueryDTO wechatMsgTemplateQueryReq2DTO(WechatMsgTemplateQueryReq wechatMsgTemplateQueryReq){

        if (wechatMsgTemplateQueryReq == null) {
            return null;
        }
        WechatMsgTemplateQueryDTO wechatMsgTemplateQueryDTO = new WechatMsgTemplateQueryDTO();
        wechatMsgTemplateQueryDTO.setId(wechatMsgTemplateQueryReq.getId());
        wechatMsgTemplateQueryDTO.setIdList(wechatMsgTemplateQueryReq.getIdList());
        wechatMsgTemplateQueryDTO.setTemplateIdList(wechatMsgTemplateQueryReq.getTemplateIdList());
        wechatMsgTemplateQueryDTO.setWechatTitle(wechatMsgTemplateQueryReq.getWechatTitle());
        wechatMsgTemplateQueryDTO.setStartTime(wechatMsgTemplateQueryReq.getStartTime());
        wechatMsgTemplateQueryDTO.setEndTime(wechatMsgTemplateQueryReq.getEndTime());
        wechatMsgTemplateQueryDTO.setSuccessFlag(wechatMsgTemplateQueryReq.getSuccessFlag());
        return wechatMsgTemplateQueryDTO;
    }

    public static MsgPublicTemplateWechatListResultResp msgPublicTemplateResultVO2Resp(MsgTemplateWechatDetailVO msgTemplateWechatDetailVO){

        if (msgTemplateWechatDetailVO == null) {
            return null;
        }
        MsgPublicTemplateWechatListResultResp templateWechatListResultResp = new MsgPublicTemplateWechatListResultResp();
        templateWechatListResultResp.setCreateTime(msgTemplateWechatDetailVO.getCreateTime());
        templateWechatListResultResp.setKeywords(getKeywordList(msgTemplateWechatDetailVO.getKeywords()));
        templateWechatListResultResp.setPId(msgTemplateWechatDetailVO.getTemplateWechatPublicId());
        templateWechatListResultResp.setId(msgTemplateWechatDetailVO.getId());
        return templateWechatListResultResp;
    }

    public static WechatCreateTemplateResp wechatCreateTemplateVO2Resp(WechatCreateTemplateVO wechatCreateTemplateVO){

        if (wechatCreateTemplateVO == null) {
            return null;
        }
        WechatCreateTemplateResp wechatCreateTemplateResp = new WechatCreateTemplateResp();
        wechatCreateTemplateResp.setErrMsg(wechatCreateTemplateVO.getErrMsg());
        wechatCreateTemplateResp.setErrCode(wechatCreateTemplateVO.getErrCode());
        wechatCreateTemplateResp.setPriTmplId(wechatCreateTemplateVO.getPriTmplId());
        wechatCreateTemplateResp.setTenantId(wechatCreateTemplateVO.getTenantId());
        wechatCreateTemplateResp.setId(wechatCreateTemplateVO.getId());
        return wechatCreateTemplateResp;
    }


    public static WechatCreateAllTemplateResp wechatCreateAllTemplateVO2Resp(WechatCreateAllTemplateVO templateVO){

        if (templateVO == null) {
            return null;
        }
        WechatCreateAllTemplateResp wechatCreateAllTemplateResp = new WechatCreateAllTemplateResp();
        wechatCreateAllTemplateResp.setSuccessCount(templateVO.getSuccessCount());
        wechatCreateAllTemplateResp.setFailCount(templateVO.getFailCount());
        return wechatCreateAllTemplateResp;
    }
}
