package com.cosfo.message.web.provider;

import com.cosfo.message.client.common.page.req.PageQueryReq;
import com.cosfo.message.client.common.page.resp.PageResp;
import com.cosfo.message.client.enums.ChannelTypeEnum;
import com.cosfo.message.client.enums.ReadStatusEnum;
import com.cosfo.message.client.provider.MessageSendLogProvider;
import com.cosfo.message.client.req.BatchMarkMsgReq;
import com.cosfo.message.client.req.MsgSendLogQueryReq;
import com.cosfo.message.client.req.NotifyMessageReq;
import com.cosfo.message.client.req.TenantNotifyReq;
import com.cosfo.message.client.resp.MsgNotifySendLogResp;
import com.cosfo.message.client.resp.MsgSendLogResp;
import com.cosfo.message.infrastructure.base.dto.PageQueryDTO;
import com.cosfo.message.infrastructure.base.dto.PageVO;
import com.cosfo.message.infrastructure.message.dto.MsgBatchUpdateSendLogDTO;
import com.cosfo.message.infrastructure.message.dto.MsgSendLogQueryDTO;
import com.cosfo.message.infrastructure.message.dto.NotifyMessageDTO;
import com.cosfo.message.web.domain.service.MessageDomainService;
import com.cosfo.message.web.domain.vo.MsgSendLogVO;
import com.cosfo.message.web.provider.converter.MessageSendLogProviderConverter;
import com.cosfo.message.web.provider.converter.PageConverter;
import com.cosfo.message.web.provider.converter.SendLogProviderConvert;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: fansongsong
 * @Date: 2023-07-14
 * @Description:
 */
@DubboService
@Slf4j
public class MessageSendLogProviderImpl implements MessageSendLogProvider {

    @Resource
    private MessageDomainService messageDomainService;

    @Override
    public DubboResponse<List<MsgSendLogResp>> querySendLog(List<Long> logIds) {
        List<MsgSendLogVO> msgSendLogVOList = messageDomainService.querySendLog(logIds);
        List<MsgSendLogResp> respList = msgSendLogVOList.stream().map(MessageSendLogProviderConverter::msgSendLogVOListVO2Resp).collect(Collectors.toList());
        return DubboResponse.getOK(respList);
    }

    /**
     * 分页查询租户消息列表
     *
     * @param msgSendLogQueryReq
     * @param pageQueryReq
     * @return
     */
    @Override
    public DubboResponse<PageResp<MsgNotifySendLogResp>> page4Manage(MsgSendLogQueryReq msgSendLogQueryReq, PageQueryReq pageQueryReq) {
        MsgSendLogQueryDTO msgSendLogQueryDTO = SendLogProviderConvert.msgSendLogQueryReq2DTO(msgSendLogQueryReq);
        Integer channelType = Optional.ofNullable(msgSendLogQueryReq.getChannelTypeEnum()).map(ChannelTypeEnum::getValue).orElse(ChannelTypeEnum.BASIC_NOTIFY.getValue());
        msgSendLogQueryDTO.setChannleType(channelType);
        PageQueryDTO pageQueryDTO = new PageQueryDTO();
        pageQueryDTO.setPageIndex(pageQueryReq.getPageIndex());
        pageQueryDTO.setPageSize(pageQueryReq.getPageSize());

        PageVO<MsgSendLogVO> pageVO;
        if (ReadStatusEnum.UN_READ.getStatus().equals(msgSendLogQueryDTO.getReadStatus())) {
            pageVO = messageDomainService.pageSendLog(msgSendLogQueryDTO, pageQueryDTO);
        } else {
            pageVO = messageDomainService.pageSendLogOptimize(msgSendLogQueryDTO, pageQueryDTO);
        }
        return DubboResponse.getOK(PageConverter.pageVO2PageResp(pageVO, SendLogProviderConvert::msgSendLogVO2Resp));
    }

    /**
     * 查询符合条件的消息数量
     *
     * @param msgSendLogQueryReq
     * @return
     */
    @Override
    public DubboResponse<Integer> countByCondition(MsgSendLogQueryReq msgSendLogQueryReq) {
        MsgSendLogQueryDTO msgSendLogQueryDTO = SendLogProviderConvert.msgSendLogQueryReq2DTO(msgSendLogQueryReq);
        Integer channelType = Optional.ofNullable(msgSendLogQueryReq.getChannelTypeEnum()).map(ChannelTypeEnum::getValue).orElse(ChannelTypeEnum.BASIC_NOTIFY.getValue());
        msgSendLogQueryDTO.setChannleType(channelType);
        Integer total = messageDomainService.countSendLog(msgSendLogQueryDTO);
        return DubboResponse.getOK(total);
    }

    /**
     * 批量标记读取状态
     *
     * @param batchMarkMsgReq
     * @return
     */
    @Override
    public DubboResponse<Boolean> markBatch(BatchMarkMsgReq batchMarkMsgReq) {
        MsgBatchUpdateSendLogDTO msgBatchUpdateSendLogDTO = SendLogProviderConvert.batchMarkMsgReq2DTO(batchMarkMsgReq);
        Integer channelType = Optional.ofNullable(batchMarkMsgReq.getChannelTypeEnum()).map(ChannelTypeEnum::getValue).orElse(ChannelTypeEnum.BASIC_NOTIFY.getValue());
        msgBatchUpdateSendLogDTO.setChannleType(channelType);
        return DubboResponse.getOK(messageDomainService.batchUpdateReadStatus(msgBatchUpdateSendLogDTO));
    }

    @Override
    public DubboResponse<Boolean> createNotifyMessage(NotifyMessageReq notifyMessageReq) {
        NotifyMessageDTO notifyMessageDTO = SendLogProviderConvert.notifyMessageReq2DTO(notifyMessageReq);
        return DubboResponse.getOK(messageDomainService.createNotifyMessage(notifyMessageDTO));
    }

    @Override
    public DubboResponse<List<MsgNotifySendLogResp>> queryAlertNotifyMessage(@Valid TenantNotifyReq tenantNotifyReq) {
        List<MsgSendLogVO> list = messageDomainService.queryAlertNotifyMessage(tenantNotifyReq.getTenantId());
        return DubboResponse.getOK(list.stream().map(SendLogProviderConvert::msgSendLogVO2Resp).collect(Collectors.toList()));
    }
}
