package com.cosfo.message.web.mq.consumer;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.message.common.constants.MqConstants;
import com.cosfo.message.common.constants.NumberConstants;
import com.cosfo.message.common.enums.*;
import com.cosfo.message.common.util.FeishuUtils;
import com.cosfo.message.infrastructure.message.dao.MsgSendLogDao;
import com.cosfo.message.infrastructure.message.dto.MsgSendLogDTO;
import com.cosfo.message.infrastructure.message.model.MsgSendLog;
import com.cosfo.message.infrastructure.notice.dao.MsgNoticeDao;
import com.cosfo.message.infrastructure.notice.dao.MsgNoticeReceiverMappingDao;
import com.cosfo.message.infrastructure.notice.model.MsgNotice;
import com.cosfo.message.infrastructure.notice.model.MsgNoticeReceiverMapping;
import com.cosfo.message.infrastructure.template.dao.MsgTemplateWechatDao;
import com.cosfo.message.infrastructure.template.dto.FeishuSendMessageDTO;
import com.cosfo.message.web.domain.dto.FeishuSendLogReqDTO;
import com.cosfo.message.web.domain.dto.SendMessageDTO;
import com.cosfo.message.web.domain.service.NoticeContentUtil;
import com.cosfo.message.web.domain.service.SendHandler;
import com.cosfo.message.web.domain.service.SendHandlerFactory;
import com.cosfo.message.web.domain.service.UserCenterService;
import com.cosfo.message.web.domain.vo.*;
import com.cosfo.message.web.facade.AuthUserAuthFacade;
import com.cosfo.message.web.facade.OmsFacade;
import com.cosfo.oms.client.common.MsgSceneTenantMappingEnum;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.enums.FeiShuTokenTypeEnum;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 描述: 根据sendLog发送第三方消息
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@MqListener(topic = MqConstants.Topic.SEND_NOTICE,
        consumerGroup = MqConstants.ConsumeGroup.CONSUME_SEND_NOTICE
)
public class SendNoticeListener extends AbstractMqListener<MsgNotice> {
    @Autowired
    private MsgNoticeDao noticeDao;
    @Value("${notice.sceneId}")
    private Long sceneId;
    @Autowired
    private MsgNoticeReceiverMappingDao receiverMappingDao;
    @Autowired
    private OmsFacade omsFacade;
    @Autowired
    private UserCenterService userCenterService;
    @Autowired
    private SendHandlerFactory sendHandlerFactory;
    @Autowired
    private MqProducer mqProducer;

    @Override
    public void process(MsgNotice req) {
        if (Objects.isNull(req) || Objects.isNull(req.getId ())) {
            log.info("SendNoticeListener 数据异常 入参:{}", JSON.toJSONString(req));
            return;
        }
        MsgNotice notice = noticeDao.getById (req.getId ());
        if (Objects.isNull(notice)) {
            log.info("SendNoticeListener 未查询到数据 入参:{}", JSON.toJSONString(req));
            return;
        }

        if (ObjectUtil.isNotNull(notice.getPushTime ()) && MsgNoticeEnum.PushStatus.READY.getValue().equals(notice.getPushStatus())) {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime day39 = now.plusDays(39);
            if (notice.getPushTime ().isAfter(now)) {
                log.info("SendNoticeListener 收到消息是在发送之前  重新投递消息内容：{}", JSONObject.toJSONString(notice));
                mqProducer.sendStartDeliver(MqConstants.Topic.SEND_NOTICE,null , JSON.toJSON(notice), notice.getPushTime ().isAfter(day39) ? day39 : notice.getPushTime ().plusSeconds(1));
            }else{
                MsgSceneVO scene = omsFacade.getMsgSceneById(sceneId);
                send(notice,Objects.equals (MsgSceneEnum.SceneStatus.ABLED.getValue (), scene.getSceneStatus ()));
            }
        }
    }

    public void send(MsgNotice notice,Boolean wechatFlag){
        Long tenantId = notice.getTenantId();
        //查询 模版状态
        String thirdTemplateId = null;
        try {
            if(wechatFlag) {
                List<MsgSceneTenantVO> msgSceneTenantVOS = omsFacade.listAvailableSceneTenantByTenantId (sceneId, tenantId, MsgSceneTenantMappingEnum.TemplateType.WECHAT);
                if (CollectionUtil.isEmpty (msgSceneTenantVOS)) {
                    log.error ("查询租户 场景 微信为空，noticeId={}，tenaneId={}", notice.getId (), tenantId);
                } else {
                    MsgSceneTenantVO msgSceneTenantVO = msgSceneTenantVOS.get (0);
                    Integer availableStatus = msgSceneTenantVO.getAvailableStatus ();
                    if (Objects.equals (MsgSceneEnum.AvailableStatus.ABLED.getValue (), availableStatus)) {
                        thirdTemplateId = msgSceneTenantVO.getThirdTemplateId ();
                    } else {
                        log.error ("查询租户 场景 微信 类型状态不可用thirdTemplateId为空，noticeId={}，tenaneId={}", notice.getId (), tenantId);
                    }
                }
            }
        }catch (Exception e){
            log.error("查询微信模版异常，noticeId={}，tenaneId={}",notice.getId(),tenantId,e);
        }

        //查询 接受者
        List<MsgNoticeReceiverMapping> mappingList;
        try {
            mappingList = receiverMappingDao.listByNoticeIds(Collections.singletonList(notice.getId()));
        }catch (Exception e){
            log.error("查询公告接受者异常，noticeId={}",notice.getId(),e);
            return;
        }

        //更新公告发送状态
        try {
            noticeDao.updatePushStatus(notice.getId(),MsgNoticeEnum.PushStatus.PUSHED.getValue());
        }catch (Exception e){
            log.error("更新公告推送状态异常，noticeId={}",notice.getId(),e);
            return;
        }

        //挨个进行发送
        if(!CollectionUtil.isEmpty(mappingList)) {
            String finalThirdTemplateId = thirdTemplateId;
            mappingList.stream().filter(receiver->receiver.getReceiverId()!=0).forEach(receiver->{
                switch (MsgNoticeReceiverMappingEnum.Type.of(receiver.getType())){
                    case STORE:
                        //验证店铺状态
                        try {
                            List<MerchantStoreVO> stores = userCenterService.getStoreByIds(Collections.singletonList(receiver.getReceiverId()));
                            if(CollectionUtil.isEmpty(stores)){
                                break;
                            }else{
                                Integer status = stores.stream().findFirst().get().getStatus();
                                if(!Objects.equals(status, MerchantStoreEnum.Status.AUDIT_SUCCESS.getCode())) {
                                    break;
                                }
                            }
                        }catch (Exception e){
                            log.error("查询接受异常，noticeId={}，门店id={}",notice.getId(),receiver.getReceiverId(),e);
                            break;
                        }
                        //验证店员账号
                        List<MerchantStoreAccountVO> accounts;
                        try {
                            accounts = userCenterService.getStoreAccountIdsByStoreId(receiver.getReceiverId(), null, 1);
                        }catch (Exception e){
                            log.error("查询公告接受门店用户异常，noticeId={}，门店id={}",notice.getId(),receiver.getReceiverId(),e);
                            break;
                        }
                        if(ObjectUtil.isNotNull(finalThirdTemplateId)) {
                            accounts.forEach(account -> sendWeChat(account, notice, finalThirdTemplateId));
                        }
                        accounts.forEach(account->sendSelfSystem(account,notice));
                        break;
                    default:
                        break;
                }
            });
        }
    }

    private void sendWeChat(MerchantStoreAccountVO account, MsgNotice notice,String thirdTemplateId) {
        if(account == null || Objects.isNull(thirdTemplateId)){
            return;
        }
        try {
            Map<Long, String> tokenMap = omsFacade.getTokenByTenantIds(Collections.singletonList(notice.getTenantId()));
            if(CollectionUtil.isEmpty(tokenMap)){
                return;
            }

            MsgSendLogDTO msgSendLogDTO = buildMsgSendLogDTO(account, notice);
            msgSendLogDTO.setAccessToken(tokenMap.get(notice.getTenantId()));
            msgSendLogDTO.setThirdTemplateId(thirdTemplateId);
            msgSendLogDTO.setChannleType(MsgSendLogEnum.ChannleType.WECHAT.getValue());
            msgSendLogDTO.setTemplateType(MsgSendLogEnum.TemplateType.WECHAT.getValue());
            msgSendLogDTO.setData(JSONObject.toJSONString(buildData(notice)));
            msgSendLogDTO.setReceiveThridUid(account.getOpenId());
            SendHandler sender = sendHandlerFactory.getHandler(MsgSendLogEnum.ChannleType.WECHAT).orElseThrow(() -> new BizException("发送渠道暂不支持"));
            sender.send(msgSendLogDTO);
        }catch (Exception e){
            log.error("公告发送微信服务号失败，accountid={}，noticeid={}",account.getId(),notice.getId(),e);
        }
    }

    private Map buildData(MsgNotice notice) {
        Map<String,String> v1 = new HashMap<>();
        v1.put("value",notice.getTitle());
        Map<String,String> v2 = new HashMap<>();
        v2.put("value", NoticeContentUtil.getContentString(notice));
        Map<String, Map<String,String>> map = new HashMap<>();
        map.put("thing6",v1);
        map.put("thing7",v2);
        return map;
    }

    private void sendSelfSystem(MerchantStoreAccountVO account, MsgNotice notice) {
        if(account == null){
            return;
        }
        try {
            MsgSendLogDTO msgSendLogDTO = buildMsgSendLogDTO(account, notice);
            msgSendLogDTO.setChannleType(MsgSendLogEnum.ChannleType.SELF_SYSTEM.getValue());
            String contentString = NoticeContentUtil.getContentString(notice);
            if(ObjectUtil.isNull(contentString)) {
                return;
            }
            msgSendLogDTO.setData(contentString);
            SendHandler sender = sendHandlerFactory.getHandler(MsgSendLogEnum.ChannleType.SELF_SYSTEM).orElseThrow(() -> new BizException("发送渠道暂不支持"));
            sender.send(msgSendLogDTO);
        }catch (Exception e){
            log.error("公告发送小程序系统用户失败，accountid={}，noticeid={}",account.getId(),notice.getId(),e);
        }
    }

    private MsgSendLogDTO buildMsgSendLogDTO(MerchantStoreAccountVO account, MsgNotice notice) {
        MsgSendLogDTO msgSendLogDTO = new MsgSendLogDTO();
        msgSendLogDTO.setTenantId(account.getTenantId());
        msgSendLogDTO.setTemplateId(account.getStoreId());
        msgSendLogDTO.setContentType(MsgSendLogEnum.ContentType.NOTIC.getValue());
        msgSendLogDTO.setPageId(notice.getId());
        msgSendLogDTO.setPage("pages/notice-details/index");
        msgSendLogDTO.setSendAccountId(-1L);
        msgSendLogDTO.setTitle(notice.getTitle());
        msgSendLogDTO.setReceiveAccountId(account.getId());
        msgSendLogDTO.setStoreId(account.getStoreId());
        return msgSendLogDTO;
    }

}