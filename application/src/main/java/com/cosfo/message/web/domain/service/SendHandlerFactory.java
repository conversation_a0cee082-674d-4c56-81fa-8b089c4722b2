package com.cosfo.message.web.domain.service;

import com.cosfo.message.common.enums.MsgSendLogEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Component
@Slf4j
public class SendHandlerFactory implements ApplicationContextAware {

    private Map<MsgSendLogEnum.ChannleType, SendHandler> handlerMap = new HashMap<>();


    private ApplicationContext applicationContext;
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @PostConstruct
    private void init() {
        applicationContext.getBeansOfType(SendHandler.class).forEach((a, b) -> {
            handlerMap.put(b.getChannleType(), b);
        });
    }


    public Optional<SendHandler> getHandler(MsgSendLogEnum.ChannleType channleType) {
        return Optional.ofNullable(handlerMap.get(channleType));
    }
}
