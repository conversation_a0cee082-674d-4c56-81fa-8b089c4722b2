package com.cosfo.message.web.strategy.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.message.client.enums.ChannelTypeEnum;
import com.cosfo.message.client.enums.MessageContentTypeEnum;
import com.cosfo.message.client.enums.MsgBodyTypeEnum;
import com.cosfo.message.common.constants.WechatTemplateConstants;
import com.cosfo.message.common.util.FeishuUtils;
import com.cosfo.message.common.util.WeiXinConnectionUtil;
import com.cosfo.message.infrastructure.message.dao.MsgSendLogDao;
import com.cosfo.message.infrastructure.message.model.MsgSendLog;
import com.cosfo.message.infrastructure.template.dto.BatchMessageSendDTO;
import com.cosfo.message.infrastructure.template.dto.MessageBodyDTO;
import com.cosfo.message.infrastructure.template.dto.MessageSendParam;
import com.cosfo.message.infrastructure.template.dto.MessageUserDTO;
import com.cosfo.message.web.domain.dto.BuilderSendLogDTO;
import com.cosfo.message.web.domain.dto.MessageUidDTO;
import com.cosfo.message.web.domain.vo.WechatResponseVO;
import com.cosfo.message.web.facade.AuthUserAuthFacade;
import com.cosfo.message.web.strategy.SendLogStrategy;
import com.cosfo.message.web.strategy.SendLogStrategyHandler;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Sets;
import com.google.common.collect.Table;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.resp.AuthUserAuthResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * @Author: fansongsong
 * @Date: 2023-10-24
 * @Description:
 */
@Slf4j
@Component
public class WechatOaCustomSendLogStrategyImpl implements SendLogStrategy, InitializingBean {

    @Resource
    private MsgSendLogDao msgSendLogDao;

    @Resource
    private AuthUserAuthFacade authUserAuthFacade;

    /**
     * 微信公众号客服消息支持的消息类型
     */
    public static final Set<MsgBodyTypeEnum> OA_CUSTOM_SUPPORT_MSG_TYPE_LIST = Sets.newHashSet(
            MsgBodyTypeEnum.TEXT, MsgBodyTypeEnum.IMAGE, MsgBodyTypeEnum.VOICE, MsgBodyTypeEnum.VIDEO, MsgBodyTypeEnum.MUSIC
    );

    @Override
    public MsgSendLog builderCustomMsgSendLog(BuilderSendLogDTO builderSendLogDTO, MsgSendLog msgSendLog) {
        MessageSendParam messageSendParam = builderSendLogDTO.getMessageSendParam();
        String receiverUid = builderSendLogDTO.getReceiverUid();

        MessageBodyDTO mesg = messageSendParam.getMesg();
        MessageUserDTO receiver = messageSendParam.getReceiver();

        Table<Integer, String, AuthUserAuthResp> phoneTable = Optional.ofNullable(builderSendLogDTO.getMessageUidDTO())
                .map(MessageUidDTO::getPhoneTable).orElse(HashBasedTable.create());

        msgSendLog.setReceiveThridUid(getWechatOaThirdUid(receiverUid, receiver, phoneTable));

        // 把请求参数拼接好存储
        MsgBodyTypeEnum msgBodyTypeEnum = MsgBodyTypeEnum.getByType(mesg.getMsgBodyType());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("touser",msgSendLog.getReceiveThridUid());
        jsonObject.put("msgtype", msgBodyTypeEnum.getMsgType());
        jsonObject.put(msgBodyTypeEnum.getMsgType(), JSON.parseObject(mesg.getData()));
        String reqJson = jsonObject.toJSONString();

        msgSendLog.setReq(reqJson);
        msgSendLog.setChannelCode(builderSendLogDTO.getMessageSendParam().getChannelCode());
        return msgSendLog;
    }

    @Override
    public void messageSend(MsgSendLog msgSendLog) {
        String accessToken = authUserAuthFacade.queryWeChatToken(msgSendLog.getChannelCode());
        String paramJson = msgSendLog.getReq();
        WechatResponseVO wechatResponseVO = WeiXinConnectionUtil.postWechatByUrl(paramJson, WechatResponseVO.class , accessToken, WechatTemplateConstants.WECHAT_OA_CUSTOM_MESSAGE_URL);
        Integer respCode = Optional.ofNullable(wechatResponseVO).map(WechatResponseVO::getErrcode).orElseThrow(() -> new BizException("发送微信公众号客服消息，响应码异常"));

        boolean success = WechatTemplateConstants.successCode.equals(respCode);
        msgSendLogDao.updateMsgLogResp(msgSendLog, wechatResponseVO, success);
        if (!success) {
            log.error("微信公众号messageSend,客服消息发送失败,消息ID={},errCode={}", msgSendLog.getId(), respCode);
        }
    }

    @Override
    public MessageUidDTO queryUidInfo(List<MessageUserDTO> queryList, BatchMessageSendDTO batchMessageSendDTO) {
        return authUserAuthFacade.getWechatOaThirdUid(queryList, batchMessageSendDTO);
    }

    @Override
    public void validParam(MessageBodyDTO mesg, Integer channleType, String channelCode) {
        if (StringUtils.isEmpty(channelCode)) {
            throw new ParamsException("公众号客服消息，渠道类型不能为空");
        }
        if (!FeishuUtils.isJSONValid(mesg.getData())) {
            throw new BizException("公众号客服消息，MessageBodyDTO中data需要是json格式");
        }
        MsgBodyTypeEnum msgBodyTypeEnum = MsgBodyTypeEnum.getByType(mesg.getMsgBodyType());
        if (Objects.isNull(msgBodyTypeEnum)) {
            throw new ParamsException("MessageBody消息参数异常");
        }
        if (!OA_CUSTOM_SUPPORT_MSG_TYPE_LIST.contains(msgBodyTypeEnum)) {
            throw new ParamsException("MessageBody消息类型异常");
        }
    }

    @Override
    public void afterPropertiesSet() {
        SendLogStrategyHandler.registryStrategy(ChannelTypeEnum.WECHAT_OFFICIAL_ACCOUNT, MessageContentTypeEnum.CUSTOM_MESSAGE, this);
    }
}
