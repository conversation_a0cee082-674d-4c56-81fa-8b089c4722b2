package com.cosfo.message.web.strategy.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.message.client.enums.ChannelTypeEnum;
import com.cosfo.message.client.enums.JumpUrlTypeEnum;
import com.cosfo.message.client.enums.MessageContentTypeEnum;
import com.cosfo.message.common.constants.NumberConstants;
import com.cosfo.message.common.constants.WechatTemplateConstants;
import com.cosfo.message.common.util.FeishuUtils;
import com.cosfo.message.common.util.WeiXinConnectionUtil;
import com.cosfo.message.infrastructure.message.dao.MsgSendLogDao;
import com.cosfo.message.infrastructure.message.model.MsgSendLog;
import com.cosfo.message.infrastructure.template.dto.BatchMessageSendDTO;
import com.cosfo.message.infrastructure.template.dto.MessageBodyDTO;
import com.cosfo.message.infrastructure.template.dto.MessageSendParam;
import com.cosfo.message.infrastructure.template.dto.MessageUserDTO;
import com.cosfo.message.infrastructure.template.dto.TemplateWechatOfficialAccountQueryDTO;
import com.cosfo.message.infrastructure.template.dto.WechatOaSendMessageDTO;
import com.cosfo.message.infrastructure.template.model.MsgTemplateWechatOfficialAccountPublic;
import com.cosfo.message.starter.config.WeChatOaMessageConfig;
import com.cosfo.message.web.domain.dto.BuilderSendLogDTO;
import com.cosfo.message.web.domain.dto.MessageUidDTO;
import com.cosfo.message.web.domain.dto.WechatOaSendLogReqDTO;
import com.cosfo.message.web.domain.service.TemplateWechatOfficialAccountDomainService;
import com.cosfo.message.web.domain.vo.WechatResponseVO;
import com.cosfo.message.web.facade.AuthUserAuthFacade;
import com.cosfo.message.web.strategy.SendLogStrategy;
import com.cosfo.message.web.strategy.SendLogStrategyHandler;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.resp.AuthUserAuthResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @Author: fansongsong
 * @Date: 2023-07-19
 * @Description:微信公众号发送模版消息策略
 */
@Slf4j
@Component
public class WechatOaTemplateSendLogStrategyImpl implements SendLogStrategy, InitializingBean {

    @Resource
    private MsgSendLogDao msgSendLogDao;

    @Resource
    private AuthUserAuthFacade authUserAuthFacade;

    @Resource
    private WeChatOaMessageConfig weChatOaMessageConfig;

    @Resource
    private TemplateWechatOfficialAccountDomainService templateWechatOfficialAccountDomainService;

    @Override
    public MsgSendLog builderCustomMsgSendLog(BuilderSendLogDTO builderSendLogDTO, MsgSendLog msgSendLog) {
        MessageSendParam messageSendParam = builderSendLogDTO.getMessageSendParam();
        String receiverUid = builderSendLogDTO.getReceiverUid();

        MessageBodyDTO mesg = messageSendParam.getMesg();
        MessageUserDTO receiver = messageSendParam.getReceiver();
        String templateId = mesg.getTemplateId();
        if (Objects.isNull(templateId)) {
            TemplateWechatOfficialAccountQueryDTO templateWechatOfficialAccountQueryDTO = new TemplateWechatOfficialAccountQueryDTO();
            templateWechatOfficialAccountQueryDTO.setTemplateCode(mesg.getTemplateCode());
            templateWechatOfficialAccountQueryDTO.setChannelCode(messageSendParam.getChannelCode());
            List<MsgTemplateWechatOfficialAccountPublic> msgTemplateWechatOfficialAccountPublics = templateWechatOfficialAccountDomainService.queryTemplateWechatOfficialAccountList(templateWechatOfficialAccountQueryDTO);
            if (CollectionUtil.isEmpty(msgTemplateWechatOfficialAccountPublics)) {
                throw new BizException("找不到对应模版信息");
            }
            templateId = msgTemplateWechatOfficialAccountPublics.get(0).getWechatTemplateId();
        }

        msgSendLog.setThirdTemplateId(templateId);
        msgSendLog.setTemplateType(NumberConstants.ZERO.intValue());

        Table<Integer, String, AuthUserAuthResp> phoneTable = Optional.ofNullable(builderSendLogDTO.getMessageUidDTO())
                .map(MessageUidDTO::getPhoneTable).orElse(HashBasedTable.create());

        msgSendLog.setReceiveThridUid(getWechatOaThirdUid(receiverUid, receiver, phoneTable));

        // 把请求参数拼接好存储
        WechatOaSendLogReqDTO wechatOaSendLogReqDTO = new WechatOaSendLogReqDTO();
        if (Objects.nonNull(receiver)) {
            wechatOaSendLogReqDTO.setPhone(receiver.getPhone());
        }
        wechatOaSendLogReqDTO.setSystemOriginType(messageSendParam.getSystemOriginType());
        WechatOaSendMessageDTO wechatOaSendMessageDTO = new WechatOaSendMessageDTO();
        wechatOaSendMessageDTO.setData(JSON.parseObject(mesg.getData()));

        wechatOaSendMessageDTO.setClientMsgId(mesg.getClientMsgId());
        wechatOaSendMessageDTO.setTouser(msgSendLog.getReceiveThridUid());
        wechatOaSendMessageDTO.setTemplateId(templateId);
        if (JumpUrlTypeEnum.PAGE.getType().equals(mesg.getJumpUrlType())) {
            wechatOaSendMessageDTO.setUrl(mesg.getJumpUrl());
        } else if (JumpUrlTypeEnum.APPLET.getType().equals(mesg.getJumpUrlType())) {
            wechatOaSendMessageDTO.setMiniprogram(JSON.parseObject(mesg.getJumpUrl()));
        }
        wechatOaSendLogReqDTO.setWechatOaSendMessageDTO(wechatOaSendMessageDTO);
        msgSendLog.setReq(JSON.toJSONString(wechatOaSendLogReqDTO));
        msgSendLog.setChannelCode(builderSendLogDTO.getMessageSendParam().getChannelCode());
        return msgSendLog;
    }

    @Override
    public void messageSend(MsgSendLog msgSendLog) {
        String accessToken = authUserAuthFacade.queryWeChatToken(msgSendLog.getChannelCode());
        WechatOaSendLogReqDTO wechatOaSendLogReqDTO = JSON.parseObject(msgSendLog.getReq(), WechatOaSendLogReqDTO.class);
        WechatOaSendMessageDTO wechatOaSendMessageDTO = wechatOaSendLogReqDTO.getWechatOaSendMessageDTO();
        String paramJson = JSON.toJSONString(wechatOaSendMessageDTO);

        WechatResponseVO wechatResponseVO = WeiXinConnectionUtil.postWechatByUrl(paramJson, WechatResponseVO.class , accessToken, WechatTemplateConstants.WECHAT_OA_TEMPLATE_MESSAGE_URL);
        Integer respCode = Optional.ofNullable(wechatResponseVO).map(WechatResponseVO::getErrcode).orElseThrow(() -> new BizException("发送微信公众号模板消息，响应码异常"));

        boolean success = WechatTemplateConstants.successCode.equals(respCode);
        msgSendLogDao.updateMsgLogResp(msgSendLog, wechatResponseVO, success);
        if (weChatOaMessageConfig.getWeChatOaMessageErrorCodes().contains(String.valueOf(respCode))) {
            log.info("微信公众号模版消息发送失败,消息ID={},errCode={}", msgSendLog.getId(), respCode);
            return;
        }

        if (!success) {
            log.error("微信公众号messageSend,模版消息发送失败,消息ID={},errCode={}", msgSendLog.getId(), respCode);
            return;
        }
    }

    @Override
    public MessageUidDTO queryUidInfo(List<MessageUserDTO> queryList, BatchMessageSendDTO batchMessageSendDTO) {
        return authUserAuthFacade.getWechatOaThirdUid(queryList, batchMessageSendDTO);
    }


    @Override
    public void validParam(MessageBodyDTO mesg, Integer channleType, String channelCode) {
        if (StringUtils.isEmpty(channelCode)) {
            throw new ParamsException("公众号消息，渠道类型不能为空");
        }
        if (!FeishuUtils.isJSONValid(mesg.getData())) {
            throw new BizException("公众号消息，MessageBodyDTO中data需要是json格式");
        }
        if (StringUtils.isEmpty(mesg.getTemplateId()) && StringUtils.isEmpty(mesg.getTemplateCode())) {
            throw new ParamsException("公众号消息，templateId、templateCode入参二选一");
        }
    }


    @Override
    public void afterPropertiesSet() {
        SendLogStrategyHandler.registryStrategy(ChannelTypeEnum.WECHAT_OFFICIAL_ACCOUNT, MessageContentTypeEnum.NORMAL, this);
    }
}
