//package com.cosfo.message.web.mq.demo.consumer;
//
//import com.cosfo.message.infrastructure.notice.dto.MsgReadLogDTO;
//import com.cosfo.message.web.domain.service.NoticeDomianService;
//import com.cosfo.message.web.mq.demo.event.DemoQueryEvent;
//import org.apache.rocketmq.spring.core.RocketMQListener;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//
//@Component
////@RocketMQMessageListener(consumerGroup = "demoGroup", topic = "demoTopic", consumeMode = ConsumeMode.ORDERLY, enableMsgTrace = false)
//public class DemoConsumer implements RocketMQListener<DemoQueryEvent> {
//
//    @Autowired
//    private NoticeDomianService noticeDomianService;
//
//    @Override
//    public void onMessage(DemoQueryEvent event) {
//        MsgReadLogDTO input = new MsgReadLogDTO();
////        input.setDemoName(event.getDemoName());
//        noticeDomianService.supportMsgReadLog(input);
//    }
//}
