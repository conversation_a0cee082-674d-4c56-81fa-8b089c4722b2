package com.cosfo.message.web.facade;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cosfo.message.common.enums.MsgSceneEnum;
import com.cosfo.message.web.domain.vo.MerchantStoreAccountVO;
import com.cosfo.message.web.domain.vo.MsgSceneTenantVO;
import com.cosfo.message.web.domain.vo.MsgSceneVO;
import com.cosfo.message.web.facade.converter.OmsConverter;
import com.cosfo.oms.client.common.MsgSceneTenantMappingEnum;
import com.cosfo.oms.client.provider.merchant.MerchantStoreAccountQueryProvider;
import com.cosfo.oms.client.provider.merchant.MerchantStoreQueryProvider;
import com.cosfo.oms.client.provider.msgscene.MsgSceneQueryProvider;
import com.cosfo.oms.client.provider.tenant.TenantQueryProvider;
import com.cosfo.oms.client.req.MsgSceneQueryReq;
import com.cosfo.oms.client.req.ThirdTemplateIdQueryReq;
import com.cosfo.oms.client.resp.*;
import com.cosfo.message.web.domain.vo.MerchantStoreVO;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
/**
 * 弃用
 */
@Deprecated
@Slf4j
@Component
public class OmsOldFacade {
    @DubboReference
    private MerchantStoreQueryProvider merchantStoreQueryProvider;
    @DubboReference
    private TenantQueryProvider tenantQueryProvider;
    @DubboReference
    private MerchantStoreAccountQueryProvider storeAccountQueryProvider;
    @DubboReference
    private MsgSceneQueryProvider msgSceneQueryProvider;
    public Map<Long,String> getTokenByTenantIds(List<Long> tenantIds){
        DubboResponse<List<WechatAuthorizerResp>> response = tenantQueryProvider.getAuthorizerByTenantIds(tenantIds);
        if (!response.isSuccess()){
            throw new BizException(response.getMsg());
        }
        if(CollectionUtil.isEmpty(response.getData())){
            return Collections.emptyMap();
        }
        return response.getData().stream().filter(e-> ObjectUtil.isNotNull(e.getAccessToken())).collect(Collectors.toMap(WechatAuthorizerResp::getTenantId,WechatAuthorizerResp::getAccessToken));
    }
    public List<MerchantStoreAccountVO> getStoreAccountIdsByStoreId(Long storeId,Integer type,Integer status){
        DubboResponse<List<MerchantStoreAccountResp>> response = storeAccountQueryProvider.getByStoreId(storeId);
        if (!response.isSuccess()){
            throw new BizException(response.getMsg());
        }
        List<MerchantStoreAccountResp> list = response.getData();
        if(ObjectUtil.isNotNull(type)){
            list = list.stream().filter(e -> Objects.equals(type, e.getType())).collect(Collectors.toList());
        }
        if(ObjectUtil.isNotNull(status)){
            list = list.stream().filter(e -> Objects.equals(status,e.getStatus())).collect(Collectors.toList());
        }
        return list.stream().map(OmsConverter::merchantStoreAccountResp2VO).collect(Collectors.toList());
    }
    public List<MerchantStoreAccountVO> getStoreAccountIdsByTenantId(Long tenantId,Integer type,Integer status){
        DubboResponse<List<MerchantStoreAccountResp>> response = storeAccountQueryProvider.getByTenantId(tenantId);
        if (!response.isSuccess()){
            throw new BizException(response.getMsg());
        }
        List<MerchantStoreAccountResp> list = response.getData();
        if(ObjectUtil.isNotNull(type)){
            list = list.stream().filter(e -> Objects.equals(type, e.getType())).collect(Collectors.toList());
        }
        if(ObjectUtil.isNotNull(status)){
            list = list.stream().filter(e -> Objects.equals(status,e.getStatus())).collect(Collectors.toList());
        }
        return list.stream().map(OmsConverter::merchantStoreAccountResp2VO).collect(Collectors.toList());
    }
    public List<MerchantStoreVO> getStoreByIds(List<Long> storeIds){
        DubboResponse<List<MerchantStoreResp>> response = merchantStoreQueryProvider.getByIds(storeIds);
        if (!response.isSuccess()){
            throw new BizException(response.getMsg());
        }
        return response.getData().stream().map(OmsConverter::merchantStoreResp2VO).collect(Collectors.toList());
    }
    public MsgSceneVO getMsgSceneById(Long sceneId){
        DubboResponse<MsgSceneResultResp> response = msgSceneQueryProvider.getScene(sceneId);
        if (!response.isSuccess()){
            throw new BizException(response.getMsg());
        }
        MsgSceneResultResp data = response.getData();
        return OmsConverter.msgSceneResp2VO(data);
    }
    public List<MsgSceneTenantVO> listAvailableSceneTenantByTenantId(Long sceneId, Long tenantId, MsgSceneTenantMappingEnum.TemplateType templateType){
        MsgSceneQueryReq req = new MsgSceneQueryReq();
        req.setTemplateType(templateType.getValue());
        req.setSceneId(sceneId);
        req.setAvailableStatus(MsgSceneEnum.AvailableStatus.ABLED.getValue());
        DubboResponse<List<MsgSceneTenantResultResp>> response = msgSceneQueryProvider.listSceneTenantByTenantId(req, tenantId);
        if (!response.isSuccess()){
            throw new BizException(response.getMsg());
        }
        List<MsgSceneTenantResultResp> data = response.getData();
        return OmsConverter.msgSceneTenantResp2VO(data);
    }
}

