package com.cosfo.message.web.domain.service;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.message.client.msgtemplate.resp.KeyWordResultResp;
import com.cosfo.message.common.constants.WechatTemplateConstants;
import com.cosfo.message.common.enums.MsgSceneEnum;
import com.cosfo.message.common.enums.MsgTemplateWechatEnum;
import com.cosfo.message.common.util.WeiXinConnectionUtil;
import com.cosfo.message.infrastructure.template.dao.*;
import com.cosfo.message.infrastructure.template.dto.*;
import com.cosfo.message.infrastructure.template.model.*;
import com.cosfo.message.web.domain.vo.*;
import com.cosfo.message.web.provider.converter.TemplateProviderConverter;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.CallerException;
import net.xianmu.common.exception.ParamsException;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date : 2023/2/10 14:56
 */
@Slf4j
@Component
public class TemplateDomainService {
    @Resource
    private MsgTemplateApplyLogDao msgTemplateApplyLogDao;
    @Resource
    private MsgTemplateWechatDao msgTemplateWechatDao;
    @Resource
    private MsgTemplateWechatPublicDao msgTemplateWechatPublicDao;

    public PageInfo<MsgPublicTemplateWechatListVO> getWechatPublicMsgTemplateList(MsgTemplateWechatPublicDTO msgTemplateWechatPublicDTO){
        PageInfo<MsgTemplateWechatPublic> wechatPublicMsgTemplatePage = msgTemplateWechatPublicDao.getWechatPublicMsgTemplateList(msgTemplateWechatPublicDTO);
        List<MsgPublicTemplateWechatListVO> voList = wechatPublicMsgTemplatePage.getList().stream().map(TemplateProviderConverter::msgTemplateWechatPublic2VO).collect(Collectors.toList());
        PageInfo<MsgPublicTemplateWechatListVO> pageInfo = (PageInfo<MsgPublicTemplateWechatListVO>)TemplateProviderConverter.turnToPageInfo(new PageInfo<MsgPublicTemplateWechatListVO>(), wechatPublicMsgTemplatePage);
        pageInfo.setList(voList);
        return pageInfo;
    }

    public PageInfo<MsgTemplateWechatListVO> getWechatMsgTemplateList(WechatMsgTemplateDTO wechatMsgTemplateDTO){
        if (ObjectUtils.isEmpty(wechatMsgTemplateDTO)){
            wechatMsgTemplateDTO = new WechatMsgTemplateDTO();
            wechatMsgTemplateDTO.setPageIndex(WechatTemplateConstants.DEFAULT_PAGE_INDEX);
            wechatMsgTemplateDTO.setPageSize(WechatTemplateConstants.DEFAULT_PAGE_SIZE);
        }
        // 这里从微信公共模板池查询
        PageInfo<MsgTemplateWechatPublic> msgTemplateWechatPage = msgTemplateWechatPublicDao.getMsgTemplateWechatList(wechatMsgTemplateDTO);
        List<MsgTemplateWechatListVO> msgTemplateWechatListVOList = new ArrayList<>();
        for (MsgTemplateWechatPublic msgTemplateWechat : msgTemplateWechatPage.getList()) {
            // 已关联模板数
            int countTemplate = msgTemplateWechatDao.countTemplate(msgTemplateWechat.getId());
            // 已失效模板数
            int countDisableTemplate = msgTemplateWechatDao.countDisableTemplate(msgTemplateWechat.getId(), MsgSceneEnum.AvailableStatus.DISABLED.getValue());
            msgTemplateWechatListVOList.add(TemplateProviderConverter.msgTemplateWechat2VO(msgTemplateWechat, countTemplate, countDisableTemplate));
        }
        PageInfo<MsgTemplateWechatListVO> pageInfo = (PageInfo<MsgTemplateWechatListVO>) TemplateProviderConverter.turnToPageInfo(new PageInfo<MsgTemplateWechatListVO>(), msgTemplateWechatPage);
        pageInfo.setList(msgTemplateWechatListVOList);
        return pageInfo;
    }


    public MsgTemplateToAppVO getWechatAppInfoById(Long id){
        // 查询该商户模板信息
        MsgTemplateWechat msgTemplateWechat = msgTemplateWechatDao.getMsgTemplateWechatById(id);
        // 查询该模板对应的帆台模板信息
        MsgTemplateWechatPublic wechatPublic = msgTemplateWechatPublicDao.getWechatPublicMsgTemplateById(msgTemplateWechat.getTemplateWechatPublicId());
        return TemplateProviderConverter.msgTemplateWechat2AppVO(msgTemplateWechat, wechatPublic);
    }


    public MsgPublicTemplateDetailVO getWechatMsgTemplateDetailByPublicTemplateId(Long id){
        MsgTemplateWechatPublic wechatPublic = msgTemplateWechatPublicDao.getWechatPublicMsgTemplateById(id);
        return TemplateProviderConverter.publicTemplate2DetailVO(wechatPublic);
    }

    public List<MsgTemplateWechatAppListVO> getFailListByCondition(MsgWechatAppDTO msgWechatAppDTO){
        // 查询出已失败的租户
        // 在个人模板池里查询所有相应帆台模板id已失败的模板
        List<MsgTemplateWechatAppListVO> msgVOList = new LinkedList<>();
        WechatMsgTemplateQueryDTO queryDTO = new WechatMsgTemplateQueryDTO();
        queryDTO.setId(msgWechatAppDTO.getId());
        queryDTO.setSuccessFlag(MsgSceneEnum.CreateStatus.FAIL.getValue());
        if (ObjectUtils.isNotEmpty(msgWechatAppDTO.getMallId())){
            queryDTO.setTenantId(msgWechatAppDTO.getMallId());
        }
        List<MsgTemplateWechat> failList = msgTemplateWechatDao.getMsgTemplateWechatListByCondition(queryDTO);
        // 查询出他们最新的失败记录
        // 创建失败的模板id集合
        List<Long> idList = failList.stream().map(item -> item.getId()).collect(Collectors.toList());
        MsgTemplateApplyLogQueryDTO msgTemplateApplyLogQueryDTO = new MsgTemplateApplyLogQueryDTO();
        if (!CollectionUtils.isEmpty(idList)){
            msgTemplateApplyLogQueryDTO.setTemplateType(MsgTemplateWechatEnum.TemplateType.WECHAT.getValue());
            msgTemplateApplyLogQueryDTO.setSuccessFlag(MsgSceneEnum.CreateStatus.FAIL.getValue());
            msgTemplateApplyLogQueryDTO.setIdList(idList);
            List<MsgTemplateApplyLog> logList = msgTemplateApplyLogDao.getListByCondition(msgTemplateApplyLogQueryDTO);

            for (MsgTemplateApplyLog msgTemplateApplyLog : logList) {
                msgVOList.add(TemplateProviderConverter.applyLog2VO(msgTemplateApplyLog));
            }
        }
        return msgVOList;
    }

    public Long createTemplate(PrivateMsgTemplateWechatDTO privateMsgTemplateWechatDTO){
        // 查询帆台模板
        MsgTemplateWechatPublic template = msgTemplateWechatPublicDao.getWechatPublicMsgTemplateById(privateMsgTemplateWechatDTO.getId());
        // 创建模板池模板
        MsgTemplateWechatPublic newWechatPublic = template;
        List<KeyWordResultResp> keywordList = TemplateProviderConverter.getKeywordListNoTrans(newWechatPublic.getKeywords());
        Map<Integer, KeyWordResultResp> resultRespMap = keywordList.stream().collect(Collectors.toMap(KeyWordResultResp::getKid, item -> item));
        List<KeyWordResultResp> newKeywordList = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(privateMsgTemplateWechatDTO)){
            for (Integer kid : privateMsgTemplateWechatDTO.getKidList()) {
                newKeywordList.add(resultRespMap.get(kid));
            }
        }
        newWechatPublic.setPId(template.getId());
        newWechatPublic.setId(null);
        // 添加场景说明
        newWechatPublic.setScene(privateMsgTemplateWechatDTO.getScene());
        newWechatPublic.setKeywords(JSONObject.toJSONString(newKeywordList));
        newWechatPublic.setCreator(privateMsgTemplateWechatDTO.getUId());
        newWechatPublic.setCreateTime(LocalDateTime.now());
        msgTemplateWechatPublicDao.save(newWechatPublic);
        return newWechatPublic.getId();
    }

    public WechatCreateTemplateVO createWeiXinTemplate(PrivateMsgTemplateWechatDTO privateMsgTemplateWechatDTO){
        // 查询帆台模板
        MsgTemplateWechatPublic template = msgTemplateWechatPublicDao.getWechatPublicMsgTemplateById(privateMsgTemplateWechatDTO.getId());
        if (ObjectUtils.isEmpty(privateMsgTemplateWechatDTO.getAuthorDTOList())){
            throw new ParamsException(501,"该商户信息有误，请联系管理员!");
        }
        WechatAuthorDTO wechatAuthorDTO = privateMsgTemplateWechatDTO.getAuthorDTOList().get(0);
        // 请求微信接口创建私人模板
        WechatCreateTemplateVO wechatCreateTemplateVO = createWechatConnection(privateMsgTemplateWechatDTO, wechatAuthorDTO, template);
        // 翻译错误码
        if (ObjectUtils.isNotEmpty(MsgTemplateWechatEnum.WechatErrorCode.getWechatErrorCode(wechatCreateTemplateVO.getErrCode()))){
            wechatCreateTemplateVO.setErrMsg(MsgTemplateWechatEnum.WechatErrorCode.getWechatErrorCode(wechatCreateTemplateVO.getErrCode()).getContent());
        }
        return wechatCreateTemplateVO;
    }

    public WechatCreateAllTemplateVO createAllTemplate(PrivateMsgTemplateWechatDTO privateMsgTemplateWechatDTO){
        // 查询帆台模板
        MsgTemplateWechatPublic template = msgTemplateWechatPublicDao.getWechatPublicMsgTemplateById(privateMsgTemplateWechatDTO.getId());
        List<WechatAuthorDTO> authorDTOList = privateMsgTemplateWechatDTO.getAuthorDTOList();
        List<WechatCreateTemplateVO> wechatCreateTemplateVOList = new ArrayList<>();
        for (WechatAuthorDTO wechatAuthorizer : authorDTOList) {
            WechatCreateTemplateVO wechatCreateTemplateVO = createWechatConnection(privateMsgTemplateWechatDTO, wechatAuthorizer, template);
            // 翻译错误码
            if (ObjectUtils.isNotEmpty(MsgTemplateWechatEnum.WechatErrorCode.getWechatErrorCode(wechatCreateTemplateVO.getErrCode()))){
                wechatCreateTemplateVO.setErrMsg(MsgTemplateWechatEnum.WechatErrorCode.getWechatErrorCode(wechatCreateTemplateVO.getErrCode()).getContent());
            }
            // 请求微信接口
            wechatCreateTemplateVOList.add(wechatCreateTemplateVO);
        }
        Integer allSize = wechatCreateTemplateVOList.size();
        log.info("allSize:{}",allSize);
        WechatCreateAllTemplateVO wechatCreateAllTemplateVO = new WechatCreateAllTemplateVO();
        List<WechatCreateTemplateVO> successTemplateList = wechatCreateTemplateVOList.stream().filter(item -> MsgTemplateWechatEnum.WechatErrorCode.SUCCESS.getValue().equals(item.getErrCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(successTemplateList)){
            wechatCreateAllTemplateVO.setSuccessCount(0);
        }else {
            wechatCreateAllTemplateVO.setSuccessCount(successTemplateList.size());
        }
        log.info("countSuccess:{}"+wechatCreateAllTemplateVO.getSuccessCount());
        wechatCreateAllTemplateVO.setFailCount(allSize - wechatCreateAllTemplateVO.getSuccessCount());
        return wechatCreateAllTemplateVO;
    }

    public List<Long> getRelateIdList(Long id){
        MsgWechatAppDTO msgWechatAppDTO = new MsgWechatAppDTO();
        msgWechatAppDTO.setId(id);
        // 查询帆台模板id下关联的子模板id列表
        msgWechatAppDTO.setSuccessFlag(MsgTemplateWechatEnum.SuccessFlag.SUCCESS.getValue());
        List<Long> relateIdList = msgTemplateWechatDao.getIdListByCondition(msgWechatAppDTO);
        return relateIdList;
    }

    public List<Long> getTemplateIdListByStatus(Long id, Integer availableStatus){
        MsgWechatAppDTO msgWechatAppDTO = new MsgWechatAppDTO();
        msgWechatAppDTO.setId(id);
        msgWechatAppDTO.setAvailableStatus(availableStatus);
        // 查询帆台模板id下已生效的子模板id列表
        List<Long> relateIdList = msgTemplateWechatDao.getIdListByCondition(msgWechatAppDTO);
        return relateIdList;
    }

    public MsgPublicTemplateWechatListVO getWechatPublicMsgTemplateById(Long id){
        MsgTemplateWechatPublic template = msgTemplateWechatPublicDao.getWechatPublicMsgTemplateById(id);
        MsgPublicTemplateWechatListVO msgPublicTemplateWechatListVO = TemplateProviderConverter.msgTemplateWechatPublic2VO(template);
        return msgPublicTemplateWechatListVO;
    }

    public List<MsgTemplateWechatDetailVO> getWechatListByCondition(WechatMsgTemplateQueryDTO wechatMsgTemplateQueryDTO){
        List<MsgTemplateWechat> wechatListByCondition = msgTemplateWechatDao.getMsgTemplateWechatListByCondition(wechatMsgTemplateQueryDTO);
        log.info("wechatListByCondition:{}",JSONObject.toJSONString(wechatListByCondition));
        List<MsgTemplateWechatDetailVO> detailVOList = wechatListByCondition.stream().map(TemplateProviderConverter::msgTemplateWechat2DetailVO).collect(Collectors.toList());
        return detailVOList;
    }

    public WechatCreateTemplateVO  createWechatConnection(PrivateMsgTemplateWechatDTO privateMsgTemplateWechatDTO, WechatAuthorDTO wechatAuthorDTO, MsgTemplateWechatPublic template){
        // 请求微信接口
        MsgTemplateApplyLog msgTemplateApplyLog = new MsgTemplateApplyLog();
        privateMsgTemplateWechatDTO.setTid(String.valueOf(template.getWechatTid()));
        privateMsgTemplateWechatDTO.setScene(template.getScene());
        List<KeyWordResultResp> keywordList = TemplateProviderConverter.getKeywordList(template.getKeywords());
        List<Integer> kidList = keywordList.stream().map(item -> item.getKid()).collect(Collectors.toList());
        privateMsgTemplateWechatDTO.setKidList(kidList);
        WechatCreateTemplateVO wechatCreateTemplateVO = new WechatCreateTemplateVO();
        //插入一条模板信息，补充申请记录信息并插入
        MsgTemplateWechat msgTemplateWechat = new MsgTemplateWechat();
        try {
            WechatCreateTemplateDTO wechatCreateTemplateDTO = TemplateProviderConverter.packageWechatTemplate(privateMsgTemplateWechatDTO);
            // 请求微信接口封装返回参数
//            wechatCreateTemplateVO = postWechatCreateTemplate(wechatCreateTemplateDTO, wechatAuthorDTO.getAccessToken());
            wechatCreateTemplateVO = WeiXinConnectionUtil.postWechatByUrl(wechatCreateTemplateDTO, wechatCreateTemplateVO , wechatAuthorDTO.getAccessToken(), WechatTemplateConstants.addMessageTemplateUrl);

            msgTemplateApplyLog.setAppId(wechatAuthorDTO.getAppId());
            msgTemplateApplyLog.setTemplateType(MsgTemplateWechatEnum.TemplateType.WECHAT.getValue());
            msgTemplateApplyLog.setTenantId(wechatAuthorDTO.getTenantId());
            msgTemplateWechat.setTenantId(wechatAuthorDTO.getTenantId());
            msgTemplateWechat.setTemplateWechatPublicId(privateMsgTemplateWechatDTO.getId());
            msgTemplateWechat.setAvailableStatus(MsgSceneEnum.AvailableStatus.ABLED.getValue());
            msgTemplateWechat.setWechatAppId(wechatAuthorDTO.getAppId());
            // 获取微信公共模板的tid
            msgTemplateWechat.setWechatTid(template.getWechatTid());
            msgTemplateWechat.setWechatTemplateId(wechatCreateTemplateVO.getPriTmplId());
            // 根据帆台关键词列表调用获取个人关键词列表
            msgTemplateWechat.setKeywords(template.getKeywords());
            msgTemplateWechat.setBindTime(wechatAuthorDTO.getCreateTime());
            msgTemplateWechat.setCreator(privateMsgTemplateWechatDTO.getUId());

            // 发送对象
            msgTemplateApplyLog.setReq(JSON.toJSONString(wechatCreateTemplateDTO));
            // 返回对象
            msgTemplateApplyLog.setResp(JSONObject.toJSONString(wechatCreateTemplateVO));
            if (WechatTemplateConstants.successCode.equals(wechatCreateTemplateVO.getErrCode())){
                msgTemplateWechat.setSuccessFlag(MsgSceneEnum.CreateStatus.SUCCESS.getValue());
                msgTemplateApplyLog.setSuccessFlag(MsgSceneEnum.CreateStatus.SUCCESS.getValue());
            }else {
                msgTemplateWechat.setSuccessFlag(MsgSceneEnum.CreateStatus.FAIL.getValue());
                msgTemplateApplyLog.setSuccessFlag(MsgSceneEnum.CreateStatus.FAIL.getValue());
                // 翻译错误码
                msgTemplateApplyLog.setReason(MsgTemplateWechatEnum.WechatErrorCode.getWechatErrorCode(wechatCreateTemplateVO.getErrCode()).getContent());
            }
            msgTemplateWechatDao.saveOrUpdateByCondition(msgTemplateWechat);
            msgTemplateApplyLog.setTemplateId(msgTemplateWechat.getId());
            msgTemplateApplyLogDao.saveOrUpdate(msgTemplateApplyLog);
            wechatCreateTemplateVO.setId(msgTemplateWechat.getId());
            wechatCreateTemplateVO.setTenantId(wechatAuthorDTO.getTenantId());
            return wechatCreateTemplateVO;
        }catch (Exception e){
            log.error("小程序:"+wechatAuthorDTO.getAppId()+"创建过程中发生错误：{}",e);
            throw new CallerException(506,e.getMessage());
        }
    }

    public void getWechatPublicList(String accessToken, int i, List<CategoryVO> categoryVOList){
        for (CategoryVO categoryVO : categoryVOList) {
            PublicTemplateCommonDTO publicTemplateCommonDTO = new PublicTemplateCommonDTO();
            publicTemplateCommonDTO.setLimit(30);
            publicTemplateCommonDTO.setStart(String.valueOf(i*30));
            publicTemplateCommonDTO.setIds(String.valueOf(categoryVO.getId()));
            PublicTemplateCommonVO publicTemplateCommonVO = getPubTemplateTitleList(publicTemplateCommonDTO, accessToken);
            List<TemplateTitleVO> templateTitleVOList = publicTemplateCommonVO.getData();
            for (TemplateTitleVO templateTitleVO : templateTitleVOList) {
                MsgTemplateWechatPublic msgTemplateWechatPublic = new MsgTemplateWechatPublic();
                msgTemplateWechatPublic.setWechatTid(templateTitleVO.getTid());
                msgTemplateWechatPublic.setWechatCategoryId(categoryVO.getId());
                msgTemplateWechatPublic.setWechatCategoryName(categoryVO.getName());
                msgTemplateWechatPublic.setWechatType(templateTitleVO.getType());
                msgTemplateWechatPublic.setWechatTitle(templateTitleVO.getTitle());
                msgTemplateWechatPublic.setTemplateName(templateTitleVO.getTitle());
                String params = "&tid="+templateTitleVO.getTid();
                String keywords = JSONObject.toJSONString(WeiXinConnectionUtil.getWechatByUrl(accessToken, new WechatGetKeyWordVO(), WechatTemplateConstants.getPubTemplateKeyWordsByIdUrl, params).getData());
                msgTemplateWechatPublic.setKeywords(keywords);
                msgTemplateWechatPublicDao.saveOrUpdate(msgTemplateWechatPublic);
            }
        }
    }

    public CategoryReceiveVO getCategory(String accessToken) {
        cn.hutool.http.HttpResponse response = HttpUtil.createGet(WechatTemplateConstants.getCategoryUrl + "?access_token="+accessToken)
                .contentType(ContentType.APPLICATION_JSON.toString()).timeout (3000).execute();
        String body = response.body();
        CategoryReceiveVO wechatGetKeyWordVO = JSONObject.parseObject(body, CategoryReceiveVO.class);
        return wechatGetKeyWordVO;
    }

    private PublicTemplateCommonVO getPubTemplateTitleList(PublicTemplateCommonDTO publicTemplateCommonDTO, String access_token){
        String json = JSON.toJSONString(publicTemplateCommonDTO);
        cn.hutool.http.HttpResponse response = HttpUtil.createGet(WechatTemplateConstants.getPubTemplateTitleListUrl + "?access_token="+access_token+"&ids="+publicTemplateCommonDTO.getIds()+"&start="+publicTemplateCommonDTO.getStart()+"&limit="+publicTemplateCommonDTO.getLimit())
                .contentType(ContentType.APPLICATION_JSON.toString()).timeout (3000).execute();
        String body = response.body();
        PublicTemplateCommonVO publicTemplateCommonVO = JSONObject.parseObject(body, PublicTemplateCommonVO.class);
        return publicTemplateCommonVO;
    }

    public TemplateCommonVO deleteTemplateById(DeleteTemplateDTO deleteTemplateDTO, String access_token){
        String json = JSON.toJSONString(deleteTemplateDTO);
        StringEntity stringEntity = new StringEntity(json, StandardCharsets.UTF_8);
        stringEntity.setContentType(ContentType.APPLICATION_JSON.toString());
        cn.hutool.http.HttpResponse response = HttpUtil.createPost(WechatTemplateConstants.deleteMessageTemplateUrl + "?access_token="+access_token)
                .contentType(ContentType.APPLICATION_JSON.toString())
                .body(json).timeout (3000).execute();
        String body = response.body();
        TemplateCommonVO publicTemplateCommonVO = JSONObject.parseObject(body, TemplateCommonVO.class);
        return publicTemplateCommonVO;
    }

    public WechatCreateTemplateVO createTemplateRetry(Long id, String accessToken){
        // 查询私人模板
        MsgTemplateWechat templateWechat = msgTemplateWechatDao.getById(id);
        MsgTemplateApplyLogQueryDTO msgTemplateApplyLogQueryDTO = new MsgTemplateApplyLogQueryDTO();
        msgTemplateApplyLogQueryDTO.setTemplateType(MsgTemplateWechatEnum.TemplateType.WECHAT.getValue());
        List<Long> idList = new ArrayList<>();
        idList.add(id);
        msgTemplateApplyLogQueryDTO.setIdList(idList);
        msgTemplateApplyLogQueryDTO.setSuccessFlag(MsgTemplateWechatEnum.SuccessFlag.FAIL.getValue());
        List<MsgTemplateApplyLog> condition = msgTemplateApplyLogDao.getListByCondition(msgTemplateApplyLogQueryDTO);
        if (CollectionUtils.isEmpty(condition)){
            throw new ParamsException("没有查询到相关失败记录，请联系管理员！");
        }
        // 取出最新的失败记录
        MsgTemplateApplyLog templateApplyLogNew = condition.get(0);

        // 请求微信接口
        MsgTemplateApplyLog msgTemplateApplyLog = new MsgTemplateApplyLog();
        WechatCreateTemplateVO wechatCreateTemplateVO = new WechatCreateTemplateVO();
        try {
            WechatCreateTemplateDTO wechatCreateTemplateDTO = JSONObject.parseObject(templateApplyLogNew.getReq(), WechatCreateTemplateDTO.class);
            wechatCreateTemplateVO = WeiXinConnectionUtil.postWechatByUrl(wechatCreateTemplateDTO, wechatCreateTemplateVO, accessToken , WechatTemplateConstants.addMessageTemplateUrl);

            msgTemplateApplyLog.setAppId(templateApplyLogNew.getAppId());
            msgTemplateApplyLog.setTemplateType(templateApplyLogNew.getTemplateType());
            msgTemplateApplyLog.setTenantId(templateApplyLogNew.getTenantId());
            templateWechat.setWechatTemplateId(wechatCreateTemplateVO.getPriTmplId());
            // 发送对象
            msgTemplateApplyLog.setReq(templateApplyLogNew.getReq());
            // 返回对象
            msgTemplateApplyLog.setResp(JSONObject.toJSONString(wechatCreateTemplateVO));
            if (WechatTemplateConstants.successCode.equals(wechatCreateTemplateVO.getErrCode())){
                templateWechat.setSuccessFlag(MsgSceneEnum.CreateStatus.SUCCESS.getValue());
                msgTemplateApplyLog.setSuccessFlag(MsgSceneEnum.CreateStatus.SUCCESS.getValue());
            }else {
                templateWechat.setSuccessFlag(MsgSceneEnum.CreateStatus.FAIL.getValue());
                msgTemplateApplyLog.setSuccessFlag(MsgSceneEnum.CreateStatus.FAIL.getValue());
                msgTemplateApplyLog.setReason(wechatCreateTemplateVO.getErrMsg());
            }
            msgTemplateWechatDao.updateById(templateWechat);
            msgTemplateApplyLog.setTemplateId(templateApplyLogNew.getTemplateId());
            msgTemplateApplyLogDao.saveOrUpdate(msgTemplateApplyLog);
            wechatCreateTemplateVO.setId(templateWechat.getId());
            wechatCreateTemplateVO.setTenantId(templateWechat.getTenantId());
            // 翻译错误码
            if (ObjectUtils.isNotEmpty(MsgTemplateWechatEnum.WechatErrorCode.getWechatErrorCode(wechatCreateTemplateVO.getErrCode()))){
                wechatCreateTemplateVO.setErrMsg(MsgTemplateWechatEnum.WechatErrorCode.getWechatErrorCode(wechatCreateTemplateVO.getErrCode()).getContent());
            }
            return wechatCreateTemplateVO;
        }catch (Exception e){
            log.error("小程序:"+templateApplyLogNew.getAppId()+"创建过程中发生错误：{}",e);
            throw new CallerException(502,e.getMessage());
        }
    }

    public void refreshApp(Long id, String accessToken){
        if (ObjectUtils.isEmpty(id)){
            throw new ParamsException("请输入正确的参数");
        }
        if (ObjectUtils.isEmpty(accessToken)){
            throw new ParamsException("请输入正确的参数");
        }
        MsgTemplateWechat msgTemplateWechat = msgTemplateWechatDao.getMsgTemplateWechatById(id);
        cn.hutool.http.HttpResponse response = HttpUtil.createGet(WechatTemplateConstants.getMessageTemplateListUrl + "?access_token="+accessToken )
                .contentType(ContentType.APPLICATION_JSON.toString()).timeout (3000).execute();
        String body = response.body();
        WechatGetPrivateTemplateVO wechatGetPrivateTemplateVO = JSONObject.parseObject(body, WechatGetPrivateTemplateVO.class);
        List<PrivateTemplateReceiveVO> privateTemplateVOList = wechatGetPrivateTemplateVO.getData();
        List<String> idList = privateTemplateVOList.stream().map(item -> item.getPriTmplId()).collect(Collectors.toList());
        if (idList.contains(msgTemplateWechat.getWechatTemplateId())){
            msgTemplateWechat.setAvailableStatus(MsgTemplateWechatEnum.AvailableStatus.ABLED.getValue());
        }else {
            msgTemplateWechat.setAvailableStatus(MsgTemplateWechatEnum.AvailableStatus.DISABLED.getValue());
        }
        // 刷新操作时间
        msgTemplateWechat.setUpdateTime(LocalDateTime.now());
        msgTemplateWechatDao.updateById(msgTemplateWechat);
    }

    /**
     * 通过tenantIdList获取已关联的tenantIdList
     * @param tenantIdList
     * @return
     */
    public List<Long> getEnableTenantIdListByTenantIdList(Long fanTaiTemplateId, List<Long> tenantIdList, Integer availableStatus){
        List<Long> enableTenantIdList = msgTemplateWechatDao.getEnableTenantIdListByTenantIdList(fanTaiTemplateId, tenantIdList, availableStatus);
        return enableTenantIdList;
    }

    /**
     * 通过模板id列表查询微信公共模板
     * @param idList
     * @return
     */
    public List<MsgPublicTemplateWechatListVO> getWechatPublicMsgTemplateByIdList(List<Long> idList){
        List<MsgTemplateWechatPublic> templateList = msgTemplateWechatPublicDao.getWechatPublicMsgTemplateByIdList(idList);
        List<MsgPublicTemplateWechatListVO> voList = templateList.stream().map(TemplateProviderConverter::msgTemplateWechatPublic2VO).collect(Collectors.toList());
        return voList;
    }
}
