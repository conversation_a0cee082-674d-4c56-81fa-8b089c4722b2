package com.cosfo.message.web.domain.service;

import cn.hutool.core.util.ObjectUtil;
import com.cosfo.message.web.domain.vo.MerchantStoreAccountVO;
import com.cosfo.message.web.domain.vo.MerchantStoreVO;
import com.cosfo.message.web.facade.UserCenterMerchantStoreAccountFacade;
import com.cosfo.message.web.facade.UserCenterMerchantStoreFacade;
import com.cosfo.message.web.facade.converter.UserCenterConverter;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: fansongsong
 * @Date: 2023-08-04
 * @Description:
 */
@Slf4j
@Component
public class UserCenterService {

    @Resource
    private UserCenterMerchantStoreFacade userCenterMerchantStoreFacade;
    @Resource
    private UserCenterMerchantStoreAccountFacade userCenterMerchantStoreAccountFacade;

    public List<MerchantStoreAccountVO> getStoreAccountIdsByStoreId(Long storeId, Integer type, Integer status){
        List<MerchantStoreAccountResultResp> list = userCenterMerchantStoreAccountFacade.getByStoreId(storeId);
        if(ObjectUtil.isNotNull(type)){
            list = list.stream().filter(e -> Objects.equals(type, e.getType())).collect(Collectors.toList());
        }
        if(ObjectUtil.isNotNull(status)){
            list = list.stream().filter(e -> Objects.equals(status,e.getStatus())).collect(Collectors.toList());
        }
        return list.stream().map(UserCenterConverter::merchantStoreAccountResultResp2VO).collect(Collectors.toList());
    }

    public List<MerchantStoreAccountVO> getStoreAccountIdsByTenantId(Long tenantId,Integer type,Integer status){
        List<MerchantStoreAccountResultResp> list = userCenterMerchantStoreAccountFacade.getByTenantId(tenantId);
        if(ObjectUtil.isNotNull(type)){
            list = list.stream().filter(e -> Objects.equals(type, e.getType())).collect(Collectors.toList());
        }
        if(ObjectUtil.isNotNull(status)){
            list = list.stream().filter(e -> Objects.equals(status,e.getStatus())).collect(Collectors.toList());
        }
        return list.stream().map(UserCenterConverter::merchantStoreAccountResultResp2VO).collect(Collectors.toList());
    }

    public List<MerchantStoreVO> getStoreByIds(List<Long> storeIds){
        List<MerchantStoreResultResp> list = userCenterMerchantStoreFacade.getByIds(storeIds);
        return list.stream().map(UserCenterConverter::merchantStoreResp2VO).collect(Collectors.toList());
    }
}
