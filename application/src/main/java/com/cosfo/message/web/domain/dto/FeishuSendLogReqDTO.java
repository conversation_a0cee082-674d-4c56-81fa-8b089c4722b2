package com.cosfo.message.web.domain.dto;

import com.cosfo.message.infrastructure.template.dto.FeishuSendMessageDTO;
import lombok.Data;
import net.xianmu.authentication.client.input.SystemOriginEnum;

/**
 * @Author: fansongsong
 * @Date: 2023-07-14
 * @Description:
 */
@Data
public class FeishuSendLogReqDTO {
    /**
     * 接收者，baseuserid
     */
    private Long baseUserId;
    /**
     * 接收者，bizuserid
     */
    private Long bizUserId;
    /**
     * 接收者，手机号码
     */
    private String phone;
    /**
     * systemOriginEnum {@link SystemOriginEnum}
     */
    private Integer systemOriginType;
    /**
     * 请求参数
     */
    private FeishuSendMessageDTO feishuSendMessageDTO;
}
