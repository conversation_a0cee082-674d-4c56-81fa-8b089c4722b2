package com.cosfo.message.web.domain.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.message.client.enums.SmsPlatformCodeEnum;
import com.cosfo.message.infrastructure.message.dto.MsgSendLogDTO;
import com.cosfo.message.infrastructure.message.dto.SmsDTO;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.vo.input.XmJobInput;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.aliyun.teautil.Common.toJSONString;

/**
 * 阿里云短信发送器
 * <AUTHOR>
 */
@Component
@Slf4j
public class AliSender implements SmsSendHandler {

    static final String accessKeyId = "LTAIfxUVIvEPOGUG";
    static final String accessKeySecret = "9rArRsylVzAF4oe8KF2ZwCsOhD1Gos";

    public static final String ACCESS_KEY_ID_KEY = "AliAccessKeyId";
    public static final String ACCESS_KEY_SECRET_KEY = "AliAccessKeySecret";
    public static final String SIGN_NAME_KEY = "AliSignName";
    public static final String TEMPLATE_CODE_KEY = "AliTemplateCode";
    public static final String FAN_TAI_SIGNATURE = "帆台";

    @Resource
    private SystemParametersMapper systemParametersMapper;

    @Resource
    private SmsSceneMapper smsSceneMapper;

    @Override
    public boolean sendSms(Sms sms) {
        try {
            String accessKeyId = getAccessKeyId();
            String accessKeySecret = getAccessKeySecret();
            String signName = getSignName();
            String templateCode = getTemplateCode();

            // 初始化请求客户端
            Client client = createClient(accessKeyId, accessKeySecret);

            // 构造API请求对象
            SendSmsRequest sendSmsRequest = new SendSmsRequest()
                    .setPhoneNumbers(sms.getPhone())
                    .setSignName(signName)
                    .setTemplateCode(templateCode)
                    .setTemplateParam(buildTemplateParam(sms.getArgs()));

            log.info("阿里云短信发送请求：phone:{}, signName:{}, templateCode:{}, templateParam:{}",
                    sms.getPhone(), signName, templateCode, buildTemplateParam(sms.getArgs()));

            // 获取响应对象
            SendSmsResponse sendSmsResponse = client.sendSms(sendSmsRequest);

            log.info("阿里云短信发送结果：phone:{}, response:{}", sms.getPhone(), toJSONString(sendSmsResponse));

            boolean success = "OK".equals(sendSmsResponse.getBody().getCode());
            if (!success) {
                log.warn("阿里云短信发送失败: phone:{}, code:{}, message:{}",
                        sms.getPhone(), sendSmsResponse.getBody().getCode(), sendSmsResponse.getBody().getMessage());
            }
            return success;
        } catch (Exception e) {
            log.error("阿里云短信发送异常: phone:{}, error:{}", sms.getPhone(), e.getMessage(), e);
            return false;
        }
    }

    public ProcessResult processResult(XmJobInput context) throws Exception {
        String phone= "15821741770";

        // 初始化请求客户端
        Client client = createClient ();
        if (StringUtils.isNotBlank(context.getInstanceParameters())) {
            String instanceParameters = context.getInstanceParameters ();
            phone = instanceParameters;
        }

        // 构造API请求对象，请替换请求参数值
        SendSmsRequest sendSmsRequest = new SendSmsRequest ()
                .setPhoneNumbers (phone)
                .setSignName ("帆台")
                .setTemplateCode ("SMS_228846374")
                .setTemplateParam ("{\"one\":\"test01\"}"); // TemplateParam为序列化后的JSON字符串。

        // 获取响应对象
        SendSmsResponse sendSmsResponse = client.sendSms (sendSmsRequest);

        // 响应包含服务端响应的 body 和 headers
        log.info (toJSONString (sendSmsResponse));
        return new ProcessResult(true);
    }


    public static Client createClient() throws Exception {
        Config config = new Config ()
                // 配置 AccessKey ID，请确保代码运行环境配置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。
                .setAccessKeyId (accessKeyId)
                // 配置 AccessKey Secret，请确保代码运行环境配置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
                .setAccessKeySecret (accessKeySecret);

        // 配置 Endpoint。中国站使用dysmsapi.aliyuncs.com
        config.endpoint = "dysmsapi.aliyuncs.com";

        return new Client (config);
    }

    public static Client createClient(String accessKeyId, String accessKeySecret) throws Exception {
        Config config = new Config()
                .setAccessKeyId(accessKeyId)
                .setAccessKeySecret(accessKeySecret);

        // 配置 Endpoint。中国站使用dysmsapi.aliyuncs.com
        config.endpoint = "dysmsapi.aliyuncs.com";

        return new Client(config);
    }

    /**
     * 获取AccessKeyId
     */
    private String getAccessKeyId() {
        try {
            SystemParameters parameters = systemParametersMapper.selectByKey(ACCESS_KEY_ID_KEY);
            return parameters != null ? parameters.getParamValue() : accessKeyId;
        } catch (Exception e) {
            log.warn("获取阿里云AccessKeyId失败，使用默认值: {}", e.getMessage());
            return accessKeyId;
        }
    }

    /**
     * 获取AccessKeySecret
     */
    private String getAccessKeySecret() {
        try {
            SystemParameters parameters = systemParametersMapper.selectByKey(ACCESS_KEY_SECRET_KEY);
            return parameters != null ? parameters.getParamValue() : accessKeySecret;
        } catch (Exception e) {
            log.warn("获取阿里云AccessKeySecret失败，使用默认值: {}", e.getMessage());
            return accessKeySecret;
        }
    }

    /**
     * 获取签名名称
     */
    private String getSignName() {
        try {
            SystemParameters parameters = systemParametersMapper.selectByKey(SIGN_NAME_KEY);
            return parameters != null ? parameters.getParamValue() : FAN_TAI_SIGNATURE;
        } catch (Exception e) {
            log.warn("获取阿里云签名名称失败，使用默认值: {}", e.getMessage());
            return FAN_TAI_SIGNATURE;
        }
    }

    /**
     * 获取模板代码
     */
    private String getTemplateCode() {
        try {
            SystemParameters parameters = systemParametersMapper.selectByKey(TEMPLATE_CODE_KEY);
            return parameters != null ? parameters.getParamValue() : "SMS_228846374";
        } catch (Exception e) {
            log.warn("获取阿里云模板代码失败，使用默认值: {}", e.getMessage());
            return "SMS_228846374";
        }
    }

    /**
     * 构建模板参数
     */
    private String buildTemplateParam(List<String> args) {
        if (args == null || args.isEmpty()) {
            return "{\"one\":\"test01\"}";
        }

        JSONObject templateParam = new JSONObject();
        for (int i = 0; i < args.size(); i++) {
            templateParam.put("param" + (i + 1), args.get(i));
        }
        return templateParam.toString();
    }

    /**
     * 渲染短信模板
     */
    public String renderTemp(Long sceneId, List<String> args) {
        try {
            SmsScene smsScene = smsSceneMapper.selectByScenePlatform(sceneId, SenderPlatformEnum.ALI_SMS.ordinal());
            if (smsScene == null) {
                log.warn("未找到阿里云短信场景模板: sceneId={}", sceneId);
                return "";
            }

            String template = smsScene.getTemplate();
            if (args != null) {
                for (String arg : args) {
                    template = template.replaceFirst("\\{s\\}", arg);
                }
            }
            return template;
        } catch (Exception e) {
            log.error("渲染阿里云短信模板失败: sceneId={}, error={}", sceneId, e.getMessage(), e);
            return "";
        }
    }

    @Override
    public SmsPlatformCodeEnum getSmsThirdCompanyCode() {
        return SmsPlatformCodeEnum.ALi;
    }

    @Override
    public void send(SmsDTO dto) {

    }

    @Override
    public void buildSmsContent(String template, List<String> args, SmsDTO dto) {

    }
}
