package com.cosfo.message.web.domain.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import static com.aliyun.teautil.Common.toJSONString;

@Component
@Slf4j
public class AliSender {

    static final String accessKeyId = "LTAIfxUVIvEPOGUG";
    static final String accessKeySecret = "9rArRsylVzAF4oe8KF2ZwCsOhD1Gos";
    public ProcessResult processResult(XmJobInput context) throws Exception {
        String phone= "15821741770";

        // 初始化请求客户端
        Client client = createClient ();
        if (StringUtils.isNotBlank(context.getInstanceParameters())) {
            String instanceParameters = context.getInstanceParameters ();
            phone = instanceParameters;
        }

        // 构造API请求对象，请替换请求参数值
        SendSmsRequest sendSmsRequest = new SendSmsRequest ()
                .setPhoneNumbers (phone)
                .setSignName ("帆台")
                .setTemplateCode ("SMS_228846374")
                .setTemplateParam ("{\"one\":\"test01\"}"); // TemplateParam为序列化后的JSON字符串。

        // 获取响应对象
        SendSmsResponse sendSmsResponse = client.sendSms (sendSmsRequest);

        // 响应包含服务端响应的 body 和 headers
        log.info (toJSONString (sendSmsResponse));
        return new ProcessResult(true);
    }


    public static Client createClient() throws Exception {
        Config config = new Config ()
                // 配置 AccessKey ID，请确保代码运行环境配置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。
                .setAccessKeyId (accessKeyId)
                // 配置 AccessKey Secret，请确保代码运行环境配置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
                .setAccessKeySecret (accessKeySecret);

        // 配置 Endpoint。中国站使用dysmsapi.aliyuncs.com
        config.endpoint = "dysmsapi.aliyuncs.com";

        return new Client (config);
    }
}
