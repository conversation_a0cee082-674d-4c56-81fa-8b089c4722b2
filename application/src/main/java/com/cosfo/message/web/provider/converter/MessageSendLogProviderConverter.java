package com.cosfo.message.web.provider.converter;

import com.alibaba.fastjson.JSON;
import com.cosfo.message.client.enums.ChannelTypeEnum;
import com.cosfo.message.client.req.MessageBodyReq;
import com.cosfo.message.client.req.MessageUserReq;
import com.cosfo.message.client.resp.MsgSendLogResp;
import com.cosfo.message.common.util.FeishuUtils;
import com.cosfo.message.infrastructure.template.dto.BatchMessageSendDTO;
import com.cosfo.message.infrastructure.template.dto.BatchMessageSendThirdUidDTO;
import com.cosfo.message.infrastructure.template.dto.MessageBodyDTO;
import com.cosfo.message.infrastructure.template.dto.MessageUserDTO;
import com.cosfo.message.web.domain.dto.FeishuSendLogReqDTO;
import com.cosfo.message.web.domain.vo.FeishuSendMessageVO;
import com.cosfo.message.web.domain.vo.MsgSendLogVO;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: fansongsong
 * @Date: 2023-07-14
 * @Description:
 */
@Slf4j
public class MessageSendLogProviderConverter {

    public static MsgSendLogResp msgSendLogVOListVO2Resp(MsgSendLogVO msgSendLogVO) {
        MsgSendLogResp msgSendLogResp = new MsgSendLogResp();
        msgSendLogResp.setId(msgSendLogVO.getId());
        msgSendLogResp.setReceiveThirdUid(msgSendLogVO.getReceiveThridUid());
        String req = msgSendLogVO.getReq();
        String resp = msgSendLogVO.getResp();
        try {
            FeishuSendLogReqDTO feishuSendLogReqDTO = JSON.parseObject(req, FeishuSendLogReqDTO.class);
            msgSendLogResp.setBaseUserId(Optional.ofNullable(feishuSendLogReqDTO).map(FeishuSendLogReqDTO::getBaseUserId).orElse(null));
            msgSendLogResp.setBizUserId(Optional.ofNullable(feishuSendLogReqDTO).map(FeishuSendLogReqDTO::getBizUserId).orElse(null));
            msgSendLogResp.setPhone(Optional.ofNullable(feishuSendLogReqDTO).map(FeishuSendLogReqDTO::getPhone).orElse(null));
            FeishuSendMessageVO feishuSendMessageVO = JSON.parseObject(resp, FeishuSendMessageVO.class);
            // 失败错误码，填充失败原因
            if (!FeishuUtils.SUCCESS_CODE.equals(feishuSendMessageVO.getCode())) {
                msgSendLogResp.setReason(Optional.ofNullable(feishuSendMessageVO).map(FeishuSendMessageVO::getMsg).orElse(null));
            }
        } catch (Exception e) {
            log.info("飞书消息记录数据转换异常 req:{},resp:{}", req, resp, e);
        }
        msgSendLogResp.setSendStatus(msgSendLogVO.getSendStatus());
        return msgSendLogResp;
    }

    public static BatchMessageSendDTO msgReadLogReq2BatchDTO(Long tenantId, ChannelTypeEnum channelTypeEnum, MessageUserReq sender, List<MessageUserReq> receiverList, MessageBodyReq mesg) {
        BatchMessageSendDTO messageSendDTO = new BatchMessageSendDTO();
        messageSendDTO.setTenantId(tenantId);
        if (Objects.nonNull(channelTypeEnum)) {
            messageSendDTO.setChannleType(channelTypeEnum.getValue());
        }
        messageSendDTO.setSender(messageUserReq2DTO(sender));
        List<MessageUserDTO> list = receiverList.stream().map(MessageSendLogProviderConverter::messageUserReq2DTO).collect(Collectors.toList());
        messageSendDTO.setReceiverList(list);
        messageSendDTO.setMesg(messageBodyReq2DTO(mesg));
        return messageSendDTO;
    }


    public static BatchMessageSendDTO msgReadLogReq2BatchDTOV2(Long tenantId, ChannelTypeEnum channelTypeEnum, MessageUserReq sender, List<MessageUserReq> receiverList, MessageBodyReq mesg, String channelCode) {
        BatchMessageSendDTO messageSendDTO = MessageSendLogProviderConverter.msgReadLogReq2BatchDTO(tenantId, channelTypeEnum, sender, receiverList, mesg);
        messageSendDTO.setChannelCode(channelCode);
        return messageSendDTO;
    }

    private static MessageUserDTO messageUserReq2DTO(MessageUserReq sender) {
        if(Objects.isNull(sender)){
            return null;
        }
        MessageUserDTO messageUserDTO = new MessageUserDTO();
        messageUserDTO.setBaseUserId(sender.getBaseUserId());
        messageUserDTO.setBizUserId(sender.getBizUserId());
        messageUserDTO.setPhone(sender.getPhone());
        if (Objects.nonNull(sender.getSystemOriginEnum())) {
            messageUserDTO.setSystemOriginType(sender.getSystemOriginEnum().getType());
        }
        return messageUserDTO;
    }

    private static MessageBodyDTO messageBodyReq2DTO(MessageBodyReq messageBodyReq) {
        MessageBodyDTO messageBodyDTO = new MessageBodyDTO();
        messageBodyDTO.setContentType(messageBodyReq.getContentType());
        messageBodyDTO.setData(messageBodyReq.getData());
        messageBodyDTO.setTitle(messageBodyReq.getTitle());
        messageBodyDTO.setMsgBodyType(messageBodyReq.getMsgBodyType());
        messageBodyDTO.setTemplateId(messageBodyReq.getTemplateId());
        messageBodyDTO.setTemplateCode(messageBodyReq.getTemplateCode());
        messageBodyDTO.setJumpUrl(messageBodyReq.getJumpUrl());
        messageBodyDTO.setClientMsgId(messageBodyReq.getClientMsgId());
        Optional.ofNullable(messageBodyReq.getJumpUrlTypeEnum()).ifPresent(jumpUrlTypeEnum -> messageBodyDTO.setJumpUrlType(jumpUrlTypeEnum.getType()));
        return messageBodyDTO;
    }

    public static BatchMessageSendThirdUidDTO msgReadLogReq2BatchUidDTO(Long tenantId, ChannelTypeEnum channelTypeEnum, String sender, List<String> receiverList, MessageBodyReq mesg, SystemOriginEnum systemOriginEnum) {
        BatchMessageSendThirdUidDTO messageSendDTO = new BatchMessageSendThirdUidDTO();
        messageSendDTO.setTenantId(tenantId);
        if (Objects.nonNull(channelTypeEnum)) {
            messageSendDTO.setChannleType(channelTypeEnum.getValue());
        }
        messageSendDTO.setSenderUid(sender);
        messageSendDTO.setReceiverUidList(receiverList);
        messageSendDTO.setMesg(messageBodyReq2DTO(mesg));
        if (Objects.nonNull(systemOriginEnum)) {
            messageSendDTO.setSystemOriginType(systemOriginEnum.getType());
        }
        return messageSendDTO;
    }

    public static BatchMessageSendThirdUidDTO msgReadLogReq2BatchUidDTOV2(Long tenantId, ChannelTypeEnum channelTypeEnum, String sender, List<String> receiverList, MessageBodyReq mesg, SystemOriginEnum systemOriginEnum, String channelCode) {
        BatchMessageSendThirdUidDTO messageSendDTO = MessageSendLogProviderConverter.msgReadLogReq2BatchUidDTO(tenantId, channelTypeEnum, sender, receiverList, mesg, systemOriginEnum);
        messageSendDTO.setChannelCode(channelCode);
        return messageSendDTO;
    }
}
