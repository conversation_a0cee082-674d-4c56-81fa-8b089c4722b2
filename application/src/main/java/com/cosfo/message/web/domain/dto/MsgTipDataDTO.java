package com.cosfo.message.web.domain.dto;

import com.cosfo.message.infrastructure.message.dto.NotifyTipBodyDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-07-21
 * @Description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class MsgTipDataDTO {

    /**
     * 副标题
     */
    private String subTitle;

    /**
     * 输入的详细json
     */
    private List<NotifyTipBodyDTO> inputData;
}
