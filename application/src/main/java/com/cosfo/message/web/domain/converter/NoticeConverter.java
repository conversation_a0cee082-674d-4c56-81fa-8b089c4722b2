package com.cosfo.message.web.domain.converter;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cosfo.message.common.enums.MsgNoticeEnum;
import com.cosfo.message.common.enums.MsgNoticeReceiverMappingEnum;
import com.cosfo.message.infrastructure.message.model.MsgSendLog;
import com.cosfo.message.infrastructure.notice.dto.MsgNoticeEditDTO;
import com.cosfo.message.infrastructure.notice.dto.MsgReadLogDTO;
import com.cosfo.message.infrastructure.notice.model.MsgNotice;
import com.cosfo.message.infrastructure.notice.model.MsgNoticeReceiverMapping;
import com.cosfo.message.infrastructure.notice.model.MsgReadLog;
import com.cosfo.message.web.domain.service.NoticeContentUtil;
import com.cosfo.message.web.domain.vo.*;

import java.util.*;
import java.util.stream.Collectors;

public class NoticeConverter {

    public static MsgReadLog msgReadLogDTO2Entity(MsgReadLogDTO dto) {
        MsgReadLog entity = new MsgReadLog();
        entity.setContentId(dto.getNoticeId());
        entity.setTenantId(dto.getTenantId());
        entity.setUId(dto.getUId());
        entity.setRoleType(dto.getRoleType());
        entity.setUName(dto.getUName());
        entity.setStoreId(dto.getStoreId());
        entity.setPhone(dto.getPhone());
        entity.setActionType(dto.getActionType());
        return entity;
    }

    public static NoticeDetailVO buildNoticeDetailVO(MsgNotice notice, Long beforeNoticId, Long afterNoticId, Integer supportAmount, Integer readAmoun,List<Long> receiverIds,boolean supportStatus) {
        NoticeDetailVO vo = new NoticeDetailVO();
        vo.setId(notice.getId());
        vo.setTitle(notice.getTitle());
        vo.setContent(notice.getContent());
        vo.setContentType(notice.getType());
        vo.setPushTime(notice.getPushTime());
        vo.setSupportSwitch(notice.getSupportSwitch());
        vo.setBeforeId(beforeNoticId);
        vo.setAfterId(afterNoticId);
        vo.setReadAmount(readAmoun);
        vo.setSupportAmount(supportAmount);
        vo.setReceiverIds(receiverIds);
        vo.setPushType(notice.getPushType());
        vo.setSupportStatus(supportStatus?1:0);
        return vo;
    }

    public static MsgSendLogVO buildMsgSendLogVO(MsgSendLog log) {
        MsgSendLogVO vo = new MsgSendLogVO();
        vo.setId(log.getId());
        vo.setTenantId(log.getTenantId());
        vo.setTemplateId(log.getTemplateId());
        vo.setChannleType(log.getChannleType());
        vo.setContentType(log.getContentType());
        vo.setPageId(log.getPageId());
        vo.setPage(log.getPage());
        vo.setTitle(log.getTitle());
        vo.setThirdTemplateId(log.getThirdTemplateId());
        vo.setReceiveAccountId(log.getReceiveAccountId());
        vo.setReceiveThridUid(log.getReceiveThridUid());
        vo.setSendAccountId(log.getSendAccountId());
        vo.setSendThridUid(log.getSendThridUid());
        vo.setData(log.getData());
        vo.setSendStatus(log.getSendStatus());
        vo.setTemplateType(log.getTemplateType());
        vo.setStoreId(log.getStoreId());
        vo.setReadStatus(log.getReadStatus());
        vo.setReadTime(log.getUpdateTime());
        vo.setCreateTime(log.getCreateTime());
        vo.setResp(log.getResp());
        vo.setReq(log.getReq());
        return vo;
    }

    public static NoticList4ReceiverVO buildNoticList4ReceiverVO(MsgSendLogVO logVO, Map<Long, Integer> noticeReadMap) {
        NoticList4ReceiverVO vo = new NoticList4ReceiverVO();
        vo.setPushTime(logVO.getCreateTime());
        vo.setTitle(logVO.getTitle());
        vo.setContent(logVO.getData());
        vo.setContentType(logVO.getContentType());
        vo.setId(logVO.getPageId());
        vo.setReadStatus(noticeReadMap.get(logVO.getPageId()));
        return vo;
    }
    public static NoticeList4ManagerVO buildNoticList4ManagerVO(MsgNotice notice, Map<Long, Long> supportAmountMap, Map<Long, Long> readAmounMap, Map<Long, List<MsgNoticeReceiverMapping>> storeAmountMap) {
        NoticeList4ManagerVO vo = new NoticeList4ManagerVO();
        vo.setId(notice.getId());
        vo.setPushTime(notice.getPushTime());
        vo.setTitle(notice.getTitle());
        vo.setContent(notice.getContent());
        vo.setContentType(notice.getType());
        List<MsgNoticeReceiverMapping> msgNoticeReceiverMappings = storeAmountMap.get(notice.getId());
        if(ObjectUtil.isNull(msgNoticeReceiverMappings)){
            vo.setReceiveStore("未配置");
        }else{
            vo.setReceiveStore("指定门店（"+msgNoticeReceiverMappings.size()+"）");
            List<Long> storeIds = msgNoticeReceiverMappings.stream().filter(e -> Objects.equals(MsgNoticeReceiverMappingEnum.Type.STORE.getValue(), e.getType())).map(MsgNoticeReceiverMapping::getReceiverId).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(storeIds) && storeIds.contains(0L)){
                vo.setReceiveStore("所有门店("+(msgNoticeReceiverMappings.size()-1) +"）");
            }
        }
        vo.setCreateUid(notice.getCreateUid());
        vo.setEditUid(notice.getEditUid());
        vo.setPushUid(notice.getPushUid());
        vo.setPushType(notice.getPushType());
        vo.setReadAmount(ObjectUtil.isEmpty(readAmounMap.get(notice.getId()))?0:readAmounMap.get(notice.getId()).intValue());
        vo.setSupportAmount(ObjectUtil.isEmpty(supportAmountMap.get(notice.getId()))?0:supportAmountMap.get(notice.getId()).intValue());
        vo.setUpdateTime(notice.getUpdateTime());
        return vo;
    }

    public static MsgNotice msgNoticeDTO2Entity(MsgNoticeEditDTO dto) {
        MsgNotice notice = new MsgNotice();
        notice.setId(dto.getId());
        notice.setType(dto.getType());
        notice.setTitle(dto.getTitle());
        notice.setContent(dto.getContent());
        notice.setTenantId(dto.getTenantId());
        notice.setPushTime(dto.getPushTime());
        notice.setEditUid(dto.getUId());
        if(ObjectUtil.isNull(dto.getId())) {
            notice.setCreateUid(dto.getUId());
        }
        if(ObjectUtil.isNotNull(dto.getPushStatus()) && MsgNoticeEnum.PushStatus.READY.getValue().equals(dto.getPushStatus())) {
            notice.setPushUid(dto.getUId());
        }
        notice.setPushType(dto.getPushType());
        notice.setPushStatus(dto.getPushStatus());
        notice.setSupportSwitch(dto.getSupportSwitch());
        return notice;
    }

    public static MsgNoticeReceiverMapping msgNoticeReceiverMapping2Entity(Long id,Integer receiverType,Long noticeId) {
        MsgNoticeReceiverMapping entity = new MsgNoticeReceiverMapping();
        entity.setType(receiverType);
        entity.setNoticeId(noticeId);
        entity.setReceiverId(id);
        return entity;
    }

    public static MsgNoticeReceiverVO buildMsgNoticeReceiverVO(MsgNoticeReceiverMapping entity, List<MsgReadLog> readLogs,List<MsgReadLog> supportLogs) {
        MsgNoticeReceiverVO vo = new MsgNoticeReceiverVO();
        vo.setId(entity.getReceiverId());
        if(CollectionUtil.isEmpty(readLogs)){
            vo.setReadAmount(0);
        }else {
            Optional<MsgReadLog> first = readLogs.stream().sorted(Comparator.comparing(MsgReadLog::getId)).findFirst();
            vo.setFirstReadTime(first.isPresent()? first.get().getCreateTime() : null);
            vo.setReadAmount(readLogs.size());
        }
        if(CollectionUtil.isEmpty(supportLogs)){
            vo.setSupportAmount(0);
        }else {
            vo.setSupportAmount(supportLogs.size());
        }
        return vo;
    }

    public static NoticeReadLogVO buildMsgReadLog(MsgReadLog e) {
        NoticeReadLogVO vo = new NoticeReadLogVO();
        vo.setTenantId(e.getTenantId());
        vo.setUId(e.getUId());
        vo.setRoleType(e.getRoleType());
        vo.setUName(e.getUName());
        vo.setPhone(e.getPhone());
        vo.setActionType(e.getActionType());
        vo.setTime(e.getUpdateTime());
        return vo;
    }
}
