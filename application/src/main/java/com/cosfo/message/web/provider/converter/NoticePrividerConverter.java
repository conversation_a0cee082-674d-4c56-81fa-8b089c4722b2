package com.cosfo.message.web.provider.converter;

import com.cosfo.message.client.req.*;
import com.cosfo.message.client.resp.*;
import com.cosfo.message.infrastructure.notice.dto.*;
import com.cosfo.message.web.domain.vo.*;

import java.time.LocalDateTime;

public class NoticePrividerConverter {

    public static MsgReadLogDTO msgReadLogReq2DTO(MsgReadLogReq req) {
        MsgReadLogDTO dto = new MsgReadLogDTO();
        dto.setNoticeId(req.getNoticeId());
        dto.setTenantId(req.getTenantId());
        dto.setUId(req.getUId());
        dto.setRoleType(req.getRoleType());
        dto.setUName(req.getUName());
        dto.setStoreId(req.getStoreId());
        dto.setPhone(req.getPhone());
        dto.setActionType(req.getActionType());
        return dto;
    }


    public static NoticeDetailResultResp noticDetailVO2Resp(NoticeDetailVO vo) {
        NoticeDetailResultResp resp = new NoticeDetailResultResp();
        resp.setId(vo.getId());
        resp.setBeforeId(vo.getBeforeId());
        resp.setAfterId(vo.getAfterId());
        resp.setTitle(vo.getTitle());
        resp.setContent(vo.getContent());
        resp.setContentType(vo.getContentType());
        resp.setPushTime(vo.getPushTime());
        resp.setSupportSwitch(vo.getSupportSwitch());
        resp.setReadAmount(vo.getReadAmount());
        resp.setSupportAmount(vo.getSupportAmount());
        resp.setReceiverIds(vo.getReceiverIds());
        resp.setPushType(vo.getPushType());
        resp.setSupportStatus(vo.getSupportStatus());
        return resp;
    }

    public static NoticeQuery4ReceiverDTO noticeQuery4ReceiverReq2DTO(NoticeQuery4ReceiverReq req) {
        NoticeQuery4ReceiverDTO noticQueryDTO = new NoticeQuery4ReceiverDTO();
        noticQueryDTO.setRecevieUid(req.getRecevieUid());
        noticQueryDTO.setReadStatus(req.getReadStatus());
        noticQueryDTO.setIds(req.getIds());
        noticQueryDTO.setTenantId(req.getTenantId());
        return noticQueryDTO;
    }
    public static NoticeList4ReceiverResultResp noticList4ReceiverVO2Resp(NoticList4ReceiverVO vo) {
        NoticeList4ReceiverResultResp resp = new NoticeList4ReceiverResultResp();
        resp.setReadStatus(vo.getReadStatus());
        resp.setId(vo.getId());
        resp.setPushTime(vo.getPushTime());
        resp.setTitle(vo.getTitle());
        resp.setContent(vo.getContent());
        resp.setContentType(vo.getContentType());
        return resp;
    }

    public static NoticeQuery4ManagerDTO noticeQuery4ManagerDTO2DTO(NoticeQuery4ManagerReq req) {
        NoticeQuery4ManagerDTO noticQueryDTO = new NoticeQuery4ManagerDTO();
        noticQueryDTO.setSortKey(req.getSortKey());
        noticQueryDTO.setSortBy(req.getSortBy());
        noticQueryDTO.setTenantId(req.getTenantId());
        noticQueryDTO.setId(req.getId());
        noticQueryDTO.setTitle(req.getTitle());
        noticQueryDTO.setPushTimeBegin(req.getPushTimeBegin());
        noticQueryDTO.setPushTimeEnd(req.getPushTimeEnd());
        noticQueryDTO.setPushStatus(req.getPushStatus());
        noticQueryDTO.setTenantId(req.getTenantId());
        return noticQueryDTO;
    }

    public static NoticeList4ManagerResultResp noticList4ManagerVO2Resp(NoticeList4ManagerVO vo) {
        NoticeList4ManagerResultResp resp = new NoticeList4ManagerResultResp();
        resp.setReceiveStore(vo.getReceiveStore());
        resp.setCreateUid(vo.getCreateUid());
        resp.setEditUid(vo.getEditUid());
        resp.setPushUid(vo.getPushUid());
        resp.setPushType(vo.getPushType());
        resp.setReadAmount(vo.getReadAmount());
        resp.setSupportAmount(vo.getSupportAmount());
        resp.setUpdateTime(vo.getUpdateTime());
        resp.setId(vo.getId());
        resp.setPushTime(vo.getPushTime());
        resp.setTitle(vo.getTitle());
        resp.setContent(vo.getContent());
        resp.setContentType(vo.getContentType());
        return resp;
    }

    public static MsgNoticeEditDTO msgNoticReq2DTO(MsgNoticeEditReq req) {
        MsgNoticeEditDTO editDTO = new MsgNoticeEditDTO();
        editDTO.setId(req.getId());
        editDTO.setTitle(req.getTitle());
        editDTO.setContent(req.getContent());
        editDTO.setPushTime(req.getPushTime());
        editDTO.setStoreIds(req.getStoreIds());
        editDTO.setPushType(req.getPushType());
        editDTO.setSupportSwitch(req.getSupportSwitch());
        editDTO.setPushStatus(req.getPushStatus());
        editDTO.setUId(req.getUId());
        editDTO.setTenantId(req.getTenantId());
        editDTO.setReceiverType(req.getReceiverType());
        return editDTO;
    }

    public static MsgNoticeReceiverQueryDTO msgNoticeReceiverQueryReq2DTO(MsgNoticeReceiverQueryReq req) {
        MsgNoticeReceiverQueryDTO dto = new MsgNoticeReceiverQueryDTO();
        dto.setReceiverType(req.getReceiverType());
        dto.setReceiverIds(req.getReceiverIds());
        dto.setNoticeId(req.getNoticeId());
        dto.setSupportFlag(req.getSupportFlag());
        dto.setReadFlag(req.getReadFlag());
        return dto;
    }

    public static MsgNoticeReceiverResp msgNoticeReceiverVO2Resp(MsgNoticeReceiverVO vo) {
        MsgNoticeReceiverResp resp = new MsgNoticeReceiverResp();
        resp.setId(vo.getId());
        resp.setReadAmount(vo.getReadAmount());
        resp.setSupportAmount(vo.getSupportAmount());
        resp.setFirstReadTime(vo.getFirstReadTime());
        return resp;
    }

    public static NoticeReadLogResultResp noticeReadLogVO2Resp(NoticeReadLogVO vo) {
        NoticeReadLogResultResp resp = new NoticeReadLogResultResp();
        resp.setTenantId(vo.getTenantId());
        resp.setUId(vo.getUId());
        resp.setRoleType(vo.getRoleType());
        resp.setUName(vo.getUName());
        resp.setPhone(vo.getPhone());
        resp.setActionType(vo.getActionType());
        resp.setTime(vo.getTime());
        return resp;
    }

    public static ReadOrSupportLogQueryDTO readOrSupportLogQueryReq2DTO(ReadOrSupportLogQueryReq req) {
        ReadOrSupportLogQueryDTO dto = new ReadOrSupportLogQueryDTO();
        dto.setStoreId(req.getStoreId());
        dto.setNoticeId(req.getNoticeId());
        dto.setActionType(req.getActionType());
        dto.setTenantId(req.getTenantId());
        return dto;
    }
}
