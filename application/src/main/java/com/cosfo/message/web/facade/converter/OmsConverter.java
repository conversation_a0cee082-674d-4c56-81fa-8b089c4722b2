package com.cosfo.message.web.facade.converter;

import cn.hutool.core.collection.CollectionUtil;
import com.cosfo.message.web.domain.vo.MerchantStoreAccountVO;
import com.cosfo.message.web.domain.vo.MerchantStoreVO;
import com.cosfo.message.web.domain.vo.MsgSceneTenantVO;
import com.cosfo.message.web.domain.vo.MsgSceneVO;
import com.cosfo.oms.client.resp.MerchantStoreAccountResp;
import com.cosfo.oms.client.resp.MerchantStoreResp;
import com.cosfo.oms.client.resp.MsgSceneResultResp;
import com.cosfo.oms.client.resp.MsgSceneTenantResultResp;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class OmsConverter {
    public static MerchantStoreAccountVO merchantStoreAccountResp2VO(MerchantStoreAccountResp e) {
        MerchantStoreAccountVO vo = new MerchantStoreAccountVO();
        vo.setId(e.getId());
        vo.setTenantId(e.getTenantId());
        vo.setStoreId(e.getStoreId());
        vo.setAccountName(e.getAccountName());
        vo.setPhone(e.getPhone());
        vo.setType(e.getType());
        vo.setRegisterTime(e.getRegisterTime());
        vo.setAuditTime(e.getAuditTime());
        vo.setOpenId(e.getOpenId());
        vo.setUnionId(e.getUnionId());
        vo.setStatus(e.getStatus());
        return vo;
    }

    public static MerchantStoreVO merchantStoreResp2VO(MerchantStoreResp e) {
        MerchantStoreVO vo = new MerchantStoreVO();
        vo.setId(e.getId());
        vo.setTenantId(e.getTenantId());
        vo.setStoreName(e.getStoreName());
        vo.setType(e.getType());
        vo.setRegisterTime(e.getRegisterTime());
        vo.setStatus(e.getStatus());
        vo.setAuditRemark(e.getAuditRemark());
        vo.setAuditTime(e.getAuditTime());
        vo.setBillSwitch(e.getBillSwitch());
        vo.setOnlinePayment(e.getOnlinePayment());
        return vo;
    }

    public static MsgSceneVO msgSceneResp2VO(MsgSceneResultResp resp) {
        MsgSceneVO vo = new MsgSceneVO();
        vo.setId(resp.getId());
        vo.setTemplateId(resp.getTemplateId());
        vo.setTemplateType(resp.getTemplateType());
        vo.setTemplateName(resp.getTemplateName());
        vo.setKeywords(resp.getKeywords());
        vo.setSceneName(resp.getSceneName());
        vo.setDescription(resp.getDescription());
        vo.setSceneStatus(resp.getSceneStatus());
        return vo;
    }

    public static List<MsgSceneTenantVO> msgSceneTenantResp2VO(List<MsgSceneTenantResultResp> data) {
        if(CollectionUtil.isEmpty(data)){
            return Collections.emptyList();
        }
       return data.stream().map(e->{
           MsgSceneTenantVO vo = new MsgSceneTenantVO();
           vo.setTemplateId(e.getTemplateId());
           vo.setThirdTemplateId(e.getThirdTemplateId());
           vo.setTemplateType(e.getTemplateType());
           vo.setTenantId(e.getTenantId());
           vo.setAvailableStatus(e.getAvailableStatus());
           vo.setSceneId(e.getSceneId());
           return vo;
       }).collect(Collectors.toList());
    }
}
