package com.cosfo.message.starter.config;

import com.alibaba.nacos.api.config.annotation.NacosConfigListener;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

/**
 * @Author: fansongsong
 * @Date: 2024-01-12
 * @Description: 订单根据门店分组配置进行拦截
 */
@Slf4j
@Configuration
@Data
public class WeChatOaMessageConfig {

    @Value("${spring.application.name:not-set}")
    private String applicationName;

    @NacosValue(value = "${we-chat.oa.message.error.codes:43101,40003,40037,43004}", autoRefreshed = true)
    private Set<String> weChatOaMessageErrorCodes;

    @NacosConfigListener(dataId = "${spring.application.name}")
    public void onConfigChanged(String newConfig) {
        log.info("Nacos配置已更新:{}", newConfig);
    }
}
