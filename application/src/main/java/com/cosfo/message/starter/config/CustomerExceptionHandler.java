package com.cosfo.message.starter.config;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.*;
import net.xianmu.common.exception.error.code.ProviderErrorCode;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.dubbo.support.constant.DubboCommonConstant;
import net.xianmu.dubbo.support.util.AspectUtils;
import net.xianmu.i18n.result.util.I18nCommonResultUtil;
import net.xianmu.i18n.result.util.I18nDubboResponseUtil;
import org.apache.catalina.connector.ClientAbortException;
import org.aspectj.lang.ProceedingJoinPoint;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.ValidationException;
import java.util.Set;

@Component
@Slf4j
@ControllerAdvice
public class CustomerExceptionHandler implements net.xianmu.dubbo.support.handle.ExceptionHandler {

    @ExceptionHandler(ClientAbortException.class)
    @ResponseBody
    public CommonResult clientAbortExceptionHandler(ClientAbortException e) {
        log.info("【警告】message=[{}]", e.getMessage(), e);
        CommonResult<Object> fail = I18nCommonResultUtil.fail(ResultStatusEnum.SERVER_ERROR);
        return fail;
    }

    @ExceptionHandler(ParamsException.class)
    @ResponseBody
    public CommonResult paramsExceptionHandler(ParamsException e) {
        log.warn("参数错误:{}", e.getMessage(), e);
        CommonResult<Object> fail = I18nCommonResultUtil.fail(ResultStatusEnum.SERVER_ERROR, e.getMessage(), e.getErrorCode().getMessage());
        return fail;
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public CommonResult methodArgumentNotValidExceptionHandler(MethodArgumentNotValidException e) {
        String message = e.getBindingResult().getAllErrors().stream()
                .map(DefaultMessageSourceResolvable::getDefaultMessage).findFirst().orElse("参数异常");
        log.warn("参数错误:{}", message, e);
        CommonResult<Object> fail = I18nCommonResultUtil.fail(ResultStatusEnum.SERVER_ERROR, message);
        return fail;
    }

    @ExceptionHandler(ConsumerException.class)
    @ResponseBody
    public CommonResult consumerExceptionHandler(ConsumerException e) {
        log.warn("消费方异常:{}", e.getMessage(), e);
        CommonResult<Object> fail = I18nCommonResultUtil.fail(ResultStatusEnum.SERVER_ERROR, e.getMessage(), e.getErrorCode().getMessage());
        return fail;
    }

    @ExceptionHandler(BizException.class)
    @ResponseBody
    public CommonResult bizExceptionHandler(BizException e) {
        log.warn("预期的业务异常:{}", e.getMessage(), e);
        CommonResult<Object> fail = I18nCommonResultUtil.fail(ResultStatusEnum.SERVER_ERROR, e.getMessage(), e.getErrorCode().getMessage());
        return fail;
    }

    @ExceptionHandler(ProviderException.class)
    @ResponseBody
    public CommonResult providerExceptionHandler(ProviderException e) {
        log.error("系统错误:{}", e.getMessage(), e);
        CommonResult<Object> fail = I18nCommonResultUtil.fail(ResultStatusEnum.SERVER_ERROR, e.getMessage(), e.getErrorCode().getMessage());
        return fail;
    }

    @ExceptionHandler(Exception.class)
    @ResponseBody
    public CommonResult exceptionHandler(Exception e) {
        log.error("未知系统错误:{}", e.getMessage(), e);
        CommonResult<Object> fail = I18nCommonResultUtil.fail(ResultStatusEnum.SERVER_ERROR, e.getMessage());
        return fail;
    }
    /**
     * provider处理异常
     * @param throwable
     * @param joinPoint
     * @return
     */
    @Override
    public DubboResponse processError(Throwable throwable, ProceedingJoinPoint joinPoint) {
        if (throwable instanceof ParamsException) {
            ParamsException exception = (ParamsException)throwable;
            log.warn("调用方参数异常, 异常信息:{}", throwable.getMessage(), throwable);
            return I18nDubboResponseUtil.getError(exception.getErrorCode().getCode(), exception.getMessage(),null);
        } else if (throwable instanceof BizException) {
            BizException exception = (BizException)throwable;
            log.warn("调用方异常, 异常信息:{}", throwable.getMessage(), throwable);
            return I18nDubboResponseUtil.getError(exception.getErrorCode().getCode(), exception.getMessage(),null);
        } else if (throwable instanceof CallerException) {
            CallerException exception = (CallerException)throwable;
            log.warn("调用方异常, 异常信息:{}", throwable.getMessage(), throwable);
            return I18nDubboResponseUtil.getError(exception.getErrorCode().getCode(), exception.getMessage(),null);
        } else if (throwable instanceof ProviderException) {
            ProviderException exception = (ProviderException)throwable;
            log.error("提供方异常, 异常信息:{}", throwable.getMessage(), throwable);
            return I18nDubboResponseUtil.getError(exception.getErrorCode().getCode(), exception.getMessage(),null);
        } else {
            log.error("提供方未知异常, 异常信息:{}", throwable.getMessage(), throwable);
            ProviderErrorCode providerErrorCode = new ProviderErrorCode(DubboCommonConstant.UNDEFINED_EXCEPTION_CODE);
            return I18nDubboResponseUtil.getError(providerErrorCode.getCode(), throwable.getMessage(),null);
        }
    }
}
