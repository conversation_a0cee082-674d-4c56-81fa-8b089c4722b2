package com.cosfo.message;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.spring.context.annotation.config.NacosPropertySource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.DubboComponentScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

@NacosPropertySource(dataId = "${spring.application.name}", type = ConfigType.PROPERTIES, autoRefreshed = true)
@Slf4j
@SpringBootApplication
//@EnableAsync
@DubboComponentScan("com.cosfo.message.**.provider")
@ComponentScan(value = {"com.cosfo.message.*"})
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
        log.info("#############服务启动成功##############");
    }
}
