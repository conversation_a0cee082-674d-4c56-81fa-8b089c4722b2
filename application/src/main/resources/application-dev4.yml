server:
  port: 80
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.jdbc.Driver
    url: ********************************************************************************
    username: test
    password: xianmu619
  schedulerx2:
    endpoint: XXXX
    namespace: XXXX
    groupId: XXXX
    appKey: XXXX
dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    address: nacos://********:11000
    parameters:
      namespace: cfccb911-1306-4ea7-8a9f-18436f9dd245
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  provider:
    version: 1.0.0
    group: online
    timeout: 6000
    retries: 0
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    check: false

redis:
  host: **************
  password: xianmu619
  port: 6379
  timeout: 6000
  database: 4
rocketmq:
  consumer:
    access-key: ''
    secret-key: ''
  name-server: ************:9879
  producer:
    access-key: ''
    group: GID_manage
    secret-key: ''
    sendMsgTimeout: 10000
es:
  port: 80
  url: dev.es.summerfarm.net
  user-name: elastic
  user-pwd: Xianmu619
nacos:
  config:
    server-addr: test-nacos.summerfarm.net:11000
    namespace: b4bafec8-bee6-4ec0-a276-1cda3ad832a8