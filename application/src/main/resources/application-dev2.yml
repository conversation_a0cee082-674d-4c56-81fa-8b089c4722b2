server:
  port: 80
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.jdbc.Driver
    url: ***********************************************************************************************
    username: test
    password: xianmu619
  schedulerx2:
    endpoint: XXXX
    namespace: XXXX
    groupId: XXXX
    appKey: XXXX
dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    address: nacos://********:11000
    parameters:
      namespace: a9f94e14-0f25-4567-a038-b32e83829046
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  provider:
    version: 1.0.0
    group: online
    timeout: 6000
    retries: 0
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    check: false


redis:
  host: **************
  password: xianmu619
  port: 6379
  timeout: 6000
  database: 1
rocketmq:
  consumer:
    access-key: ''
    secret-key: ''
  name-server: *************:9876
  producer:
    access-key: ''
    group: GID_manage
    secret-key: ''
    sendMsgTimeout: 10000
es:
  port: 80
  url: dev.es.summerfarm.net
  user-name: elastic
  user-pwd: Xianmu619
nacos:
  config:
    server-addr: test-nacos.summerfarm.net:11000
    namespace: 800ed4d6-a4fd-4345-86dd-8a4cc70c9bda