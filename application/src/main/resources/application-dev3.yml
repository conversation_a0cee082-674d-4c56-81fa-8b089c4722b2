server:
  port: 80
# 数据库配置
spring:
  application:
    name: message-center
  datasource:
    hikari:
      minimum-idle: 3
      maximum-pool-size: 10
      max-lifetime: 30000   #不能小于30秒，否则默认回到1800秒
      connection-test-query: SELECT 1
    username: test
    password: xianmu619
    #?serverTimezone=UTC解决时区的报错
    url: *******************************************************************************************************************
    # mysql5 的驱动是 com.mysql.jdbc.Driver, mysql8的驱动是 com.mysql.cj.jdbc.Driver
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 自定义数据源
    type: com.alibaba.druid.pool.DruidDataSource
    #druid 数据源专有配置
    initialSize: 5
    minIdle: 5
    maxActive: 80
    maxWait: 60000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 300000
  # redis配置
  redis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 0
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 5 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
  schedulerx2:
    appKey: p8ReNDkzW3s8/bSTVmUMkQ==
    endpoint: acm.aliyun.com
    groupId: message-center
    namespace: 0fba89cd-351e-4e9f-86dc-4b4fbc06170e
rocketmq:
  name-server: test-mq-nameserver.summerfarm.net:9876
  producer:
    enable-msg-trace: off
    group: GID_cosfo_dts
    send-message-timeout: 10000
    access-key:
    secret-key:
  consumer:
    access-key:
    secret-key:
# 日志文件路径
log-path: ${APP_LOG_DIR:./log}
logging:
  level:
    root: info
    org.springframework: INFO
    org.mybatis: INFO
    com.cosfo.message: INFO
  pattern:
    console: "%d - %msg%n"

dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    address: nacos://test-nacos.summerfarm.net:11000
    parameters:
      namespace: c26bc4c2-bd51-4aae-a170-1f04b9c52987
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    timeout: 10000
    check: false
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  provider:
    version: 1.0.0
    group: online
    timeout: 6000
    retries: 0
    telnet: ls,ps,cd,pwd,trace,count,invoke,select,status,log,help,clear,exit,shutdown
wechat:
  templet:
    sendEvn: trial
notice:
  sceneId: 1

xm:
  log:
    enable: true
    resp: true
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
nacos:
  config:
    server-addr: test-nacos.summerfarm.net:11000
    namespace: bd543515-d448-423f-baec-ec069c924c12