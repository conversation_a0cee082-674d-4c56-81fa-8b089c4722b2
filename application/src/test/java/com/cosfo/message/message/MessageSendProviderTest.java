package com.cosfo.message.message;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.message.client.enums.ChannelTypeEnum;
import com.cosfo.message.client.enums.JumpUrlTypeEnum;
import com.cosfo.message.client.enums.MessageContentTypeEnum;
import com.cosfo.message.client.enums.MsgBodyTypeEnum;
import com.cosfo.message.client.enums.TemplateWechatEnum;
import com.cosfo.message.client.provider.MessageSendProvider;
import com.cosfo.message.client.provider.MessageSendProviderV2;
import com.cosfo.message.client.req.MessageBodyReq;
import com.cosfo.message.client.req.MessageUserReq;
import com.cosfo.message.client.resp.MsgSendLogResp;
import com.cosfo.message.common.util.FeishuUtils;
import com.cosfo.message.infrastructure.template.dto.WechatOaSendMessageDTO;
import com.cosfo.message.web.domain.dto.SendMessageDTO;
import com.cosfo.message.web.domain.dto.WechatOaSendLogReqDTO;
import com.cosfo.message.web.facade.AuthUserAuthFacade;
import com.cosfo.message.web.mq.consumer.SendThirdMessageListener;
import com.google.common.collect.Lists;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.authentication.client.input.user.AuthUserAuthQueryInput;
import net.xianmu.authentication.client.resp.AuthUserAuthResp;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.enums.base.auth.WxOfficialAccountsChannelEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.NamedThreadFactory;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.BrokenBarrierException;
import java.util.concurrent.CyclicBarrier;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: fansongsong
 * @Date: 2023-07-17
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class MessageSendProviderTest {

    @Resource
    MessageSendProvider messageSendProvider;
    @Resource
    SendThirdMessageListener sendThirdMessageListener;

    @Resource
    MessageSendProviderV2 messageSendProviderV2;

    @Resource
    private AuthUserAuthFacade wechatFacade;

    private static final Integer DEFAULT_NUM = 15;
    ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(DEFAULT_NUM, DEFAULT_NUM, 10, TimeUnit.MINUTES, new LinkedBlockingQueue(100), new NamedThreadFactory("StoreBalance-thread"));

    @org.junit.Test
    public void isJSONValid() {
        String body = "{\"code\":0,\"data\":{\"body\":{\"content\":\"{\\\"title\\\":null,\\\"elements\\\":[[{\\\"tag\\\":\\\"text\\\",\\\"text\\\":\\\"【成本波动】\\\\n规格：有籽青柠檬3斤*1包/普通/标准规格在嘉兴总仓成本波动24.00%\\\\nSKU：16738467013\\\\n最新批次号：2023073175038028\\\\n批次成本：10.31\\\\n库存成本：8.33（2023-07-31）\\\\n当前售价：11.00\\\"}]]}\"},\"chat_id\":\"oc_ad62c8eb1a706cc5df04a7c06a4239c8\",\"create_time\":\"1690786630291\",\"deleted\":false,\"message_id\":\"om_81e9d88c6fc69f866d713acb7cc46df0\",\"msg_type\":\"interactive\",\"sender\":{\"id\":\"cli_a4296303f724100d\",\"id_type\":\"app_id\",\"sender_type\":\"app\",\"tenant_key\":\"17f4494b0dd7d75d\"},\"update_time\":\"1690786630291\",\"updated\":false},\"msg\":\"success\"}";
        if (!FeishuUtils.isJSONValid(body)) {
            throw new BizException("发送飞书单聊消息失败,响应内容:" + body);
        }

        body = "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n" +
                "\n" +
                "rpcId: 0.1.1.41.3\n" +
                "\n" +
                "stackFirstLine: <html>\n" +
                "\n" +
                "stacktrace: <html>\n" +
                "<head><title>504 Gateway Time-out</title></head>\n" +
                "<body>\n" +
                "<h1>504 Gateway Time-out</h1>\n" +
                "<p>The gateway did not receive a timely response from the upstream server or application.<hr/>Powered by Tengine</body>\n" +
                "</html>";
        if (!FeishuUtils.isJSONValid(body)) {
            throw new BizException("发送飞书单聊消息失败,响应内容:" + body);
        }
    }

    @org.junit.Test
    public void process() throws InterruptedException {
        CyclicBarrier cyclicBarrier = new CyclicBarrier(DEFAULT_NUM);
        for (int i = 0; i < DEFAULT_NUM; i++) {
            threadPoolExecutor.execute(() -> {
                        long startTime = 0;
                        try {
                            cyclicBarrier.await();
                            startTime = System.currentTimeMillis();
                            System.err.println(Thread.currentThread().getName() + "开始发送消息" + startTime);
                            SendMessageDTO sendMessageDTO = SendMessageDTO.builder().msgSendLogId(3646L).build();
                            sendThirdMessageListener.process(sendMessageDTO);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        } catch (BrokenBarrierException e) {
                            e.printStackTrace();
                        } finally {
                            long end = System.currentTimeMillis();
                            System.err.println(Thread.currentThread().getName() + "结束发送消息" + (end - startTime));
                        }

                    }

            );
        }
        Thread.sleep(15000);
    }

    @org.junit.Test
    public void batchSendMessage() {
        Long tenantId = 1L;
        MessageUserReq sender = new MessageUserReq();
        sender.setSystemOriginEnum(SystemOriginEnum.ADMIN);
        // 段总手机号
        sender.setPhone("***********");
        List<MessageUserReq> receiverList = Lists.newArrayList();
        MessageUserReq receiver = new MessageUserReq();
        receiver.setSystemOriginEnum(SystemOriginEnum.ADMIN);
        receiver.setPhone("***********");
        receiverList.add(receiver);
        MessageBodyReq mesg = new MessageBodyReq();
        mesg.setContentType(MessageContentTypeEnum.NORMAL.getType());
        mesg.setMsgBodyType(MsgBodyTypeEnum.TEXT.getType());
        mesg.setData("{\"text\":\"test content\"}");


        DubboResponse<List<MsgSendLogResp>> listDubboResponse = messageSendProvider.batchSendMessage(tenantId, ChannelTypeEnum.FEISHU_SYSTEM, sender, receiverList, mesg);
        System.err.println(JSON.toJSONString(listDubboResponse));
    }

    @org.junit.Test
    public void sendMessage() {
        Long tenantId = 1L;
        MessageUserReq sender = new MessageUserReq();
        sender.setSystemOriginEnum(SystemOriginEnum.ADMIN);
        // 段总手机号
        sender.setPhone("13071250438");
        List<MessageUserReq> receiverList = Lists.newArrayList();
        MessageUserReq receiver = new MessageUserReq();
        receiver.setSystemOriginEnum(SystemOriginEnum.ADMIN);
        receiver.setPhone("***********");
        receiverList.add(receiver);
        MessageBodyReq mesg = new MessageBodyReq();
        mesg.setContentType(MessageContentTypeEnum.NORMAL.getType());
        mesg.setMsgBodyType(MsgBodyTypeEnum.TEXT.getType());
        mesg.setData("{\"text\":\"test content\"}");


        DubboResponse<MsgSendLogResp> listDubboResponse = messageSendProvider.sendMessage(tenantId, ChannelTypeEnum.FEISHU_SYSTEM, sender, receiver, mesg);
        System.err.println(JSON.toJSONString(listDubboResponse));
    }

    @org.junit.Test
    public void batchSendMessageByThridUid() {
        Long tenantId = 1L;
        MessageBodyReq mesg = new MessageBodyReq();
        mesg.setContentType(MessageContentTypeEnum.NORMAL.getType());
        mesg.setMsgBodyType(MsgBodyTypeEnum.IMAGE.getType());
        mesg.setData("{\"image_key\": \"img_7ea74629-9191-4176-998c-2e603c9c5e8g\"}");


        DubboResponse<List<MsgSendLogResp>> listDubboResponse = messageSendProvider.batchSendMessageByThridUid(tenantId, ChannelTypeEnum.FEISHU_SYSTEM,
                "d288da61", Lists.newArrayList("XM00979"), mesg, SystemOriginEnum.ADMIN);
        System.err.println(JSON.toJSONString(listDubboResponse));
    }

    @org.junit.Test
    public void sendMessageByThridUid() {
        Long tenantId = 1L;
        MessageBodyReq mesg = new MessageBodyReq();
        mesg.setContentType(MessageContentTypeEnum.NORMAL.getType());
        mesg.setMsgBodyType(MsgBodyTypeEnum.IMAGE.getType());
        mesg.setData("{\"image_key\": \"img_7ea74629-9191-4176-998c-2e603c9c5e8g\"}");


        DubboResponse<MsgSendLogResp> listDubboResponse = messageSendProvider.sendMessageByThridUid(tenantId, ChannelTypeEnum.FEISHU_SYSTEM,
                "d288da61", "XM00979", mesg, SystemOriginEnum.ADMIN);
        System.err.println(JSON.toJSONString(listDubboResponse));
    }

    @org.junit.Test
    public void batchSendMessageV2() {
        Long tenantId = 1L;
        MessageUserReq sender = new MessageUserReq();
        sender.setSystemOriginEnum(SystemOriginEnum.ADMIN);
        // 段总手机号
        sender.setPhone("***********");
        List<MessageUserReq> receiverList = Lists.newArrayList();
        MessageUserReq receiver = new MessageUserReq();
        receiver.setSystemOriginEnum(SystemOriginEnum.ADMIN);
        receiver.setPhone("***********");
        receiverList.add(receiver);
        MessageBodyReq mesg = new MessageBodyReq();
        mesg.setContentType(MessageContentTypeEnum.NORMAL.getType());
        mesg.setMsgBodyType(MsgBodyTypeEnum.TEXT.getType());
        mesg.setData("{\"text\":\"test content\"}");


        DubboResponse<List<MsgSendLogResp>> listDubboResponse = messageSendProviderV2.batchSendMessage(tenantId, ChannelTypeEnum.FEISHU_SYSTEM, null, sender, receiverList, mesg);
        System.err.println(JSON.toJSONString(listDubboResponse));
    }

    @org.junit.Test
    public void batchSendMessageByThridUidV2() {
        Long tenantId = 1L;
        MessageBodyReq mesg = new MessageBodyReq();
        mesg.setContentType(MessageContentTypeEnum.NORMAL.getType());
        mesg.setMsgBodyType(MsgBodyTypeEnum.IMAGE.getType());
        mesg.setData("{\"image_key\": \"img_7ea74629-9191-4176-998c-2e603c9c5e8g\"}");


        DubboResponse<List<MsgSendLogResp>> listDubboResponse = messageSendProviderV2.batchSendMessageByThridUid(tenantId, ChannelTypeEnum.FEISHU_SYSTEM,
                null, "d288da61", Lists.newArrayList("XM00979"), mesg, SystemOriginEnum.ADMIN);
        System.err.println(JSON.toJSONString(listDubboResponse));
    }



    @org.junit.Test
    public void batchSendMessageV2ForWechatOa() {
        String tenantName = "测试商城";

        String accessToken = wechatFacade.queryWeChatToken(WxOfficialAccountsChannelEnum.FTGYL.channelCode);
        System.err.println("token:" + accessToken);
//        accessToken = wechatFacade.queryWeChatToken(WxOfficialAccountsChannelEnum.FTGYL.channelCode);
//        System.err.println("token:" + accessToken);
//        accessToken = wechatFacade.queryWeChatToken(WxOfficialAccountsChannelEnum.FTGYL.channelCode);
//        System.err.println("token:" + accessToken);
//        accessToken = wechatFacade.queryWeChatToken(WxOfficialAccountsChannelEnum.FTGYL.channelCode);
//        System.err.println("token:" + accessToken);



        Long tenantId = 2L;
        // 组装参数，调用消息中心发送消息
        AuthUserAuthQueryInput authUserAuthQueryInput = new AuthUserAuthQueryInput();
        authUserAuthQueryInput.setSystemOriginEnum(net.xianmu.authentication.client.input.SystemOriginEnum.COSFO_MANAGE);
        authUserAuthQueryInput.setPhones(Lists.newArrayList("***********"));
        authUserAuthQueryInput.setPageNum(1);
        authUserAuthQueryInput.setAuthType(AuthTypeEnum.OFFICIAL_WE_CHAT);
        authUserAuthQueryInput.setTenantId(tenantId);
        authUserAuthQueryInput.setPageSize(authUserAuthQueryInput.getPhones().size());
        authUserAuthQueryInput.setChannelCode(WxOfficialAccountsChannelEnum.FTGYL.channelCode);
        List<AuthUserAuthResp> authUserAuthResps = wechatFacade.queryUserRespByPhones(authUserAuthQueryInput);
        Map<String, String> phoneOpenIdMap = authUserAuthResps.stream().collect(Collectors.toMap(AuthUserAuthResp::getPhone, AuthUserAuthResp::getAuthId));



        List<MessageBodyReq> reqList = Lists.newArrayList();
        MessageBodyReq messageBodyReq = getMessageBodyReq();
        // 模版1
        String keyword = TemplateWechatEnum.TemplateCode.SAAS_PAY_SUCCESS_SUPPLIER_ORDER_CODE.getKeyword();
        String data = String.format(keyword, "201.00", "2023-10-20 00:00:00");
        messageBodyReq.setData(data);
//        messageBodyReq.setClientMsgId("124435");
        reqList.add(messageBodyReq);
//        // 模版2
//        keyword = TemplateWechatEnum.TemplateCode.SAAS_ORDER_SUPPLIER_REFUND_CODE.getKeyword();
//        data = String.format(keyword, "202.00", "2023-10-20 00:00:00", "OR123456");
//        MessageBodyReq messageBodyReq2 = getMessageBodyReq();
//        messageBodyReq2.setData(data);
//        messageBodyReq2.setTemplateCode(TemplateWechatEnum.TemplateCode.SAAS_ORDER_SUPPLIER_REFUND_CODE.getCode());
//        reqList.add(messageBodyReq2);
//        // 模版3
//        keyword = TemplateWechatEnum.TemplateCode.SAAS_ORDER_SUPPLIER_TOTAL_CODE.getKeyword();
//        data = String.format(keyword, "3", "203.00", tenantName);
//        MessageBodyReq messageBodyReq3 = getMessageBodyReq();
//        messageBodyReq3.setData(data);
//        messageBodyReq3.setTemplateCode(TemplateWechatEnum.TemplateCode.SAAS_ORDER_SUPPLIER_TOTAL_CODE.getCode());
//        reqList.add(messageBodyReq3);

        for (MessageBodyReq bodyReq : reqList) {
            DubboResponse<List<MsgSendLogResp>> listDubboResponse = messageSendProviderV2.batchSendMessageByThridUid(tenantId, ChannelTypeEnum.WECHAT_OFFICIAL_ACCOUNT, WxOfficialAccountsChannelEnum.FTGYL.channelCode, null, Lists.newArrayList("o3geR6u8JxUHNtq8nSF5LRU0qHww"), bodyReq, null);
            System.err.println(JSON.toJSONString(listDubboResponse));
        }

    }



    @org.junit.Test
    public void batchSendMessageV2ForWechatCustomByUid() {
        Long tenantId = 2L;
        // 组装参数，调用消息中心发送消息
        AuthUserAuthQueryInput authUserAuthQueryInput = new AuthUserAuthQueryInput();
        authUserAuthQueryInput.setSystemOriginEnum(net.xianmu.authentication.client.input.SystemOriginEnum.COSFO_MANAGE);
        authUserAuthQueryInput.setPhones(Lists.newArrayList("***********"));
        authUserAuthQueryInput.setPageNum(1);
        authUserAuthQueryInput.setAuthType(AuthTypeEnum.OFFICIAL_WE_CHAT);
        authUserAuthQueryInput.setTenantId(tenantId);
        authUserAuthQueryInput.setPageSize(authUserAuthQueryInput.getPhones().size());
        authUserAuthQueryInput.setChannelCode(WxOfficialAccountsChannelEnum.FTGYL.channelCode);
        List<AuthUserAuthResp> authUserAuthResps = wechatFacade.queryUserRespByPhones(authUserAuthQueryInput);
        Map<String, String> phoneOpenIdMap = authUserAuthResps.stream().collect(Collectors.toMap(AuthUserAuthResp::getPhone, AuthUserAuthResp::getAuthId));

        MessageBodyReq messageBodyReq = getMessageBodyReq();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("content","测试一下");
        messageBodyReq.setData(jsonObject.toJSONString());
        messageBodyReq.setMsgBodyType(MsgBodyTypeEnum.TEXT.getType());
        messageBodyReq.setContentType(MessageContentTypeEnum.CUSTOM_MESSAGE.getType());
        DubboResponse<List<MsgSendLogResp>> listDubboResponse = messageSendProviderV2.batchSendMessageByThridUid(tenantId, ChannelTypeEnum.WECHAT_OFFICIAL_ACCOUNT, WxOfficialAccountsChannelEnum.FTGYL.channelCode, null, Lists.newArrayList("o3geR6u8JxUHNtq8nSF5LRU0qHww"), messageBodyReq, null);
        System.err.println(JSON.toJSONString(listDubboResponse));
    }


    @org.junit.Test
    public void batchSendMessageV2ForWechatCustom() {
        Long tenantId = 2L;

        MessageUserReq messageUserReq = new MessageUserReq();
        messageUserReq.setSystemOriginEnum(SystemOriginEnum.COSFO_MANAGE);
        messageUserReq.setPhone("***********");

        MessageBodyReq messageBodyReq = getMessageBodyReq();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("content", "测试一下");
        messageBodyReq.setData(jsonObject.toJSONString());
        messageBodyReq.setMsgBodyType(MsgBodyTypeEnum.TEXT.getType());
        messageBodyReq.setContentType(MessageContentTypeEnum.CUSTOM_MESSAGE.getType());
        DubboResponse<List<MsgSendLogResp>> listDubboResponse = messageSendProviderV2.batchSendMessage(tenantId, ChannelTypeEnum.WECHAT_OFFICIAL_ACCOUNT, WxOfficialAccountsChannelEnum.FTGYL.channelCode, null, Lists.newArrayList(messageUserReq), messageBodyReq);
        System.err.println(JSON.toJSONString(listDubboResponse));
    }

    private MessageBodyReq getMessageBodyReq() {
        MessageBodyReq messageBodyReq = new MessageBodyReq();
        messageBodyReq.setContentType(MessageContentTypeEnum.NORMAL.getType());
        messageBodyReq.setTemplateCode(TemplateWechatEnum.TemplateCode.SAAS_PAY_SUCCESS_SUPPLIER_ORDER_CODE.getCode());
        messageBodyReq.setJumpUrlTypeEnum(JumpUrlTypeEnum.PAGE);
        messageBodyReq.setJumpUrl("https://manage.cosfo.cn/index.html#/login");
        return messageBodyReq;
    }
}
