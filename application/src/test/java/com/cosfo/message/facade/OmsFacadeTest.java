package com.cosfo.message.facade;

import com.alibaba.fastjson.JSON;
import com.cosfo.message.web.domain.service.UserCenterService;
import com.cosfo.message.web.domain.vo.MerchantStoreAccountVO;
import com.cosfo.message.web.domain.vo.MerchantStoreVO;
import com.cosfo.message.web.facade.AuthUserAuthFacade;
import com.cosfo.message.web.facade.OmsOldFacade;
import com.google.common.collect.Lists;
import net.xianmu.common.enums.base.auth.WxOfficialAccountsChannelEnum;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-08-04
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class OmsFacadeTest {

    @Autowired
    UserCenterService userCenterFacade;

    @Autowired
    OmsOldFacade omsOldFacade;
    @Autowired
    AuthUserAuthFacade authUserAuthFacade;

    @org.junit.Test
    public void getStoreByIds() {
        List<Long> storeIds = Lists.newArrayList(4122L, 141957L);
        List<MerchantStoreVO> newData = userCenterFacade.getStoreByIds(storeIds);
        List<MerchantStoreVO> oldData = omsOldFacade.getStoreByIds(storeIds);
        String newStr = JSON.toJSONString(newData);
        String oldStr = JSON.toJSONString(oldData);
        System.err.println(newStr.equals(oldStr));
    }


    @org.junit.Test
    public void getStoreAccountIdsByStoreId() {
        Long storeId = 4122L;
        List<MerchantStoreAccountVO> newData = userCenterFacade.getStoreAccountIdsByStoreId(storeId, null, 1);
        List<MerchantStoreAccountVO> oldData = omsOldFacade.getStoreAccountIdsByStoreId(storeId, null, 1);
        String newStr = JSON.toJSONString(newData);
        String oldStr = JSON.toJSONString(oldData);
        System.err.println(newStr.equals(oldStr));
    }


    @org.junit.Test
    public void getStoreAccountIdsByTenantId() {
        Long tenantId = 2L;
        List<MerchantStoreAccountVO> newData = userCenterFacade.getStoreAccountIdsByTenantId(tenantId, null, 1);
        List<MerchantStoreAccountVO> oldData = omsOldFacade.getStoreAccountIdsByTenantId(tenantId, null, 1);
        String newStr = JSON.toJSONString(newData);
        String oldStr = JSON.toJSONString(oldData);
        System.err.println(newStr.equals(oldStr));
    }

    @org.junit.Test
    public void queryWeChatToken() {
        String accessToken = authUserAuthFacade.queryWeChatToken(WxOfficialAccountsChannelEnum.FTGYL.channelCode);
        System.err.println(accessToken);
        accessToken = authUserAuthFacade.queryWeChatToken(WxOfficialAccountsChannelEnum.XMSRM.channelCode);
        System.err.println(accessToken);
    }

}
