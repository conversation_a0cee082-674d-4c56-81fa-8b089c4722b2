package com.cosfo.message;

import com.alibaba.fastjson.JSON;
import com.cosfo.message.client.common.page.req.PageQueryReq;
import com.cosfo.message.client.common.page.resp.PageResp;
import com.cosfo.message.client.enums.KeyCodeEnum;
import com.cosfo.message.client.enums.MessageContentTypeEnum;
import com.cosfo.message.client.enums.ReadStatusEnum;
import com.cosfo.message.client.provider.MessageSendLogProvider;
import com.cosfo.message.client.req.BatchMarkMsgReq;
import com.cosfo.message.client.req.MsgSendLogQueryReq;
import com.cosfo.message.client.req.NotifyMessageReq;
import com.cosfo.message.client.req.NotifyTipBodyReq;
import com.cosfo.message.client.req.TenantNotifyReq;
import com.cosfo.message.client.resp.MsgNotifySendLogResp;
import com.google.common.collect.Lists;
import net.xianmu.common.result.DubboResponse;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-07-19
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class SendLogTest {

    @Resource
    private MessageSendLogProvider sendLogProvider;

    @Test
    public void page4Manage(){
        MsgSendLogQueryReq messageTipPageDTO = new MsgSendLogQueryReq();
        messageTipPageDTO.setReadStatusEnum(ReadStatusEnum.UN_READ);
        messageTipPageDTO.setTenantId(2L);
        PageQueryReq pageQueryReq = new PageQueryReq();
        pageQueryReq.setPageIndex(1);
        pageQueryReq.setPageSize(10);
        DubboResponse<PageResp<MsgNotifySendLogResp>> response = sendLogProvider.page4Manage(messageTipPageDTO,pageQueryReq);
        System.err.println(JSON.toJSON(response));

        messageTipPageDTO.setStartTime(LocalDateTime.now().minusMonths(1));
        messageTipPageDTO.setEndTime(LocalDateTime.now());
        messageTipPageDTO.setReadStatusEnum(ReadStatusEnum.READ);
        response = sendLogProvider.page4Manage(messageTipPageDTO,pageQueryReq);
        System.err.println(JSON.toJSON(response));

        messageTipPageDTO.setReadStatusEnum(null);
        response = sendLogProvider.page4Manage(messageTipPageDTO,pageQueryReq);
        System.err.println(JSON.toJSON(response));
    }

    @Test
    public void countByCondition(){
        MsgSendLogQueryReq messageTipPageDTO = new MsgSendLogQueryReq();
        messageTipPageDTO.setReadStatusEnum(ReadStatusEnum.UN_READ);
        messageTipPageDTO.setTenantId(2L);

        DubboResponse<Integer> response = sendLogProvider.countByCondition(messageTipPageDTO);
        System.err.println(response);
    }

//    @Test
//    public void markBatch(){
//        BatchMarkMsgReq msgTipMarkDTO = new BatchMarkMsgReq();
//        msgTipMarkDTO.setIds(Lists.newArrayList(3635L));
//        msgTipMarkDTO.setReadStatusEnum(ReadStatusEnum.READ);
//        msgTipMarkDTO.setTenantId(2L);
//        sendLogProvider.markBatch(msgTipMarkDTO);
//        System.err.println();
//    }
//
//    @Test
//    public void createNotifyMessage() {
//        NotifyMessageReq notifyMessageReq = new NotifyMessageReq();
//        notifyMessageReq.setTenantId(2L);
//        notifyMessageReq.setPageId(72460L);
//        notifyMessageReq.setTitle("退款失败");
//        notifyMessageReq.setSubTitle("因退款账户资金不足，退款失败");
//        notifyMessageReq.setMessageContentTypeEnum(MessageContentTypeEnum.AFTER_TENANT_NOTIFY);
//
//        List<NotifyTipBodyReq> list = Lists.newArrayList();
//        NotifyTipBodyReq notifyTipBodyReq = new NotifyTipBodyReq();
//        notifyTipBodyReq.setKeyCode(KeyCodeEnum.AFTER_SALE_NO.getCode());
//        notifyTipBodyReq.setKeyValue(Lists.newArrayList("*********************"));
//        list.add(notifyTipBodyReq);
//        notifyTipBodyReq = new NotifyTipBodyReq();
//        notifyTipBodyReq.setKeyCode(KeyCodeEnum.REFUND_PRICE.getCode());
//        notifyTipBodyReq.setKeyValue(Lists.newArrayList("0.01"));
//
//        list.add(notifyTipBodyReq);
//        notifyTipBodyReq = new NotifyTipBodyReq();
//        notifyTipBodyReq.setKeyCode(KeyCodeEnum.UNDERFUNDED_ACCOUNT.getCode());
//        notifyTipBodyReq.setKeyValue(Lists.newArrayList("杭州鲜沐-********（汇付商户号）", "杭州客私服-********（汇付商户号）"));
//        list.add(notifyTipBodyReq);
//        notifyMessageReq.setDetailList(list);
//
//        notifyMessageReq.setUniqueId("*********************");
//        DubboResponse<Boolean> notifyMessage = sendLogProvider.createNotifyMessage(notifyMessageReq);
//        System.err.println(notifyMessage);
//    }
//
//    @Test
//    public void alertMessage() {
//        TenantNotifyReq notifyMessageReq = new TenantNotifyReq();
//        notifyMessageReq.setTenantId(2L);
//        DubboResponse<List<MsgNotifySendLogResp>> list = sendLogProvider.queryAlertNotifyMessage(notifyMessageReq);
//        System.err.println(list);
//    }
}
