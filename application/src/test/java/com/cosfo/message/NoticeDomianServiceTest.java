package com.cosfo.message;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cosfo.message.client.common.page.req.PageQueryReq;
import com.cosfo.message.client.req.MsgNoticeReceiverQueryReq;
import com.cosfo.message.client.req.NoticeQuery4ManagerReq;
import com.cosfo.message.common.constants.WechatTemplateConstants;
import com.cosfo.message.common.enums.MsgNoticeEnum;
import com.cosfo.message.common.enums.MsgReadLogEnum;
import com.cosfo.message.common.enums.MsgSendLogEnum;
import com.cosfo.message.infrastructure.base.dto.PageVO;
import com.cosfo.message.infrastructure.message.dto.MsgSendLogQueryDTO;
import com.cosfo.message.infrastructure.message.dto.WechatSendReqDTO;
import com.cosfo.message.infrastructure.notice.dao.MsgNoticeReceiverMappingDao;
import com.cosfo.message.infrastructure.notice.dto.*;
import com.cosfo.message.infrastructure.notice.model.MsgNoticeReceiverMapping;
import com.cosfo.message.web.domain.service.MessageDomainService;
import com.cosfo.message.web.domain.service.NoticeDomianService;
import com.cosfo.message.web.domain.vo.*;
import com.cosfo.message.infrastructure.base.dto.PageQueryDTO;
import com.cosfo.message.infrastructure.notice.dao.MsgNoticeDao;
import com.cosfo.message.infrastructure.notice.dao.MsgReadLogDao;
import com.cosfo.message.infrastructure.notice.model.MsgNotice;
import com.cosfo.message.infrastructure.notice.model.MsgReadLog;
import com.cosfo.message.web.domain.converter.NoticeConverter;
import com.cosfo.message.web.facade.OmsFacade;
import com.cosfo.message.web.provider.NoticeProviderImpl;
import com.cosfo.message.web.scheduler.SendNoticeProcessor;
import com.cosfo.message.web.scheduler.TestSendNoticeProcessor;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import org.apache.http.entity.ContentType;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
@SpringBootTest
public class NoticeDomianServiceTest {
    @Resource
    private MsgReadLogDao readLogDao;
    @Resource
    private MsgNoticeDao noticeDao;
    @Resource
    private MsgNoticeReceiverMappingDao noticeReceiverMappingDao;
    @Resource
    private MessageDomainService messageDomainService;
    @Resource
    private SendNoticeProcessor sendNoticeProcessor;
    @Resource
    private NoticeDomianService noticeDomianService;
    @Resource
    private NoticeProviderImpl pageReceiver;
    @Autowired
    private OmsFacade omsFacade;
    @Autowired
    private TestSendNoticeProcessor testSendNoticeProcessor;
    /**
     * 公告处理记录 - 点赞；取消点赞；
     */
    @Test
    public void sendNoticeProcessor() {
        try {
            testSendNoticeProcessor.processResult (null);
        } catch (Exception e) {
            throw new RuntimeException (e);
        }
//        PageQueryReq pageQueryReq  =new PageQueryReq();
//        pageQueryReq.setPageSize(10);
//        pageQueryReq.setPageIndex(1);
////
//        MsgNoticeReceiverQueryReq req = new MsgNoticeReceiverQueryReq();
//        req.setNoticeId(152L);
//        req.setSupportFlag(true);
//        req.setReceiverIds(Collections.singletonList(3382L));
//        pageReceiver.pageReceiver(req,pageQueryReq);



//        NoticeQuery4ManagerReq queryDTO =new NoticeQuery4ManagerReq();
//        queryDTO.setPushStatus(1);
//        queryDTO.setSortBy(0);
//        queryDTO.setSortKey("id");
//        queryDTO.setTenantId(2L);
//        queryDTO.setPushTimeBegin(LocalDateTime.parse("2023-03-01 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
//        queryDTO.setPushTimeEnd(LocalDateTime.parse("2023-03-01 23:59:59", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
//        PageQueryDTO pageQueryDTO  =new PageQueryDTO();
//        pageQueryDTO.setPageSize(10);
//        pageQueryDTO.setPageIndex(1);
//        pageReceiver.page4Manager(queryDTO,pageQueryReq);

//    omsFacade.getStoreAccountIdsByStoreId(2663L, null, 1);





        try {
            sendNoticeProcessor.processResult(null);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    /**
     * 公告处理记录 - 点赞；取消点赞；
     */
    @Test
    public void supportMsgReadLog() {
        MsgReadLogDTO dto = new MsgReadLogDTO();
        Long uId = 1L;
        Long tenantId = 2L;
        Long noticeId = 19L;

        if (ObjectUtil.isNull(uId)){
            throw new ParamsException("用户id不能为空");
        }
        MsgNotice notice = noticeDao.getById(noticeId);
        if (ObjectUtil.isNull(notice)){
            throw new BizException("公告不存在");
        }
        if(Objects.equals(MsgNoticeEnum.SupportSwitch.CLOST.getValue(),notice.getSupportSwitch())){
            throw new BizException("公告未开启点赞功能");
        }

        //查询该用户对公告的浏览/点赞记录
        List<MsgReadLog> logs = readLogDao.listByUIdAndNoticId(uId,noticeId,tenantId);
        if(Objects.equals(MsgReadLogEnum.ActionType.CANCEL_SUPPORT.getValue(), dto.getActionType())){
            List<Long> logIds = logs.stream().filter(e->Objects.equals(MsgReadLogEnum.ActionType.SUPPORT.getValue(),e.getActionType())).map(MsgReadLog::getId).collect(Collectors.toList());
            readLogDao.removeByIds(logIds);
        }else{
            //如果已经点赞过，就不重复点赞了。
            if(!logs.stream().anyMatch(e->Objects.equals(MsgReadLogEnum.ActionType.SUPPORT.getValue(),e.getActionType()))) {
                MsgReadLog readLog = NoticeConverter.msgReadLogDTO2Entity(dto);
                readLogDao.save(readLog);
            }
        }
        messageDomainService.readed(uId,noticeId, 2L);
    }

    @Test
    public void getNoticeById() {
        MsgReadLogDTO dto = new MsgReadLogDTO();
        Boolean needSaveLog =false;

        Long uId = 2L;
        Long tenantId = 2L;
        Long noticeId = 11L;

        if (ObjectUtil.isNull(uId)){
            throw new ParamsException("用户id不能为空");
        }
        MsgNotice notice = noticeDao.getById(noticeId);
        if (ObjectUtil.isNull(notice)){
            throw new BizException("公告不存在");
        }

        //需要保存浏览记录
        if(needSaveLog){
            MsgReadLog readLog = NoticeConverter.msgReadLogDTO2Entity(dto);
            readLogDao.save(readLog);
            messageDomainService.readed(uId,noticeId, 2L);
        }
        //查询用户收到的公告前后记录
        Long beforeNoticeId = ObjectUtil.isNotNull(uId)?messageDomainService.getBeforeNoticeId(uId, noticeId, tenantId):null;
        Long afterNoticeId = ObjectUtil.isNotNull(uId)?messageDomainService.getAfterNoticeId(uId, noticeId, tenantId):null;

        //查询该公告的阅读数和点赞数
        Map<Long, Long> supportAmountMap = readLogDao.countByNoticIdsAndActionType(Collections.singletonList(noticeId), MsgReadLogEnum.ActionType.SUPPORT.getValue());
        Integer supportAmount = CollectionUtil.isEmpty(supportAmountMap)?0:supportAmountMap.get(noticeId).intValue();
        Map<Long, Long> readAmounMap = readLogDao.countByNoticIdsAndActionType(Collections.singletonList(noticeId), MsgReadLogEnum.ActionType.READ.getValue());
        Integer readAmoun = CollectionUtil.isEmpty(readAmounMap)?0:readAmounMap.get(noticeId).intValue();

        List<Long> receiverIds = noticeReceiverMappingDao.listByNoticeIds(Collections.singletonList(noticeId)).stream().map(MsgNoticeReceiverMapping::getReceiverId).collect(Collectors.toList());

        List<MsgReadLog> logs = readLogDao.listByUIdAndNoticId(uId,noticeId,tenantId);
        boolean supportStatus = logs.stream().filter(e -> Objects.equals(MsgReadLogEnum.ActionType.SUPPORT.getValue(), e.getActionType())).findAny().isPresent();

        System.out.println(NoticeConverter.buildNoticeDetailVO(notice,beforeNoticeId,afterNoticeId,supportAmount,readAmoun,receiverIds,supportStatus));
    }
    @Test
    public void readed() {
        messageDomainService.readed(2655L, 51L, 2L);
    }
    @Test
    public void page4Receiver() {
        PageQueryDTO pageQueryDTO = new PageQueryDTO();
        pageQueryDTO.setPageSize(20);
        pageQueryDTO.setPageIndex(1);
        NoticeQuery4ReceiverDTO req =new NoticeQuery4ReceiverDTO();

        req.setRecevieUid(2655L);
        req.setTenantId(2L);

        noticeDomianService.page4Receiver(req,pageQueryDTO);

    }
    @Test
    public void page4Manager() {
        NoticeQuery4ManagerDTO queryDTO =  new NoticeQuery4ManagerDTO();
        queryDTO.setSortKey("id");
        queryDTO.setSortBy(0);
        queryDTO.setTenantId(2L);
//        queryDTO.setId();
//        queryDTO.setTitle();
//        queryDTO.setPushTimeBegin();
//        queryDTO.setPushTimeEnd();
        queryDTO.setPushStatus(2);
        PageQueryDTO pageQueryDTO = new PageQueryDTO();
        pageQueryDTO.setPageSize(20);
        pageQueryDTO.setPageIndex(1);
        IPage<MsgNotice> page = noticeDao.page(queryDTO,pageQueryDTO);
        List<MsgNotice> records = page.getRecords();
        if(CollectionUtil.isEmpty(records)){
             PageVO.getNullResult(pageQueryDTO.getPageIndex(),pageQueryDTO.getPageSize());
             return;
        }
        List<Long> noticeIds = records.stream().map(MsgNotice::getId).collect(Collectors.toList());
        Map<Long, Long> supportAmountMap = readLogDao.countByNoticIdsAndActionType(noticeIds, MsgReadLogEnum.ActionType.SUPPORT.getValue());
        Map<Long, Long> readAmounMap = readLogDao.countByNoticIdsAndActionType(noticeIds, MsgReadLogEnum.ActionType.READ.getValue());
        List<MsgNoticeReceiverMapping> msgNoticeReceiverMappings = noticeReceiverMappingDao.listByNoticeIds(noticeIds);
        Map<Long, List<MsgNoticeReceiverMapping>> storeAmountMap = msgNoticeReceiverMappings.stream().collect(Collectors.groupingBy(MsgNoticeReceiverMapping::getNoticeId));

        System.out.println(PageVO.page2VO(page,e->NoticeConverter.buildNoticList4ManagerVO(e,supportAmountMap,readAmounMap,storeAmountMap)));
    }
    @Test
    public void deleteNotic() {
        Long id = 0L;
        MsgNotice notice = noticeDao.getById(id);
        if (ObjectUtil.isNull(notice)){
            throw new BizException("公告不存在");
        }
        noticeDao.removeById(id);
        noticeReceiverMappingDao.removeByNoticId(id);
    }
    @Test
    public void editNotic() {
        MsgNoticeEditDTO dto = new MsgNoticeEditDTO();
        dto.setSupportSwitch(1);
        dto.setPushType(1);
        dto.setPushStatus(0);
        dto.setTenantId(2L);
        dto.setUId(1L);
        dto.setTitle("测试1");
        dto.setContent("[{\"type\":1,\"content\":\"************\"},{\"type\":1,\"content\":\"23423423432432432\"}]");
        List<Long> storeIds = dto.getStoreIds();
        if(ObjectUtil.isNotNull(dto.getPushStatus()) && MsgNoticeEnum.PushStatus.READY.getValue().equals(dto.getPushStatus())) {
            if(CollectionUtil.isEmpty(storeIds)){
                throw new BizException("请选择门店");
            }
        }
        if(ObjectUtil.isNotNull(dto.getId())) {
            MsgNotice noticeDB = noticeDao.getById(dto.getId());
            if (ObjectUtil.isNull(noticeDB)){
                throw new BizException("公告不存在");
            }
            if (MsgNoticeEnum.PushStatus.PUSHED.getValue().equals(noticeDB.getPushStatus())){
                throw new BizException("公告已发布不允许修改");
            }
            noticeReceiverMappingDao.removeByNoticId(dto.getId());
        }

        MsgNotice notice = NoticeConverter.msgNoticeDTO2Entity(dto);
        noticeDao.saveOrUpdate(notice);

        if(CollectionUtil.isEmpty(storeIds)){
            List<MsgNoticeReceiverMapping> mappingList = storeIds.stream().map(e-> NoticeConverter.msgNoticeReceiverMapping2Entity(e,dto.getReceiverType(),notice.getId())).collect(Collectors.toList());
            noticeReceiverMappingDao.saveBatch(mappingList);
        }
    }

    @Test
    public void pageReceiver() {
        MsgNoticeReceiverQueryDTO queryDTO = new MsgNoticeReceiverQueryDTO();
        queryDTO.setNoticeId(32L);
        PageQueryDTO pageQueryDTO = new PageQueryDTO();
        pageQueryDTO.setPageSize(20);
        pageQueryDTO.setPageIndex(1);

        Boolean supportFlag = queryDTO.getSupportFlag();
        Map<Long, List<MsgReadLog>> supportStoreMap;
        if(ObjectUtil.isNotEmpty(supportFlag)){
            List<MsgReadLog> supportLogs = readLogDao.listByNoticIdsAndActionType(Collections.singletonList(queryDTO.getNoticeId()), MsgReadLogEnum.ActionType.SUPPORT.getValue()).get(queryDTO.getNoticeId());
             supportStoreMap = supportLogs.stream().collect(Collectors.groupingBy(MsgReadLog::getStoreId));
            if(CollectionUtil.isNotEmpty(supportLogs)){
                Set<Long> supportStoreIds = supportLogs.stream().map(MsgReadLog::getStoreId).collect(Collectors.toSet());
                if(supportFlag){
                    //如果查询已经点赞的,则和传入的storeid取交集
                    List<Long> receiverIds = queryDTO.getReceiverIds();
                    if(CollectionUtil.isEmpty(receiverIds)){
                        queryDTO.setReceiverIds(new ArrayList<>(supportStoreIds));
                    }else{
                        HashSet<Long> resSet = new HashSet<>();
                        resSet.addAll(receiverIds);
                        resSet.retainAll(receiverIds);
                        queryDTO.setReceiverIds(new ArrayList<>(resSet));
                    }
                    if(CollectionUtil.isEmpty(queryDTO.getReceiverIds())){
                         PageVO.getNullResult(pageQueryDTO.getPageIndex(),pageQueryDTO.getPageSize());
                         return;
                    }
                }else{
                    queryDTO.setNotInReceiverIds(supportStoreIds);
                }
            }
        }else{
            supportStoreMap = Collections.emptyMap();
        }

        Boolean readFlag = queryDTO.getReadFlag();
        Map<Long, List<MsgReadLog>> readStoreMap;
        if(ObjectUtil.isNotEmpty(readFlag)){
            List<MsgReadLog> readLogs  = readLogDao.listByNoticIdsAndActionType(Collections.singletonList(queryDTO.getNoticeId()), MsgReadLogEnum.ActionType.READ.getValue()).get(queryDTO.getNoticeId());
            readStoreMap = readLogs.stream().collect(Collectors.groupingBy(MsgReadLog::getStoreId));

            if(CollectionUtil.isNotEmpty(readLogs)){
                Set<Long> readStoreIds = readLogs.stream().map(MsgReadLog::getStoreId).collect(Collectors.toSet());
                if(readFlag){
                    //如果查询已经点赞的,则和传入的storeid取交集
                    List<Long> receiverIds = queryDTO.getReceiverIds();
                    if(CollectionUtil.isEmpty(receiverIds)){
                        queryDTO.setReceiverIds(new ArrayList<>(readStoreIds));
                    }else{
                        HashSet<Long> resSet = new HashSet<>();
                        resSet.addAll(receiverIds);
                        resSet.retainAll(receiverIds);
                        queryDTO.setReceiverIds(new ArrayList<>(resSet));
                    }
                    if(CollectionUtil.isEmpty(queryDTO.getReceiverIds())){
                         PageVO.getNullResult(pageQueryDTO.getPageIndex(),pageQueryDTO.getPageSize());
                        return;
                    }
                }else{
                    queryDTO.setNotInReceiverIds(readStoreIds);
                }
            }
        }else{
            readStoreMap = Collections.emptyMap();
        }
        IPage<MsgNoticeReceiverMapping> page = noticeReceiverMappingDao.page(queryDTO,pageQueryDTO);
        List<MsgNoticeReceiverMapping> records = page.getRecords();
        if(CollectionUtil.isEmpty(records)){
             PageVO.getNullResult(pageQueryDTO.getPageIndex(),pageQueryDTO.getPageSize());
            return;
        }
        System.out.println( PageVO.page2VO(page,e->NoticeConverter.buildMsgNoticeReceiverVO(e,readStoreMap.get(e.getReceiverId()),supportStoreMap.get(e.getReceiverId()))));
    }

    @Test
    public void sendWechat() {
        WechatSendReqDTO wechatSendReqDTO = new WechatSendReqDTO();
        wechatSendReqDTO.setTouser("ooWYY4_tN0oVxXu87TDBvafOFCtE");
        wechatSendReqDTO.setTemplate_id("-ZeeYyw3Vs42o-UM5DRe2EbOlfKvBU8lp7CS0ZAWT2w");
        wechatSendReqDTO.setPage("pages/notice-details/index?id=32");
        wechatSendReqDTO.setMiniprogram_state("developer");
        wechatSendReqDTO.setLang("zh_CN");
//        wechatSendReqDTO.setData(JSONObject.parseObject(dto.getData(), Map.class));
        Map<String,String> v1 = new HashMap<>();
        v1.put("value","111111");
        Map<String,String> v2 = new HashMap<>();
        v2.put("value","ceshi");
        Map<String,String> v3 = new HashMap<>();
        v3.put("value","15821761778");
        Map<String, Map<String,String>> map = new HashMap<>();
        map.put("thing1",v1);
        map.put("name2",v2);
        map.put("phone_number3",v3);

        wechatSendReqDTO.setData(map);
        String req = JSONObject.toJSONString(wechatSendReqDTO);
        HttpResponse response = HttpUtil.createPost(WechatTemplateConstants.sendMsg + "?access_token=" + "66_GZQ1EAAiSKKm7BAJMr1YmsknXPCm4tXRcmCctLwrb75LfVcDs0IoiI3u_89TuZizSMljP8yte3Tg28gVx3o2ohCc6R1VRxx7RMqSIFZDY3zU9R-BZ006ZDpQMFHTh1-uKMTm6zW_ICGyTCqhWRIgAEDLGX")
                .contentType(ContentType.APPLICATION_JSON.toString())
                .body(req).timeout (3000).execute();
        WechatResponseVO responseVO = JSONObject.parseObject(response.body(), WechatResponseVO.class);
        System.out.println(responseVO);
    }
}
