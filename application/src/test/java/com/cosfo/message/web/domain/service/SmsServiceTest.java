package com.cosfo.message.web.domain.service;

import com.cosfo.mall.common.context.SMSTypeEnum;
import com.cosfo.mall.common.sms.model.Sms;
import com.cosfo.message.common.enums.SmsProviderEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SmsService 单元测试
 * <AUTHOR>
 * @date 2025-08-14
 */
@ExtendWith(MockitoExtension.class)
class SmsServiceTest {

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private RedisTemplate<String, String> redisTemplate;

    @Mock
    private ValueOperations<String, String> valueOperations;

    @Mock
    private ChuangLanSmsSender chuangLanSmsSender;

    @Mock
    private AliSender aliSender;

    @InjectMocks
    private SmsService smsService;

    private Sms testSms;

    @BeforeEach
    void setUp() {
        // 初始化测试短信对象
        testSms = new Sms();
        testSms.setPhone("13800138000");
        testSms.setContent("测试短信内容");
        testSms.setType(SMSTypeEnum.NOTIFY);
        testSms.setSceneId(1L);
        testSms.setArgs(Arrays.asList("参数1", "参数2"));

        // Mock Redis操作
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
    }

    @Test
    void testSendSmsWithSpecifiedProvider_ChuangLan_Success() {
        // 准备测试数据
        String providerCode = "CHUANGLAN";
        
        // Mock Spring容器返回发送器实例
        when(applicationContext.getBean(ChuangLanSmsSender.class)).thenReturn(chuangLanSmsSender);
        when(chuangLanSmsSender.sendSms(testSms)).thenReturn(true);

        // 执行测试
        boolean result = smsService.sendSms(testSms, providerCode);

        // 验证结果
        assertTrue(result);
        verify(chuangLanSmsSender, times(1)).sendSms(testSms);
        verify(applicationContext, times(1)).getBean(ChuangLanSmsSender.class);
    }

    @Test
    void testSendSmsWithSpecifiedProvider_Ali_Success() {
        // 准备测试数据
        String providerCode = "ALI";
        
        // Mock Spring容器返回发送器实例
        when(applicationContext.getBean(AliSender.class)).thenReturn(aliSender);
        when(aliSender.sendSms(testSms)).thenReturn(true);

        // 执行测试
        boolean result = smsService.sendSms(testSms, providerCode);

        // 验证结果
        assertTrue(result);
        verify(aliSender, times(1)).sendSms(testSms);
        verify(applicationContext, times(1)).getBean(AliSender.class);
    }

    @Test
    void testSendSmsWithInvalidProvider() {
        // 准备测试数据
        String invalidProviderCode = "INVALID";

        // 执行测试
        boolean result = smsService.sendSms(testSms, invalidProviderCode);

        // 验证结果
        assertFalse(result);
        verify(chuangLanSmsSender, never()).sendSms(any());
        verify(aliSender, never()).sendSms(any());
    }

    @Test
    void testSendSmsWithRotation_FirstCall() {
        // Mock Redis返回null（第一次调用）
        when(valueOperations.get("sms:provider:rotation")).thenReturn(null);
        when(applicationContext.getBean(ChuangLanSmsSender.class)).thenReturn(chuangLanSmsSender);
        when(chuangLanSmsSender.sendSms(testSms)).thenReturn(true);

        // 执行测试
        boolean result = smsService.sendSms(testSms);

        // 验证结果
        assertTrue(result);
        verify(chuangLanSmsSender, times(1)).sendSms(testSms);
        verify(valueOperations, times(1)).set(eq("sms:provider:rotation"), eq("ALI"), eq(3600L), eq(TimeUnit.SECONDS));
    }

    @Test
    void testSendSmsWithRotation_SecondCall() {
        // Mock Redis返回ALI（上次使用了ALI）
        when(valueOperations.get("sms:provider:rotation")).thenReturn("ALI");
        when(applicationContext.getBean(ChuangLanSmsSender.class)).thenReturn(chuangLanSmsSender);
        when(chuangLanSmsSender.sendSms(testSms)).thenReturn(true);

        // 执行测试
        boolean result = smsService.sendSms(testSms);

        // 验证结果
        assertTrue(result);
        verify(chuangLanSmsSender, times(1)).sendSms(testSms);
        verify(valueOperations, times(1)).set(eq("sms:provider:rotation"), eq("CHUANGLAN"), eq(3600L), eq(TimeUnit.SECONDS));
    }

    @Test
    void testGetCurrentProvider() {
        // Mock Redis返回当前提供商
        when(valueOperations.get("sms:provider:rotation")).thenReturn("CHUANGLAN");

        // 执行测试
        SmsProviderEnum currentProvider = smsService.getCurrentProvider();

        // 验证结果
        assertNotNull(currentProvider);
        assertEquals(SmsProviderEnum.CHUANGLAN, currentProvider);
    }

    @Test
    void testGetCurrentProvider_NotSet() {
        // Mock Redis返回null
        when(valueOperations.get("sms:provider:rotation")).thenReturn(null);

        // 执行测试
        SmsProviderEnum currentProvider = smsService.getCurrentProvider();

        // 验证结果
        assertNull(currentProvider);
    }

    @Test
    void testResetRotation() {
        // 执行测试
        smsService.resetRotation();

        // 验证结果
        verify(redisTemplate, times(1)).delete("sms:provider:rotation");
    }

    @Test
    void testSetCurrentProvider_Success() {
        // 执行测试
        boolean result = smsService.setCurrentProvider("ALI");

        // 验证结果
        assertTrue(result);
        verify(valueOperations, times(1)).set(eq("sms:provider:rotation"), eq("ALI"), eq(3600L), eq(TimeUnit.SECONDS));
    }

    @Test
    void testSetCurrentProvider_InvalidCode() {
        // 执行测试
        boolean result = smsService.setCurrentProvider("INVALID");

        // 验证结果
        assertFalse(result);
        verify(valueOperations, never()).set(anyString(), anyString(), anyLong(), any(TimeUnit.class));
    }

    @Test
    void testSendSmsWithRotation_RedisException() {
        // Mock Redis抛出异常
        when(valueOperations.get("sms:provider:rotation")).thenThrow(new RuntimeException("Redis连接失败"));
        when(applicationContext.getBean(ChuangLanSmsSender.class)).thenReturn(chuangLanSmsSender);
        when(chuangLanSmsSender.sendSms(testSms)).thenReturn(true);

        // 执行测试
        boolean result = smsService.sendSms(testSms);

        // 验证结果 - 应该使用默认的第一个提供商
        assertTrue(result);
        verify(chuangLanSmsSender, times(1)).sendSms(testSms);
    }

    @Test
    void testSendSmsWithRotation_SenderException() {
        // Mock正常的轮询逻辑
        when(valueOperations.get("sms:provider:rotation")).thenReturn(null);
        when(applicationContext.getBean(ChuangLanSmsSender.class)).thenReturn(chuangLanSmsSender);
        when(chuangLanSmsSender.sendSms(testSms)).thenReturn(false);

        // 执行测试
        boolean result = smsService.sendSms(testSms);

        // 验证结果
        assertFalse(result);
        verify(chuangLanSmsSender, times(1)).sendSms(testSms);
    }
}
